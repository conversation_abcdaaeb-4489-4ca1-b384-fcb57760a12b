import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { ThemeSwitch } from '@/components/theme-switch'
import { Card, CardContent } from '@/components/ui/card'
import { toast } from '@/hooks/use-toast'
import { useAuthStore } from '@/stores/authStore'
import { Copy } from 'lucide-react'
import { BASE_URL } from '@/utils/network'

const forms = [
  { title: 'License Letter Request', publicUrl: `${BASE_URL}/license-letter-request` },
  { title: 'Visa Letter Request', publicUrl: `${BASE_URL}/visa-letter-request` },
  { title: 'License Application', publicUrl: `${BASE_URL}/license-application` },
]

export default function Home() {
  const userId = useAuthStore((state) => state.auth.user?._id)

  const handleCopy = (url: string) => {
    navigator.clipboard.writeText(url)
     toast({
          title: 'Copied!!!',
          variant: 'success',
        })
  }
 

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        {/* <TopNav links={topNav} /> */}
        <div className='ml-auto flex items-center space-x-4'>
          {/* <Search /> */}
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>
      {/* ===== Main ===== */}
      <Main>
        <div className='mb-2 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight lg:text-[28px]'>
          Welcome to our all-in-one Direct customer hub.
          </h1>
          <div className='flex items-center space-x-2 lg:relative sm:fixed lg:w-auto sm:space-x-2 fixed bottom-0 right-0 p-3 bg-background w-full text-right flex justify-end z-10 sm:z-auto sm:bg-transparent sm:p-0'></div>
        </div>

        <Card className='mt-6'>
          <CardContent>
      <div className="p-0">
        <p className="mb-6">
          Simply select the form you need, copy the link, and send it to the customer directly. Feel free to complete any form you'd like.
        </p>
        <ul className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {forms.map((form) => {
            const urlWithId = userId ? `${form.publicUrl}?id=${userId}` : form.publicUrl
            return (
              <li key={urlWithId} className="border rounded-xl border-gray-200 w-full">
                <button className="w-full text-left flex justify-between items-center rounded-xl py-2 px-4">
                  <span className="text-lg font-medium">{form.title}</span>
                </button>
                <div className="mt-0 flex flex-col sm:flex-row items-end sm:items-center justify-between px-4 pb-4">
                  <span className="text-sm text-gray-600 break-all py-2 px-3 rounded-xl mb-2 sm:mb-0 bg-green-50 dark:bg-gray-800 dark:text-gray-400 w-full sm:w-auto">{urlWithId}</span>
                  <button
                    onClick={() => handleCopy(urlWithId)}
                    className="flex items-center text-green-600 text-sm font-medium hover:underline ml-0 sm:ml-2 mt-1 sm:mt-0"
                  >
                    <Copy className="w-4 h-4 mr-1" /> Copy Link
                  </button>
                </div>
              </li>
            )
          })}
        </ul>
      </div>
      </CardContent>
    </Card>
    </Main>
    </>
  )
}