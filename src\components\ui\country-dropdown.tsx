"use client";
import React, { useCallback, useState, forwardRef } from "react";

// shadcn
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

// utils
import { cn } from "@/lib/utils";

// assets
import { ChevronDown, CheckIcon, Globe } from "lucide-react";
import { CircleFlag } from "react-circle-flags";

// data
import { countries } from "country-data-list";

// Country interface
export interface Country {
  alpha2: string;
  alpha3: string;
  countryCallingCodes: string[];
  currencies: string[];
  emoji?: string;
  ioc: string;
  languages: string[];
  name: string;
  status: string;
}

// Props for single-select dropdown
type SingleSelectProps = {
  options?: Country[];
  multiple?: false;
  defaultValue?: string;
  onChange?: (country: Country) => void;
  disabled?: boolean;
  placeholder?: string;
  slim?: boolean;
};

// Props for multi-select dropdown
type MultiSelectProps = {
  options?: Country[];
  multiple: true;
  defaultValue?: string[];
  onChange?: (countries: Country[]) => void;
  disabled?: boolean;
  placeholder?: string;
  slim?: boolean;
};

export type CountryDropdownProps = SingleSelectProps | MultiSelectProps;

const CountryDropdownComponent = (
  props: CountryDropdownProps,
  ref: React.ForwardedRef<HTMLButtonElement>
) => {
  // Destructure props and separate triggerProps
  const {
    options = countries.all.filter(
      (country: Country) =>
        country.emoji &&
        country.status !== "deleted" &&
        country.ioc !== "PRK" &&
        country.name !== "Ceuta" &&
        country.name !== "Melilla"
    ),
    defaultValue,
    disabled = false,
    placeholder = "Select a country",
    slim = false,
    multiple = false,
    onChange,
    ...triggerProps
  } = props;

  const [open, setOpen] = useState(false);
  // No initial selection; show placeholder until user selects
  const [selectedCountry, setSelectedCountry] = useState<Country | undefined>(undefined);
  const [selectedCountries, setSelectedCountries] = useState<Country[]>([]);

  const handleSelect = useCallback(
    (country: Country) => {
      if (multiple) {
        setSelectedCountries(prev => {
          const exists = prev.find(c => c.alpha3 === country.alpha3);
          const newSelection = exists
            ? prev.filter(c => c.alpha3 !== country.alpha3)
            : [...prev, country];
          (onChange as (countries: Country[]) => void)?.(newSelection);
          return newSelection;
        });
      } else {
        setSelectedCountry(country);
        (onChange as (country: Country) => void)?.(country);
        setOpen(false);
      }
    },
    [onChange, multiple]
  );

  const triggerClasses = cn(
    "flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
    slim === true && "w-20"
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger
        ref={ref}
        className={triggerClasses}
        disabled={disabled}
        {...(triggerProps as React.ButtonHTMLAttributes<HTMLButtonElement>)}
      >
        {multiple ? (
          selectedCountries.length > 0 ? (
            <span>{slim ? `${selectedCountries.length} selected` : selectedCountries.map(c => c.name).join(', ')}</span>
          ) : (
            <span>{placeholder}</span>
          )
        ) : selectedCountry ? (
          <div className="flex items-center flex-grow w-0 gap-2 overflow-hidden">
            <div className="inline-flex items-center justify-center w-5 h-5 shrink-0 overflow-hidden rounded-full">
              <CircleFlag
                countryCode={selectedCountry.alpha2.toLowerCase()}
                height={20}
              />
            </div>
            {slim === false && (
              <span className="overflow-hidden text-ellipsis whitespace-nowrap">
                {selectedCountry.name}
              </span>
            )}
          </div>
        ) : (
          <span>
            {slim === false ? (
              placeholder || setSelectedCountry.name
            ) : (
              <Globe size={20} />
            )}
          </span>
        )}
        <ChevronDown size={16} />
      </PopoverTrigger>
      <PopoverContent
        collisionPadding={10}
        side="bottom"
        className="min-w-[--radix-popper-anchor-width] p-0"
        usePortal={false} // Set usePortal to false for DateTimePicker inside Dialog
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        <Command className="w-full max-h-[200px] sm:max-h-[270px]">
          <CommandList>
            <div className="sticky top-0 z-10 bg-popover">
              <CommandInput placeholder="Search country..." />
            </div>
            <CommandEmpty>No country found.</CommandEmpty>
            <CommandGroup>
              {options
                .filter((x) => x.name)
                .map((option, key: number) => (
                  <CommandItem
                    className="flex items-center w-full gap-2"
                    key={key}
                    onSelect={() => handleSelect(option)}
                  >
                    <div className="flex flex-grow w-0 space-x-2 overflow-hidden">
                      <div className="inline-flex items-center justify-center w-5 h-5 shrink-0 overflow-hidden rounded-full">
                        <CircleFlag
                          countryCode={option.alpha2.toLowerCase()}
                          height={20}
                        />
                      </div>
                      <span className="overflow-hidden text-ellipsis whitespace-nowrap">
                        {option.name}
                      </span>
                    </div>
                    <CheckIcon
                      className={cn(
                        "ml-auto h-4 w-4 shrink-0",
                        multiple
                          ? selectedCountries.some(c => c.alpha3 === option.alpha3)
                            ? "opacity-100"
                            : "opacity-0"
                          : option.name === selectedCountry?.name
                          ? "opacity-100"
                          : "opacity-0"
                      )}
                    />
                  </CommandItem>
                ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

CountryDropdownComponent.displayName = "CountryDropdownComponent";

export const CountryDropdown = forwardRef<HTMLButtonElement, CountryDropdownProps>(
  CountryDropdownComponent
);