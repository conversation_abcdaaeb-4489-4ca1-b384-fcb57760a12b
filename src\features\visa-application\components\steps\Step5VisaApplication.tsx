import React from 'react'
import { useFormContext, useWatch } from 'react-hook-form'
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { ApplicationFormValues } from '../../types/application-form-types'

interface Step5FormProps {
  basicSalary: number
  setBasicSalary: React.Dispatch<React.SetStateAction<number>>
  transportationAllowance: number
  setTransportationAllowance: React.Dispatch<React.SetStateAction<number>>
  accommodationAllowance: number
  setAccommodationAllowance: React.Dispatch<React.SetStateAction<number>>
  otherAllowance: number
  setOtherAllowance: React.Dispatch<React.SetStateAction<number>>
  calculateTotalSalary: () => number
}

const Step5VisaApplication: React.FC<Step5FormProps> = ({
  basicSalary,
  setBasicSalary,
  transportationAllowance,
  setTransportationAllowance,
  accommodationAllowance,
  setAccommodationAllowance,
  otherAllowance,
  setOtherAllowance,
  calculateTotalSalary,
}) => {
  const form = useFormContext<ApplicationFormValues>()
  const visaType = useWatch({ control: form.control, name: 'visaType' })

  return (
    <>
    <form className="space-y-4 fz-form">
      {visaType === 'Employment Visa Renewal' && (
        <>
          <FormField
            control={form.control}
            name='salaryChange'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Do you want to change the salary details of the
                  Visa holder ?
                </FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Choose' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='Yes'>Yes</SelectItem>
                      <SelectItem value='No'>No</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </>
      )}
      <FormField
        control={form.control}
        name='basicSalary'
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              Basic Salary (AED){' '}
              <span className='text-red-500'>*</span>
            </FormLabel>
            <FormControl>
              <div className='flex items-center border rounded-md'>
                <Input
                  {...field}
                  placeholder='Enter'
                  value={field.value || basicSalary}
                  onChange={(e) => {
                    const value = Number(e.target.value)
                    setBasicSalary(value)
                    field.onChange(value)
                  }}
                  min={10}
                  max={9999999}
                />
                <span className='px-3'>AED</span>
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name='transportationAllowance'
        render={({ field }) => (
          <FormItem>
            <FormLabel>Transportation Allowance (AED)</FormLabel>
            <FormControl>
              <div className='flex items-center border rounded-md'>
                <Input
                  {...field}
                  placeholder='Enter'
                  value={field.value || transportationAllowance}
                  onChange={(e) => {
                    const value = Number(e.target.value)
                    setTransportationAllowance(value)
                    field.onChange(value)
                  }}
                />
                <span className='px-3'>AED</span>
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name='accommodationAllowance'
        render={({ field }) => (
          <FormItem>
            <FormLabel>Accommodation Allowance (AED)</FormLabel>
            <FormControl>
              <div className='flex items-center border rounded-md'>
                <Input
                  {...field}
                  placeholder='Enter'
                  value={field.value || accommodationAllowance}
                  onChange={(e) => {
                    const value = Number(e.target.value)
                    setAccommodationAllowance(value)
                    field.onChange(value)
                  }}
                />
                <span className='px-3'>AED</span>
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name='otherAllowance'
        render={({ field }) => (
          <FormItem>
            <FormLabel>Other Allowance (AED)</FormLabel>
            <FormControl>
              <div className='flex items-center border rounded-md'>
                <Input
                  {...field}
                  placeholder='Enter'
                  value={field.value || otherAllowance}
                  onChange={(e) => {
                    const value = Number(e.target.value)
                    setOtherAllowance(value)
                    field.onChange(value)
                  }}
                />
                <span className='px-3'>AED</span>
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name='totalMonthlySalary'
        render={({ field }) => (
          <FormItem>
            <FormLabel>Total Monthly Salary (AED)</FormLabel>
            <FormControl>
              <div className='flex items-center border rounded-md'>
                <Input
                  {...field}
                  value={calculateTotalSalary()}
                  disabled
                />
                <span className='px-3'>AED</span>
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </form>
    </>
  )
}

export default Step5VisaApplication