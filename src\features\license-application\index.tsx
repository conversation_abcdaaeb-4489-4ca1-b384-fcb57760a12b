import React, { useState, useEffect } from 'react'
import { number, z } from 'zod'
import { useForm } from 'react-hook-form'
import { UseFormReturn } from 'react-hook-form'
import { FieldErrors } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { PlusIcon, FilePenLine, Lightbulb, Loader2 } from 'lucide-react'
import { TrashIcon } from 'lucide-react'
import PhoneInput from 'react-phone-input-2'
import { toast } from '@/hooks/use-toast'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { CountryDropdown } from '@/components/ui/country-dropdown'
import { DateTimePicker } from '@/components/ui/datetime-picker'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import ApplicationProgress from '@/components/application-progress'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
// import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import AddUBO from './addUBO'
import CompanyMembers from './company-members'
import type { Member } from './company-members'
import { activitiesData } from './Business_Acttivites'
import { mainpulateLicenseMembersData, createLicenseApplicationMembers, uploadMemberFiles } from '@/services/licenseapplication'
import { mainpulateLicenseApplicationData, createLicenseApplication } from '@/services/licenseapplication'


const applicationFormSchema = z.object({
  applicationDate: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine((date) => !isNaN(date.getTime()), {
      message: 'Invalid date format',
    }),
  applicationType: z.string().min(1, { message: 'Select a choice.' }),
  cem: z.string(),
  emailAddress: z.string().email({ message: 'Invalid email address.' }),
  phone: z.string().optional(),
  // partnerName: z.string().min(2, { message: 'Enter a value for this field.' }),
  // partnerId:z.string().min(2, { message: 'Enter a value for this field.' }),
  // partnerEmail :  z.preprocess(
  //       (val) => (typeof val === 'string' && val.trim() === '' ? undefined : val),
  //       z.string().email('Invalid email').optional()
  //     ),
  //   partnerContactEmail: z
  //       .string()
  //       .email({ message: 'Invalid email address.' }),

  quotationValue: z.string().min(1, { message: 'Select a choice.' }),
  grossNet: z.string().min(1, { message: 'Select a choice.' }),
  commissionPlan: z.string().min(1, { message: 'Select a choice.' }),
  paymentType: z.string().min(1, { message: 'Select a choice.' }),
  newActivity: z.string().min(1, { message: 'Select a choice.' }),

  additionalActivity: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  companyStatement: z.boolean().refine((val) => val === true, {
    message: 'You must accept the statement.',
  }),
  countryOfOperation: z.array(z.string()),
  operateAsFranchise: z.string().optional(),
  tradeNameOfFranchise: z.string().optional(),
  option1English: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  option1Arabic: z.string().optional(),
  option2English: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  option2Arabic: z.string().optional(),
  option3English: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  option3Arabic: z.string().optional(),
  agreementTerms: z.boolean().refine((val) => val === true, {
    message: 'You must accept the terms and conditions.',
  }),
  branchName: z.string().min(2, { message: 'Enter a value for this field.' }),
  branchNameArabic: z.string().optional(),
  countryOfRegistration: z.string().optional(),

  addressOfParentCompany: z.string().optional(),
  financialYearStartDate: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .optional(),

  financialYearEndDate: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine((date) => !isNaN(date.getTime()), {
      message: 'Invalid date format',
    }),
  licenseType: z.string().optional(),
  tradeLicenseValidity: z.string().min(1, { message: 'Select a choice.' }),
  visaPackage: z.string().min(1, { message: 'Select a choice.' }),
  investorOrPartnerVisa: z.string().min(1, { message: 'Select a choice.' }),
  numberOfPartnerVisas: z
    .string()
    .min(1, { message: 'Enter a value for this field.' }),

  includeVisaCostInQuote: z.string().min(1, { message: 'Select a choice.' }),
  numberOfVisasInQuote: z.string().min(1, { message: 'Select a choice.' }),

  numberOfInsideCountryVisas: z
    .string()
    .min(1, { message: 'Select a choice.' }),

  numberOfOutsideCountryVisas: z
    .string()
    .min(1, { message: 'Select a choice.' }),

  establishmentCard: z.string().min(1, { message: 'Select a choice.' }),

  shareholdingType: z.string().min(1, { message: 'Select a choice.' }),

  numberOfShareholders: z
    .number()
    .min(1, { message: 'Enter a value greater than or equal to 1.' }) // Validate min value
    .max(10, { message: 'Enter a value less than or equal to 10.' }), // Validate max value
  proposedShareCapital: z.number().min(10000, {
    message: 'Enter a value greater than or equal to 10000.',
  }),

  shareValue: z
    .number()
    .min(10, { message: 'Enter a value greater than or equal to 10.' }),

  totalNumberOfShares: z.string().optional(),
  // shareCapitalPerShareholder: z.string().optional(),
  shareholderInIFZA: z.string().min(1, { message: 'Select a choice.' }),
  companyStatementValue: z.string().min(1, { message: 'Select a choice.' }),
  agreement: z.string().optional(),
  declaration: z.string().min(1, { message: 'Select a choice.' }),
  confirmation: z.string().min(1, { message: 'Select a choice.' }),

  authorizedSignatoryEmail: z.union([
    z.string().email(),
    z.literal(''),
    z.undefined(),
  ]),

  authorizedSignatoryName: z.string().optional(),

  // members: z.array(
  //   z.object({
  //   })
  // ),
})

type ApplicationFormValues = z.infer<typeof applicationFormSchema>
export default function LicenseApplication() {
  const [_, setIsFormSubmitted] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  // Initialize members state for CompanyMembers
  const [members, setMembers] = useState<Member[]>([])
  // const [date, setDate] = useState<Date | undefined>(undefined)

  const form = useForm<ApplicationFormValues>({
    resolver: zodResolver(applicationFormSchema),
    defaultValues: {
      applicationDate: new Date(),
      applicationType: '',
      cem: 'RA',
      emailAddress: '',
      phone: '',

      // partnerName: '',
      // partnerId: '',
      // partnerEmail: '',
      // partnerContactEmail: '',
      quotationValue: '',
      grossNet: '',
      commissionPlan: '',
      paymentType: '',
      newActivity: '',
      additionalActivity: '',
      companyStatement: false,
      countryOfOperation: [],
      operateAsFranchise: '',
      tradeNameOfFranchise: '',
      option1English: '',
      option1Arabic: '',
      option2English: '',
      option2Arabic: '',
      option3English: '',
      option3Arabic: '',
      agreementTerms: false,
      branchName: '',
      branchNameArabic: '',
      countryOfRegistration: '',
      addressOfParentCompany: '',
      financialYearStartDate: new Date(),
      financialYearEndDate: new Date(new Date().getFullYear(), 11, 31),
      licenseType: '',
      tradeLicenseValidity: '',
      visaPackage: '',
      investorOrPartnerVisa: '',
      numberOfPartnerVisas: '',
      includeVisaCostInQuote: '',
      numberOfVisasInQuote: '',
      numberOfInsideCountryVisas: '',
      numberOfOutsideCountryVisas: '',
      establishmentCard: '',
      shareholdingType: ' ',
      numberOfShareholders: 1,
      proposedShareCapital: 1,
      shareValue: 1,
      totalNumberOfShares: '',
      // shareCapitalPerShareholder: '',
      shareholderInIFZA: '',
      companyStatementValue: '',
      agreement: '',
      declaration: '',
      confirmation: '',
      authorizedSignatoryEmail: '',
      authorizedSignatoryName: '',

      // members: [], // Empty members array for step 5
    },
    // shouldUnregister: false,
    mode: 'all',
    criteriaMode: 'all',
  })

  const applicationType = form.watch('applicationType')
  const newActivityValue = form.watch('newActivity')
  const [currentStep, setCurrentStep] = React.useState(1) // Track current step

  useEffect(() => {
    const currentValue = form.getValues('financialYearEndDate')

    if (!currentValue) {
      const now = new Date()
      const endOfYear = new Date(now.getFullYear(), 11, 31)

      form.setValue('financialYearEndDate', endOfYear)
    }
  }, [form])

  // Calculate the Total Number of Shares when Proposed Share Capital or Share Value changes
  const calculateTotalNumberOfShares = (form: UseFormReturn<any>) => {
    const proposedShareCapital = form.watch('proposedShareCapital')
    const shareValue = form.watch('shareValue')

    if (proposedShareCapital && shareValue) {
      // Calculate the Total Number of Shares
      const totalNumberOfShares = (proposedShareCapital / shareValue).toFixed(0) // Convert to string

      // Set the calculated value of Total Number of Shares
      form.setValue('totalNumberOfShares', totalNumberOfShares) // Set the value as a string
    }
  }

  // Calculate the Share Capital per Shareholder when Proposed Share Capital or Number of Shareholders changes
  const calculateShareCapitalperShareholder = (form: UseFormReturn<any>) => {
    const proposedShareCapital = form.watch('proposedShareCapital')
    const numberOfShareholders = form.watch('numberOfShareholders')

    if (proposedShareCapital && numberOfShareholders) {
      // Calculate the Share Capital per Shareholder with decimal values
      let shareCapitalPerShareholder = (
        proposedShareCapital / numberOfShareholders
      ).toFixed(2) // Keep 2 decimal places

      // Remove trailing zeros if present
      shareCapitalPerShareholder = parseFloat(
        shareCapitalPerShareholder
      ).toString()

      // Set the calculated value of Share Capital per Shareholder
      form.setValue('shareCapitalPerShareholder', shareCapitalPerShareholder) // Set the value as a string
    }
  }
  const fieldsPerStep: Record<number, (keyof ApplicationFormValues)[]> = {
    1: [
      'applicationDate',
      'applicationType',
      'cem',
      'emailAddress',
      'phone',
      // 'partnerName',
      // 'partnerId',
      // 'partnerEmail',
      // 'partnerContactEmail',
      'quotationValue',
      'grossNet',
      'commissionPlan',
      'paymentType',
      'newActivity',
      'additionalActivity',
      'companyStatement',
      'countryOfOperation',
      'operateAsFranchise',
      'tradeNameOfFranchise',
      'option1English',
      'option1Arabic',
      'option2English',
      'option2Arabic',
      'option3English',
      'option3Arabic',
      'agreementTerms',
      'branchName',
      'branchNameArabic',
      'countryOfRegistration',
      'addressOfParentCompany',
      'financialYearStartDate',
      'financialYearEndDate',
    ], // Fields for step 1

    2: [
      'licenseType',
      'tradeLicenseValidity',
      'visaPackage',
      'investorOrPartnerVisa',
      'numberOfPartnerVisas',
      'includeVisaCostInQuote',
      'numberOfVisasInQuote',
      'numberOfInsideCountryVisas',
      'numberOfOutsideCountryVisas',
      'establishmentCard',
      'shareholdingType',
      'numberOfShareholders',
      'proposedShareCapital',
      'shareValue',
      'totalNumberOfShares',
      // 'shareCapitalPerShareholder',
      'shareholderInIFZA',
    ], // Fields for step 2

    3: [], // Fields for step 3

    4: [
      'companyStatementValue',
      'agreement',
      'declaration',
      'confirmation',
      'authorizedSignatoryName',
      'authorizedSignatoryEmail',
    ], // Fields for step 4
  }
  const [stepValues, setStepValues] = useState<
    Record<number, ApplicationFormValues>
  >({})

  const getStepSchema = (step: number, values: ApplicationFormValues) => {
    switch (step) {
      case 1:
        return z.object({
          applicationDate: applicationFormSchema.shape.applicationDate,
          applicationType: applicationFormSchema.shape.applicationType,
          emailAddress: applicationFormSchema.shape.emailAddress,
          // partnerName: applicationFormSchema.shape.partnerName,
          // partnerId: applicationFormSchema.shape.partnerId,
          // partnerEmail: applicationFormSchema.shape.partnerEmail,
          // partnerContactEmail: applicationFormSchema.shape.partnerContactEmail,
          quotationValue: applicationFormSchema.shape.quotationValue,
          grossNet: applicationFormSchema.shape.grossNet,
          commissionPlan: applicationFormSchema.shape.commissionPlan,
          paymentType: applicationFormSchema.shape.paymentType,
          ...(values.applicationType && {
            newActivity: applicationFormSchema.shape.newActivity,
          }),

          ...(values.newActivity === 'Yes' && {
            additionalActivity: applicationFormSchema.shape.additionalActivity,
          }),
          companyStatement: applicationFormSchema.shape.companyStatement,
          countryOfOperation: applicationFormSchema.shape.countryOfOperation,
          ...(values.applicationType === 'Individual' && {
            operateAsFranchise: applicationFormSchema.shape.operateAsFranchise,
          }),

          ...(values.operateAsFranchise === 'Yes' && {
            tradeNameOfFranchise:
              applicationFormSchema.shape.tradeNameOfFranchise,
          }),

          ...(['Individual', 'Corporate'].includes(values.applicationType) && {
            option1English: applicationFormSchema.shape.option1English,
            option2English: applicationFormSchema.shape.option2English,
            option3English: applicationFormSchema.shape.option3English,
            agreementTerms: applicationFormSchema.shape.agreementTerms,
          }),

          ...(values.applicationType === 'Branch' && {
            branchName: applicationFormSchema.shape.branchName,
            branchNameArabic: applicationFormSchema.shape.branchNameArabic,
            countryOfRegistration:
              applicationFormSchema.shape.countryOfRegistration,
            addressOfParentCompany:
              applicationFormSchema.shape.addressOfParentCompany,
          }),
        })

      case 2:
        return z.object({
          // licenseType: applicationFormSchema.shape.licenseType,
          tradeLicenseValidity:
            applicationFormSchema.shape.tradeLicenseValidity,
          visaPackage: applicationFormSchema.shape.visaPackage,

          ...(values.visaPackage !== '0 Visa' &&
            values.visaPackage !== '' && {
              investorOrPartnerVisa:
                applicationFormSchema.shape.investorOrPartnerVisa,
            }),

          ...(values.investorOrPartnerVisa === 'Partner' && {
            numberOfPartnerVisas:
              applicationFormSchema.shape.numberOfPartnerVisas,
          }),

          ...(values.visaPackage !== '0 Visa' &&
            values.visaPackage !== '' && {
              includeVisaCostInQuote:
                applicationFormSchema.shape.includeVisaCostInQuote,
            }),

          ...(values.includeVisaCostInQuote === 'Yes' && {
            numberOfVisasInQuote:
              applicationFormSchema.shape.numberOfVisasInQuote,
            numberOfInsideCountryVisas:
              applicationFormSchema.shape.numberOfInsideCountryVisas,
            numberOfOutsideCountryVisas:
              applicationFormSchema.shape.numberOfOutsideCountryVisas,
          }),

          ...(values.visaPackage !== '0 Visa' &&
            values.visaPackage !== '' && {
              establishmentCard: applicationFormSchema.shape.establishmentCard,
            }),

          ...(['Individual', 'Corporate'].includes(values.applicationType) && {
            shareholdingType: applicationFormSchema.shape.shareholdingType,
            numberOfShareholders:
              applicationFormSchema.shape.numberOfShareholders,
            proposedShareCapital:
              applicationFormSchema.shape.proposedShareCapital,
            shareValue: applicationFormSchema.shape.shareValue,
          }),

          ...(values.shareholdingType === 'Corporate' && {
            shareholderInIFZA: applicationFormSchema.shape.shareholderInIFZA,
          }),
        })

      case 3:
        return z.object({})

      case 4:
        return z.object({
          companyStatementValue:
            applicationFormSchema.shape.companyStatementValue,
          declaration: applicationFormSchema.shape.declaration,
          confirmation: applicationFormSchema.shape.confirmation,
          authorizedSignatoryName:
            applicationFormSchema.shape.authorizedSignatoryName,
          authorizedSignatoryEmail:
            applicationFormSchema.shape.authorizedSignatoryEmail,
        })

      default:
        return z.object({})
    }
  }

  interface RowData {
    id: number
    option: string
    activityCode: string
    ded: string
    tpa: string
    field4: string
    checked: boolean
  }

  const [rows, setRows] = useState<RowData[]>([
    {
      id: 1,
      option: '',
      activityCode: '',
      ded: '',
      tpa: '',
      field4: '',
      checked: false,
    },
    // ...additional rows
  ])

  function handleDelete(id: number) {
    setRows(rows.filter((row) => row.id !== id))
  }

  function handleOptionChange(id: number, value: string) {
    // Look up the activity by its ID (since your SelectItem uses activity.ID as the value)
    const activity = activitiesData.find((a: any) => a.ID === value)

    setRows(rows.map((row) =>
      row.id === id
        ? {
            ...row,
            option: value,
            // populate the code, license type and TPA from the lookup
            activityCode: activity?.Activity_Code || '',
            ded: activity?.DED_License_Type || '',
            tpa: activity?.TPA || '',
          }
        : row
    ))
  }

  function handleFieldChange(
    id: number,
    field: keyof Omit<RowData, 'id' | 'option' | 'checked'>,
    value: string
  ) {
    setRows(
      rows.map((row) => (row.id === id ? { ...row, [field]: value } : row))
    )
  }

  function handleCheckedChange(id: number, checked: boolean) {
    setRows(rows.map((row) => (row.id === id ? { ...row, checked } : row)))
  }

  function handleAddRow() {
    if (rows.length >= 7) {
      toast({
        title: 'Limit Reached',
        description: 'You can only add up to 7 activities.',
        variant: 'destructive',
      })
      return
    }
    const newRow: RowData = {
      id: rows.length ? Math.max(...rows.map((r) => r.id)) + 1 : 1,
      option: '',
      activityCode: '',
      ded: '',
      tpa: '',
      field4: '',
      checked: false,
    }
    setRows([...rows, newRow])
  }

  // Define transliteration map
  const transliterationMap: { [key: string]: string } = {
    A: 'ا',
    B: 'ب',
    C: 'ك',
    D: 'د',
    E: 'ي',
    F: 'ف',
    G: 'ج',
    H: 'ه',
    I: 'ي',
    J: 'ج',
    K: 'ك',
    L: 'ل',
    M: 'م',
    N: 'ن',
    O: 'و',
    P: 'ب',
    Q: 'ك',
    R: 'ر',
    S: 'س',
    T: 'ت',
    U: 'ا',
    V: 'ف',
    W: 'و',
    X: 'كس',
    Y: 'ي',
    Z: 'ز',
    TION: 'شن',
    THE: 'ذا',
    PP: 'ب',
    CH: 'ش',
    SH: 'ش',
    TH: 'ذ',
    CK: 'ك',
    PH: 'ف', // Consider "PH" as 'ف'
    GH: 'غ', // Consider "GH" as 'غ'
    AI: 'اي', // Consider "AI" as 'اي'
    OU: 'او', // Consider "OU" as 'او'
  }

  function transliterate(input: string): string {
    let result = ''
    let i = 0

    // Convert input to uppercase to handle both cases uniformly
    input = input.toUpperCase()

    // Check if the input is an exact match for one of the exceptions
    if (transliterationMap[input]) {
      return transliterationMap[input] // Return directly if there's an exception
    }

    while (i < input.length) {
      let char = input[i]
      let nextChar = i + 1 < input.length ? input[i + 1] : ''
      let nextTwoChars = char + nextChar

      // Check for compound characters (e.g. 'sh', 'ch', 'tion')
      if (transliterationMap[nextTwoChars]) {
        result += transliterationMap[nextTwoChars]
        i += 2 // Skip next character as it's part of the compound character
      } else if (transliterationMap[char]) {
        result += transliterationMap[char]
        i++
      } else {
        result += input[i] // If no transliteration found, keep the original character
        i++
      }
    }
    return result
  }

  // const [activities, setActivities] = useState([

  //   {
  //     activityName: '',
  //     activityCode: '',
  //     licenseType: '', // DED License Type

  //   },
  // ])

  // useEffect(() => {
  //   const licenseTypes = activities.map((a) => a.licenseType)

  //   const hasProfessional = licenseTypes.includes('Professional')
  //   const hasCommercial = licenseTypes.includes('Commercial')

  //   if (hasProfessional && hasCommercial) {
  //     form.setValue(
  //       'licenseType',
  //       'Combination (Additional Charges may apply)'
  //     )
  //   } else if (hasProfessional) {
  //     form.setValue(
  //       'licenseType',
  //       'Professional (Includes Service & Consultancy)'
  //     )
  //   } else if (hasCommercial) {
  //     form.setValue('licenseType', 'Commercial (Includes Trade)')
  //   } else {
  //     form.setValue('licenseType', '')
  //   }
  // }, [activities, form])

  const handleShareholdingTypeChange = (value: string) => {
    setShareholdingType(value)
  }

  const [shareholdingType, setShareholdingType] = useState(
    applicationType || ''
  )

  useEffect(() => {
    if (
      (applicationType === 'Individual' || applicationType === 'Corporate') &&
      form.getValues('shareholdingType') !== applicationType
    ) {
      form.setValue('shareholdingType', applicationType)
      handleShareholdingTypeChange(applicationType)
    }
  }, [applicationType])

  const [showNote, setShowNote] = useState(false) // State to manage if the note should be shown

  const handleShareCapitalBlur = () => {
    const proposedShareCapital = form.getValues('proposedShareCapital')
    const shareholdingType = form.getValues('shareholdingType') // Get shareholdingType from the form

    // Check if Proposed Share Capital is less than 48,000 and Shareholding Type is 'Individual'
    if (proposedShareCapital < 48000 && shareholdingType === 'Individual') {
      setShowNote(true) // Show the note if conditions are met
    } else {
      setShowNote(false) // Hide the note if conditions are not met
    }
  }

  // Function to move between steps
  const nextStep = async () => {
    const values = form.getValues()

    // Get the schema for the current step
    const schema = getStepSchema(currentStep, values)

    // Extract the fields required for validation from the schema
    const requiredFields = Object.keys(
      schema.shape
    ) as (keyof ApplicationFormValues)[]

    // Trigger validation for only the required fields
    await form.trigger(requiredFields)

    // Use Zod to validate the values manually and catch detailed errors
    const result = schema.safeParse(values)

    const stepFields = fieldsPerStep[currentStep]
    const currentStepValues: Partial<ApplicationFormValues> = {}

    for (const key of stepFields) {
      currentStepValues[key] = form.getValues(key) as any
    }

    console.log('Form Data For Step', currentStep, ':', currentStepValues)

    if (!result.success) {
      const issues = result.error.issues

      // Set manual errors for the invalid fields
      issues.forEach((issue) => {
        const field = issue.path[0] as keyof ApplicationFormValues

        if (requiredFields.includes(field)) {
          form.setError(field, {
            type: 'manual',
            message: issue.message,
          })
        }
      })

      // Show only the errors for the current step's required fields
      const visibleErrors: Partial<FieldErrors<ApplicationFormValues>> = {}
      for (const key of requiredFields) {
        const error = form.formState.errors[key]
        if (error) {
          visibleErrors[key] = error as any
        }
      }

      console.log('Validation Errors For Step', currentStep, ':', visibleErrors)

      toast({
        title: 'Error',
        description: 'Please fill in all the required fields.',
        variant: 'destructive',
      })

      return // Stop if there are validation errors
    }

    // Clear errors and proceed to the next step
    form.clearErrors()
    setStepValues((prev) => ({ ...prev, [currentStep]: values }))

    if (currentStep < 4) {
      setCurrentStep(currentStep + 1)
    }

    console.log(
      'Validation Errors For Step',
      currentStep,
      ': All fields have been filled correctly.'
    )
  }

  const prevStep = () => {
    if (currentStep > 1) {
      const previousStep = currentStep - 1
      const previousValues = stepValues[previousStep] || {}

      form.clearErrors() // clear all errors

      Object.entries(previousValues).forEach(([key, value]) => {
        const currentValue = form.getValues(key as keyof ApplicationFormValues)
        if (currentValue !== value) {
          form.setValue(key as keyof ApplicationFormValues, value, {
            shouldValidate: false,
            shouldTouch: false,
            shouldDirty: false,
          })
        }
      })

      setCurrentStep(previousStep)
    }
  }

  // Helper to map activity codes to API request fields
  const mapActivitiesToApiReq = (activityCodes: string[]) => {
    const apireq: any = {}
    activityCodes.forEach((code, index) => {
      if (index === 0) apireq.st_Business_Activity = code
      else if (index === 1) apireq.nd_Business_Activity = code
      else if (index === 2) apireq.rd_Business_Activity = code
      else if (index === 3) apireq.th_Business_Activity1 = code
      else if (index === 4) apireq.th_Business_Activity = code
      else if (index === 5) apireq.th_Business_Activity3 = code
      else if (index === 6) apireq.th_Business_Activity2 = code
    })
    apireq.Main_Activity = activityCodes[0] || ''
    return apireq
  }

  const onSubmit = async (data: ApplicationFormValues) => {
    // Get the fields for the current step
    const fieldsToValidate = fieldsPerStep[currentStep]

    // Trigger validation for the fields in the current step
    const isValid = await form.trigger(fieldsToValidate)

    // Log form data for the current step
    console.log('Form Data for Step', currentStep, ':', form.getValues())

    // Check if there are validation errors or if everything is filled correctly
    if (Object.keys(form.formState.errors).length === 0) {
      console.log(
        'Validation Errors For Step',
        currentStep,
        ':',
        'All fields have been filled correctly.'
      )
    } else {
      console.log(
        'Validation Errors For Step',
        currentStep,
        ':',
        form.formState.errors
      )
    }

    
    if (isValid) {
      setIsLoading(true)
      try {
        // Prepare payload mapping form values to API schema
        const payload = mainpulateLicenseApplicationData(data)
        console.log("☞ sending payload to /api/LIC/create:", JSON.stringify(payload, null, 2));
        // Send request to create license application
        const result = await createLicenseApplication(payload)
        // Example response schema: { data: <id>, message: string, license: object }
        console.log('API response:', result)
        console.log('Returned data (ID):', result.data)
        console.log('Returned message:', result.message)
        console.log('Returned license object:', result.license)
        // API returns { data: <id>, message, license }
        const licenseId = result.data ?? result.id ?? result.license?.id;
        console.log('Extracted licenseId:', licenseId);
        // Log the full license data as JSON
        console.log(`License record: ${JSON.stringify(result.license)}`)
        // also submit members if any
        if (members.length > 0) {
          // Ensure numberOfShares is set to 0 for any member where it's undefined
          const normalizedMembers = members.map(member => ({
            ...member,
            numberOfShares: member.numberOfShares || 0
          }))
          const membersPayload = mainpulateLicenseMembersData(normalizedMembers as any)
          console.log(`☞ sending members payload to /api/LIC/${licenseId}/members:`, JSON.stringify(membersPayload, null, 2));
          const memberResult = await createLicenseApplicationMembers(licenseId, membersPayload)
          console.log('Member creation response:', memberResult)
          // Extract member items from response robustly by scanning common array properties
          const possibleArrays = [memberResult, memberResult.data, memberResult.data?.data, memberResult.data?.members, memberResult.members];
          const rawItems: any[] = possibleArrays.find(arr => Array.isArray(arr)) || [];
          // Map raw items to member IDs
          const memberIds: (string|number)[] = rawItems.map(item =>
            typeof item === 'object'
              ? (item.id ?? item.data ?? item.memberId)
              : item
          );
          // Upload files for each member
          for (let i = 0; i < normalizedMembers.length; i++) {
            const m = normalizedMembers[i];
            const memberId = memberIds[i];
            if (!memberId) {
              console.error('Missing memberId for file upload:', memberResult, rawItems)
              toast({ title: `Cannot upload files: memberId undefined for ${m.firstName}`, variant: 'destructive' })
              continue
            }
            try {
              // Debug: log upload parameters
              console.log('Calling uploadMemberFiles with:', {
                licenseId,
                memberId,
                files: { passport: m.passport, eid: m.emiratesID },
                data: {
                  Member_First_Name: m.firstName,
                  Member_Last_Name: m.lastName,
                  Passport_Number: m.passportNumber,
                },
              });
              // Only passport (required) and eid (optional) per API spec; only three form fields
              await uploadMemberFiles(
                licenseId,
                memberId,
                {
                  passport: m.passport!,
                  eid: m.emiratesID || undefined,
                },
                {
                  Member_First_Name: m.firstName || '',
                  Member_Last_Name: m.lastName || '',
                  Passport_Number: m.passportNumber || '',
                }
              )
              toast({ title: `Files uploaded for ${m.firstName}`, variant: 'success' })
            } catch (fileError: any) {
              console.error('File upload error for member', m, fileError)
              toast({ title: `File upload failed for ${m.firstName}`, description: fileError.response?.data?.message || fileError.message, variant: 'destructive' })
            }
          }
        }
        toast({
          title: result.message ?? 'Success!',
          description: JSON.stringify(result),
          variant: 'success',
        })
        // Log or handle returned application ID
        console.log('License application ID:', licenseId)
      } catch (error: any) {
        console.error("API error response:", error.response?.data || error.message);
        toast({
          title: 'Fail!',
          description: error.response?.data?.message || error.message || 'Something went wrong.',
          variant: 'error',
        })
      } finally {
        setIsLoading(false)
      }
    } else {
      // Handle validation errors
      toast({
        title: 'Error',
        description: 'Please fill in all the required fields.',
        variant: 'destructive',
      })
    }
  }

  // Detect external access via id query param
  const externalId = typeof window !== 'undefined'
    ? new URLSearchParams(window.location.search).get('id')
    : null

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        {/* <TopNav links={topNav} /> */}
        <div className='ml-auto flex items-center space-x-4'>
          {/* <Search /> */}
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-2 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight lg:text-[28px]'>
            License Application
          </h1>
          <div className='flex items-center space-x-2 lg:relative sm:fixed lg:w-auto sm:space-x-2 fixed bottom-0 right-0 p-3 bg-background w-full text-right flex justify-end z-10 sm:z-auto sm:bg-transparent sm:p-0'></div>
        </div>
        <ApplicationProgress currentStep={currentStep} />

        {/* === Step 1 Content === */}
        {currentStep === 1 && (
          <Card className='mt-6'>
            <CardHeader>
              <CardTitle>Company Information ( Step 1 of 4 )</CardTitle>
            </CardHeader>

            <CardContent>
              <Form {...form}>
                <form
                  // onSubmit={form.handleSubmit(onSubmit)}
                  className='space-y-4 mb-4'
                >
                  {/* Application Date */}
                  <div className='flex flex-wrap space-between gap-y-4'>
                    <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                      <FormField
                        control={form.control}
                        name='applicationDate'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Application Date{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <DateTimePicker
                              granularity='day'
                              value={field.value || new Date()}
                              onChange={field.onChange}
                              displayFormat={{
                                hour24: 'dd MMMM yyyy', // 31 January 2024
                              }}
                              disabled={true}
                            />
                            <FormDescription>dd-MMM-yyyy</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Application Type */}
                    <div className='flex-1'>
                      <FormField
                        control={form.control}
                        name='applicationType'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Application Type{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder='Select an application type' />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value='Individual'>
                                  Individual
                                </SelectItem>
                                <SelectItem value='Corporate'>
                                  Corporate
                                </SelectItem>
                                <SelectItem value='Branch'>Branch</SelectItem>
                              </SelectContent>
                            </Select>

                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
 {/* <CompanyMembers /> */}
                  {/* CEM field */}
                  <div className='flex flex-wrap space-between gap-y-4'>
                    <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                      <FormField
                        control={form.control}
                        name='cem'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>CEM</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                value={field.value || ''}
                                disabled={true}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/*Email Address */}
                    <div className='flex-1'>
                      <FormField
                        control={form.control}
                        name='emailAddress'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Email Address{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <Input type='email' {...field} />
                            </FormControl>
                            <FormDescription>
                              Please mention the email address where you would
                              like to receive the copy of this form as well as
                              the updates regarding the application and
                              quotation.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                  {/* App Updates WhatsApp Number */}
                  <FormField
                    control={form.control}
                    name='phone'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>App Updates WhatsApp Number</FormLabel>
                        <PhoneInput
                          country={'ae'}
                          // value={field.value.toString()}
                          onChange={(phone) => field.onChange(phone)}
                          containerClass='fz-country-code flex h-9 w-full rounded-tl-[6px] rounded-bl-[6px] border border-input bg-transparent text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm'
                          inputClass=' flex  !h-9 !w-full rounded-tl-[6px] rounded-bl-[6px] border !border-transparent !bg-transparent px-3 text-base !shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm transform translate-y-[-2px]'
                          buttonClass='!h-100 !border-l-transparent !border-t-transparent !bg-slate-200 dark:!bg-slate-800 dark:!border-r-transparent !border-b-transparent !border-gray-200 !bg-transparent !rounded-tl-[8px] !rounded-bl-[6px]'
                          enableSearch={true}
                          searchPlaceholder='Search country...'
                        />
                        <FormDescription>
                          Please mention the mobile number where you would like
                          to receive the copy of this form as well as the
                          updates regarding the application and quotation.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Partner Name */}
                  {/* <div className='flex flex-wrap space-between gap-y-4'>
    <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                      <FormField
                        control={form.control}
                        name='partnerName'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Partner Name{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div> */}

                  {/* <div className='flex-1'>

                       <FormField
                        control={form.control}
                        name='partnerId'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Partner ID{' '} <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                   </div>
                  </div> */}

                  {/* Partner Email Address */}
                  {/* <div className='flex flex-wrap space-between gap-y-4'>
                  <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                      <FormField
                        control={form.control}
                        name='partnerEmail'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Partner Email Address</FormLabel>
                            <FormControl>
                              <Input type='email' {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div> */}
                  {/* Partner Contact Email */}
                  {/* <div className='flex-1'>
                      <FormField
                        control={form.control}
                        name='partnerContactEmail'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Partner Contact Email{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <Input type='Enter Contact Email' {...field} />
                            </FormControl>
                            <FormDescription>
                             Please mention the email address where you would like to receive the copy of this form as well as the updates regarding the application and quotation.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div> */}

                  {/* Quotation Type */}

                  <FormField
                    control={form.control}
                    name='quotationValue'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Quotation Type <span style={{ color: 'red' }}>*</span>
                        </FormLabel>
                        <FormControl>
                          <RadioGroup
                            className='flex align-center'
                            value={field.value}
                            onValueChange={field.onChange}
                          >
                            <RadioGroupItem
                              className='mt-1'
                              value='Gross'
                              id='Gross'
                            />
                            <label htmlFor='Gross'>Gross</label>

                            <RadioGroupItem
                              className='mt-1'
                              value='Net'
                              id='Net'
                            />
                            <label htmlFor='Net'>Net</label>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  {/* Gross/Net - Both Required */}

                  <FormField
                    control={form.control}
                    name='grossNet'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Gross/Net - Both Required{' '}
                          <span style={{ color: 'red' }}>*</span>
                        </FormLabel>
                        <FormControl>
                          <RadioGroup
                            className='flex align-center'
                            value={field.value}
                            onValueChange={field.onChange}
                          >
                            <RadioGroupItem
                              className='mt-1'
                              value='Yes'
                              id='Yes'
                            />
                            <label htmlFor='Yes'>Yes</label>
                            <RadioGroupItem
                              className='mt-1'
                              value='No'
                              id='No'
                            />
                            <label htmlFor='No'>No</label>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  {/* Commission Plan */}
                  <FormField
                    control={form.control}
                    name='commissionPlan'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Commission Plan{' '}
                          <span className='text-red-500'>*</span>
                        </FormLabel>
                        <FormControl>
                          <RadioGroup
                            className='flex align-center'
                            value={field.value}
                            onValueChange={field.onChange}
                          >
                            <RadioGroupItem
                              className='mt-1'
                              value='Plane A'
                              id='Plane A'
                            />
                            <label htmlFor='Plane A'>Plane A</label>
                            <RadioGroupItem
                              className='mt-1'
                              value='Plane B'
                              id='Plane B'
                            />
                            <label htmlFor='Plane B'>Plane B</label>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  {/* How would you like to make payment for the application */}
                  <FormField
                    control={form.control}
                    name='paymentType'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          How would you like to make payment for the application{' '}
                          <span className='text-red-500'>*</span>
                        </FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder='Select payment type' />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value='Online Payment'>
                                Online Payment
                              </SelectItem>
                              <SelectItem value='Bank Transfer'>
                                Bank Transfer
                              </SelectItem>
                              <SelectItem value='E-Wallet'>E-Wallet</SelectItem>
                              <SelectItem value='Cheque'>Cheque</SelectItem>
                              <SelectItem value='Cash'>Cash</SelectItem>
                              <SelectItem value='ATM'>ATM</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <CardHeader className='px-0 pt-4 pb-0'>
                    <CardTitle>Select Your Business Activities</CardTitle>

                    <CardDescription className='px-0 pt-4 pb-0 '>
                      Please select the activities from the following link :{' '}
                      <a
                        href='https://activities.ifza.com/'
                        target='_blank'
                        rel='noopener noreferrer'
                        className='text-blue-500 underline'
                      >
                        IFZA Business Activities
                      </a>
                    </CardDescription>
                  </CardHeader>

                  {/* Instructions Text */}

                  {form.watch('applicationType') === 'Branch' && (
                    <>
                      <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                        <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                          <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                        </span>
                        <div className='flex flex-col ml-2'>
                          <AlertDescription>
                            <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                              Please add the same activity as the Branch company
                              or similar ones if it is not listed in our
                              activities' list.
                            </p>
                          </AlertDescription>
                        </div>
                      </Alert>
                    </>
                  )}

                  {/* Activities Table */}
                  <p className='text-xl font-semibold mt-4 mb-4'>Activities</p>

                  <div className='fz-input-table space-y-4'>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead> </TableHead>
                          <TableHead>Activity Name</TableHead>
                          <TableHead>Activity Code</TableHead>
                          <TableHead>DED License Type</TableHead>
                          <TableHead>TPA</TableHead>
                          <TableHead>Regulatory Authority</TableHead>
                          <TableHead>Main Activity</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {rows.map((row) => (
                          <TableRow key={row.id}>
                            <TableCell>
                              <Button
                                variant='ghost'
                                onClick={() => handleDelete(row.id)}
                              >
                                <TrashIcon className='w-4 h-4' />
                              </Button>
                            </TableCell>
                            <TableCell>
                              <Select
                                value={row.option}
                                onValueChange={(value) =>
                                  handleOptionChange(row.id, value)
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder='Select Activity Name' />
                                </SelectTrigger>
                                <SelectContent searchable searchPlaceholder="Search activities...">
                                  {activitiesData.map((activity: any) => (
                                    <SelectItem key={activity.ID} value={activity.ID}>
                                      {activity.Activity_Name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </TableCell>
                            <TableCell>
                              <Input
                                value={row.activityCode}
                                onChange={(e) =>
                                  handleFieldChange(
                                    row.id,
                                    'activityCode',
                                    e.target.value
                                  )
                                }
                                placeholder='Activity Code'
                              />
                            </TableCell>
                            <TableCell>
                              <Input
                                value={row.ded}
                                onChange={(e) =>
                                  handleFieldChange(
                                    row.id,
                                    'ded',
                                    e.target.value
                                  )
                                }
                                placeholder='DED'
                              />
                            </TableCell>

                            <TableCell>
                              <Input
                                value={row.tpa}
                                onChange={(e) =>
                                  handleFieldChange(
                                    row.id,
                                    'tpa',
                                    e.target.value
                                  )
                                }
                                placeholder='TPA'
                              />
                            </TableCell>
                            <TableCell>
                              <Input
                                value={row.field4}
                                onChange={(e) =>
                                  handleFieldChange(
                                    row.id,
                                    'field4',
                                    e.target.value
                                  )
                                }
                                placeholder='Field 4'
                              />
                            </TableCell>
                            <TableCell>
                              <Checkbox
                                checked={row.checked}
                                onCheckedChange={(checked) =>
                                  handleCheckedChange(row.id, checked === true)
                                }
                              />
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                    <Button
                      type='button'
                      variant='link'
                      className='mt-4 underline'
                      onClick={handleAddRow}
                    >
                      <PlusIcon></PlusIcon> Add New
                    </Button>
                  </div>

                  {/* Conditionally render the newActivity field based on Application Type */}
                  {applicationType && (
                    <FormField
                      control={form.control}
                      name='newActivity'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Are you looking for an activity which is not
                            available in our list ?{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <RadioGroup
                              className='flex align-center'
                              value={field.value}
                              onValueChange={field.onChange}
                            >
                              <RadioGroupItem
                                className='mt-1'
                                value='Yes'
                                id='option1'
                              />
                              <label htmlFor='option1'>Yes</label>
                              <RadioGroupItem
                                className='mt-1'
                                value='No'
                                id='option2'
                              />
                              <label htmlFor='option2'>No</label>
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {/* Conditionally render the description and additional input fields */}
                  {newActivityValue === 'Yes' && (
                    <>
                      <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
                        <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                          <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
                        </span>
                        <div className='flex space-y-2 flex-col ml-3'>
                          <AlertDescription>
                            <p className='text-sm text-slate-800 dark:text-slate-400'>
                              Please let us know briefly about the particular
                              activity that you are looking for. We will let you
                              know if it is possible to add the activity in our
                              available activity list.
                            </p>
                          </AlertDescription>
                        </div>
                      </Alert>

                      {/* Additional Activity */}

                      <FormField
                        control={form.control}
                        name='additionalActivity'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Additional Activity{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder='Please mention additional Activity name and details here.'
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </>
                  )}

                  {/* I accept the statement below to abide by the activity rules. */}

                  <FormField
                    control={form.control}
                    name='companyStatement'
                    render={({ field }) => (
                      <FormItem>
                        <div className='flex items-center'>
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={(checked: boolean) =>
                                field.onChange(checked)
                              }
                            />
                          </FormControl>
                          <FormLabel className='ml-2'>
                            I accept the statement below to abide by the
                            activity rules.
                          </FormLabel>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
                    <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                      <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
                    </span>
                    <div className='flex space-y-2 flex-col ml-3'>
                      <AlertDescription>
                        <p className='text-sm text-slate-800 dark:text-slate-400'>
                          We undertake that we will obtain all approvals,
                          permits, licenses or other permissions from UAE
                          government agencies that may be required at any time
                          for particular products or services, and that we will
                          cease to trade in any particular products or services
                          to the extent that such trading is restricted or
                          prohibited by UAE governmental agencies.
                        </p>
                      </AlertDescription>
                    </div>
                  </Alert>

                  {/*Multi Select Dropdown : Country of Operation */}

                  <FormField
                    control={form.control}
                    name='countryOfOperation'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Country of Operation{' '}
                          <span className='text-red-500'>*</span>
                        </FormLabel>
                        <FormControl>
                          <CountryDropdown
                            multiple
                            placeholder='Country'
                            defaultValue={field.value as string[]}
                            onChange={(countries) => {
                              const codes = countries.map((c) => c.alpha3)
                              field.onChange(codes)
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  {/* Are you going to operate as Franchise ? */}

                  {form.watch('applicationType') === 'Individual' && (
                    <>
                      <FormField
                        control={form.control}
                        name='operateAsFranchise'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Are you going to operate as Franchise ?
                            </FormLabel>
                            <FormControl>
                              <RadioGroup
                                className='flex align-center'
                                value={field.value}
                                onValueChange={field.onChange}
                              >
                                <RadioGroupItem
                                  className='mt-1'
                                  value='Yes'
                                  id='franchise_yes'
                                />
                                <label htmlFor='franchise_yes'>Yes</label>
                                <RadioGroupItem
                                  className='mt-1'
                                  value='No'
                                  id='franchise_no'
                                />
                                <label htmlFor='franchise_no'>No</label>
                              </RadioGroup>
                            </FormControl>
                            <FormDescription>
                              Franchise is typically a branch of an already
                              existing company in another jurisdiction/country.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Conditionally render "Trade Name of Franchise" field when 'Yes' is selected */}
                      {form.watch('operateAsFranchise') === 'Yes' && (
                        <FormField
                          control={form.control}
                          name='tradeNameOfFranchise'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Please input Trade name of Franchise
                              </FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  defaultValue={field.value || ''}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}
                    </>
                  )}

                  {['Individual', 'Corporate'].includes(
                    form.watch('applicationType')
                  ) && (
                    <>
                      <CardHeader className='px-0 pt-4 pb-0'>
                        <CardTitle>Set Your Company Name</CardTitle>
                      </CardHeader>

                      <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
                        <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                          <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
                        </span>
                        <div className='flex space-y-2 flex-col ml-3'>
                          <AlertDescription>
                            <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                              Please be aware that the names you list below
                              merely reflect your company's preferences.
                              Approval depends on adherence to the guidelines
                              and remains subject to regulatory oversight.
                            </p>
                          </AlertDescription>
                          <AlertDescription>
                            <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                              {' '}
                              The application will be processed with our default
                              Arabic translation. Please enter your preferred
                              Arabic translation for the company name below, if
                              any.
                            </p>
                          </AlertDescription>

                          <AlertDescription>
                            <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                              {' '}
                              Please refer to the company guidelines{' '}
                              <a
                                href='https://cdn.ifza.com/docs/01_new/references/companyguidelines_190821.pdf'
                                target='_blank'
                                rel='noopener noreferrer'
                                className='text-blue-500 underline'
                              >
                                here
                              </a>
                              .
                            </p>
                          </AlertDescription>
                        </div>
                      </Alert>
                      {/* Option 1 - English */}
                      <div className='flex flex-wrap space-between gap-y-4'>
                        <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                          <FormField
                            control={form.control}
                            name='option1English'
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Option 1 - English</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    placeholder='Enter the Option 1 - English Name'
                                    onBlur={() => {
                                      // Ensure the value is a string (fallback to empty string if undefined)
                                      const englishValue = field.value || ''
                                      const arabicTranslation =
                                        transliterate(englishValue) // Transliterate the value
                                      form.setValue(
                                        'option1Arabic',
                                        arabicTranslation
                                      ) // Set the Arabic translation to the corresponding field
                                    }}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        {/* Option 1 - Arabic */}
                        <div className='flex-1'>
                          <FormField
                            control={form.control}
                            name='option1Arabic'
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Option 1 - Arabic</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    placeholder='Auto-populated by default Arabic translation'
                                    disabled
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        {/* Option 2 - English */}
                        <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                          <FormField
                            control={form.control}
                            name='option2English'
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Option 2 - English</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    placeholder='Enter the Option 2 - English Name'
                                    onBlur={() => {
                                      // Ensure the value is a string (fallback to empty string if undefined)
                                      const englishValue = field.value || ''
                                      const arabicTranslation =
                                        transliterate(englishValue) // Transliterate the value
                                      form.setValue(
                                        'option2Arabic',
                                        arabicTranslation
                                      ) // Set the Arabic translation to the corresponding field
                                    }}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        {/* Option 2 - Arabic */}
                        <div className='flex-1'>
                          <FormField
                            control={form.control}
                            name='option2Arabic'
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Option 2 - Arabic</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    placeholder='Auto-populated by default Arabic translation'
                                    disabled
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        {/* Option 3 - English */}
                        <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                          <FormField
                            control={form.control}
                            name='option3English'
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Option 3 - English</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    placeholder='Enter the Option 3 - English Name'
                                    onBlur={() => {
                                      // Ensure the value is a string (fallback to empty string if undefined)
                                      const englishValue = field.value || ''
                                      const arabicTranslation =
                                        transliterate(englishValue) // Transliterate the value
                                      form.setValue(
                                        'option3Arabic',
                                        arabicTranslation
                                      ) // Set the Arabic translation to the corresponding field
                                    }}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        {/* Enter Option 3 - Arabic */}
                        <div className='flex-1'>
                          <FormField
                            control={form.control}
                            name='option3Arabic'
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Option 3 - Arabic</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    placeholder='Auto-populated by default Arabic translation'
                                    disabled
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>

                      <FormField
                        control={form.control}
                        name='agreementTerms'
                        render={({ field }) => (
                          <FormItem>
                            <div className='flex items-center'>
                              <FormControl>
                                <Checkbox
                                  checked={field.value}
                                  onCheckedChange={(checked: boolean) =>
                                    field.onChange(checked)
                                  }
                                />
                              </FormControl>
                              <FormLabel className='ml-2'>
                                I, hereby confirm that I have read and accept
                                the terms and conditions
                              </FormLabel>
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <Alert className='border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                        <AlertDescription>
                          Terms & Conditions are available{' '}
                          <a
                            href='https://files.ifza.com/external/92561fb670f9e384622387016a0d266b04d1eeeb68011885c30444762e0d674c'
                            target='_blank'
                            rel='noopener noreferrer'
                            className='text-blue-500 underline'
                          >
                            here
                          </a>
                        </AlertDescription>
                      </Alert>
                    </>
                  )}

                  {/* Branch Name (only visible if Branch is selected) */}
                  {form.watch('applicationType') === 'Branch' && (
                    <>
                      <div className='flex flex-wrap space-between gap-y-4'>
                        <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                          <FormField
                            control={form.control}
                            name='branchName'
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Branch Name</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    placeholder='Enter the Branch Name'
                                    onBlur={() => {
                                      // Ensure the value is a string (fallback to empty string if undefined)
                                      const englishValue = field.value || ''
                                      const arabicTranslation =
                                        transliterate(englishValue) // Transliterate the value
                                      form.setValue(
                                        'branchNameArabic',
                                        arabicTranslation
                                      ) // Set the Arabic translation to the corresponding field
                                    }}
                                  />
                                </FormControl>
                                <FormDescription>
                                  The company name should be an exact match to
                                  that of the Branch company Branch Name In
                                  Arabic
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        {/* Branch Name in Arabic */}
                        <div className='flex-1'>
                          <FormField
                            control={form.control}
                            name='branchNameArabic'
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Branch Name in Arabic</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    placeholder='Auto-populated by default Arabic translation'
                                    disabled
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>

                      {/* Country of Registration of the Parent Company */}

                      <FormField
                        control={form.control}
                        name='countryOfRegistration'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Country of Registration of the Parent Company
                            </FormLabel>
                            <FormControl>
                              <CountryDropdown
                                placeholder='Country'
                                defaultValue={field.value}
                                onChange={(country) => {
                                  field.onChange(country.alpha3)
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name='addressOfParentCompany'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Address of Parent Company</FormLabel>
                            <FormControl>
                              <Input {...field} value={field.value || ''} />
                            </FormControl>

                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </>
                  )}

                  {/* Financial Year Details */}
                  <CardHeader className='px-0 pt-4 pb-0'>
                    <CardTitle>Financial Year Details</CardTitle>
                  </CardHeader>

                  {/* Financial Year Details */}
                  <div className='flex flex-wrap space-between gap-2'>
                    {/* Financial Year Start Date */}
                    <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                      <FormField
                        control={form.control}
                        name='financialYearStartDate'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Financial Year Start Date</FormLabel>
                            <DateTimePicker
                              granularity='day'
                              value={field.value || new Date()}
                              onChange={field.onChange}
                              displayFormat={{
                                hour24: 'dd MMMM yyyy',
                              }}
                              disabled={true}
                            />
                            <FormDescription>dd-MMM-yyyy</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Financial Year End Date */}
                    <div className='w-full sm:w-[49%]'>
                      <FormField
                        control={form.control}
                        name='financialYearEndDate'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Financial Year End Date</FormLabel>
                            <DateTimePicker
                              granularity='day'
                              value={field.value}
                              onChange={field.onChange}
                              displayFormat={{
                                hour24: 'dd MMMM yyyy',
                              }}
                            />
                            <FormDescription>dd-MMM-yyyy</FormDescription>

                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        )}

        {/* === Step 2 Content === */}
        {currentStep === 2 && (
          <Card className='mt-6'>
            <CardHeader>
              <CardTitle>
                Select the License Type and Visa Package for your Business (
                Step 2 of 6 )
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form className='space-y-4 fz-form'>
                  {/* Instructions Text */}
                  <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                    <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                      <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                    </span>
                    <div className='flex flex-col ml-2'>
                      <AlertDescription>
                        Business activities should be from{' '}
                        <strong className='font-bold'>
                          {' '}
                          one license type only.{' '}
                        </strong>{' '}
                        Certain activities are subject to third party approvals.
                      </AlertDescription>
                    </div>
                  </Alert>
                  <div className='flex flex-wrap space-between gap-y-4'>
                    {/* License Type */}
                    <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                      <FormField
                        control={form.control}
                        name='licenseType'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              License Type{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={field.onChange}
                                value={field.value}
                                disabled // Disable the select field
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder='Select License Type' />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value='Commercial (Includes Trade)'>
                                    Commercial (Includes Trade)
                                                                   </SelectItem>
                                  <SelectItem value='Professional (Includes Service & Consultancy)'>
                                    Professional (Includes Service &
                                    Consultancy)
                                  </SelectItem>
                                  <SelectItem value='Combination (Additional Charges may apply)'>
                                    Combination (Additional Charges may apply)
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    {/* Trade License Validity */}
                    <div className='flex-1'>
                      <FormField
                        control={form.control}
                        name='tradeLicenseValidity'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Trade License Validity{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={field.onChange}
                                value={field.value}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder='Select' />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value='1 Year'>1 Year</SelectItem>
                                  <SelectItem value='2 Years'>
                                    2 Years
                                  </SelectItem>
                                  <SelectItem value='3 Years'>
                                    3 Years
                                  </SelectItem>

                                  <SelectItem value='5 Years'>
                                    5 Years
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {/* Show Message for Trade License Validity */}
                  {(form.watch('tradeLicenseValidity') === '2 Years' ||
                    form.watch('tradeLicenseValidity') === '3 Years' ||
                    form.watch('tradeLicenseValidity') === '5 Years') && (
                    <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
                      <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                        <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
                      </span>
                      <div className='flex space-y-2 flex-col ml-3'>
                        <AlertDescription>
                          <p className='text-sm text-slate-800 dark:text-slate-400'>
                            Trade License is issued with "One Year" validity and
                            is renewed automatically every year for the number
                            of years selected.
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>
                  )}

                  {/* Visa Package */}

                  <FormField
                    control={form.control}
                    name='visaPackage'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Visa Package <span className='text-red-500'>*</span>
                        </FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder='Select Visa Package' />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value='0 Visa'>0 Visa</SelectItem>
                              <SelectItem value='1 Visa'>1 Visa</SelectItem>
                              <SelectItem value='2 Visa'>2 Visa</SelectItem>
                              <SelectItem value='3 Visa'>3 Visa</SelectItem>
                              <SelectItem value='4 Visa'>4 Visa</SelectItem>
                              <SelectItem value='5 Visa'>5 Visa</SelectItem>
                              <SelectItem value='6 Visa'>6 Visa</SelectItem>
                              <SelectItem value='7 Visa'>7 Visa</SelectItem>
                              <SelectItem value='8 Visa'>8 Visa</SelectItem>
                              <SelectItem value='9 Visa'>9 Visa</SelectItem>
                              <SelectItem value='10 Visa'>10 Visa</SelectItem>
                              <SelectItem value='More than 10 Visas'>
                                More than 10 Visas
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Show Message for Visa Package */}
                  {(form.watch('visaPackage') === '4 Visa' ||
                    form.watch('visaPackage') === '5 Visa' ||
                    form.watch('visaPackage') === '6 Visa' ||
                    form.watch('visaPackage') === '7 Visa') && (
                    <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
                      <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                        <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
                      </span>
                      <div className='flex space-y-2 flex-col ml-3'>
                        <AlertDescription>
                          <p className='text-sm text-slate-800 dark:text-slate-400'>
                            Please note that 4 or more visas allocation will
                            require an office space with IFZA.
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>
                  )}

                  {/* Would you require Investor or Partner Visa ? */}
                  {form.watch('visaPackage') !== '0 Visa' &&
                    form.watch('visaPackage') !== '' && (
                      <div className='mt-4 mb-4'>
                        <FormField
                          control={form.control}
                          name='investorOrPartnerVisa'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Would you require Investor or Partner Visa ?
                              </FormLabel>
                              <FormControl>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder='Select ' />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value='Investor'>
                                      Investor
                                    </SelectItem>
                                    <SelectItem value='Partner'>
                                      Partner
                                    </SelectItem>
                                    <SelectItem value='No'>No</SelectItem>
                                  </SelectContent>
                                </Select>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    )}

                  {/* Show message based on the selected "Visa Type" */}
                  {form.watch('investorOrPartnerVisa') === 'Investor' && (
                    <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
                      <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                        <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
                      </span>
                      <div className='flex space-y-2 flex-col ml-3'>
                        <AlertDescription>
                          <p className='text-sm text-slate-800 dark:text-slate-400'>
                            Valid only for single shareholder company with a
                            minimum share capital of AED 48,000.
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>
                  )}

                  {form.watch('investorOrPartnerVisa') === 'Partner' && (
                    <>
                      <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
                        <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                          <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
                        </span>
                        <div className='flex space-y-2 flex-col ml-3'>
                          <AlertDescription>
                            <p className='text-sm text-slate-800 dark:text-slate-400'>
                              A License cannot have more than 3 Partner Visas.
                              Each Partner needs to allocate a minimum of 48,000
                              share capital for Partner visa to be applicable.
                            </p>
                          </AlertDescription>
                        </div>
                      </Alert>

                      {/* How many partner visas would you require? */}

                      <FormField
                        control={form.control}
                        name='numberOfPartnerVisas'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              How many partner visas would you require ?
                            </FormLabel>
                            <FormControl>
                              <Input
                                type='number'
                                placeholder='Enter number of partner visas'
                                value={field.value}
                                onChange={field.onChange}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </>
                  )}

                  {/* Do you want to include Visa cost in License Estimate/Quote */}

                  {form.watch('visaPackage') !== '0 Visa' &&
                    form.watch('visaPackage') !== '' && (
                      <FormField
                        control={form.control}
                        name='includeVisaCostInQuote'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Do you want to include Visa cost in License
                              Estimate/Quote ?
                            </FormLabel>
                            <FormControl>
                              <RadioGroup
                                className='flex align-center'
                                value={field.value || ''}
                                onValueChange={field.onChange}
                              >
                                <RadioGroupItem
                                  className='mt-1'
                                  value='Yes'
                                  id='yes'
                                />
                                <label htmlFor='yes'>
                                  Yes
                                </label>
                                <RadioGroupItem
                                  className='mt-1'
                                  value='No'
                                  id='no'
                                />
                                <label htmlFor='no'>
                                  No
                                </label>
                              </RadioGroup>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}

                  {/* Show additional visa fields when "Yes" is selected for including visa cost */}

                  {form.watch('includeVisaCostInQuote') === 'Yes' && (
                    <>
                      {/* How many visas do you want to include in the quotation ?  */}

                      <FormField
                        control={form.control}
                        name='numberOfVisasInQuote'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              How many visas do you want to include in the
                              quotation ?
                            </FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={field.onChange}
                                value={field.value}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder='Select number of visas' />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value='0'>0</SelectItem>
                                  <SelectItem value='1'>1</SelectItem>
                                  <SelectItem value='2'>2</SelectItem>
                                  <SelectItem value='3'>3</SelectItem>
                                  <SelectItem value='4'>4</SelectItem>
                                  <SelectItem value='5'>5</SelectItem>
                                  <SelectItem value='6'>6</SelectItem>
                                  <SelectItem value='7'>7</SelectItem>
                                  <SelectItem value='8'>8</SelectItem>
                                  <SelectItem value='9'>9</SelectItem>
                                  <SelectItem value='10'>10</SelectItem>
                                  <SelectItem value='More than 10'>
                                    More than 10
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* No. of Inside Country Visas */}

                      <FormField
                        control={form.control}
                        name='numberOfInsideCountryVisas'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              No. of Inside Country Visas{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={field.onChange}
                                value={field.value}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder='Select number of Inside Country Visas' />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value='0'>0</SelectItem>
                                  <SelectItem value='1'>1</SelectItem>
                                  <SelectItem value='2'>2</SelectItem>
                                  <SelectItem value='3'>3</SelectItem>
                                  <SelectItem value='4'>4</SelectItem>
                                  <SelectItem value='5'>5</SelectItem>
                                  <SelectItem value='6'>6</SelectItem>
                                  <SelectItem value='7'>7</SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Number of Outside Country Visas */}

                      <FormField
                        control={form.control}
                        name='numberOfOutsideCountryVisas'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Number of Outside Country Visas
                            </FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={field.onChange}
                                value={field.value}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder='Select number of Outside Country Visas' />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value='0'>0</SelectItem>
                                  <SelectItem value='1'>1</SelectItem>
                                  <SelectItem value='2'>2</SelectItem>
                                  <SelectItem value='3'>3</SelectItem>
                                  <SelectItem value='4'>4</SelectItem>
                                  <SelectItem value='5'>5</SelectItem>
                                  <SelectItem value='6'>6</SelectItem>
                                  <SelectItem value='7'>7</SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </>
                  )}

                  {(form.watch('visaPackage') === '4 Visa' ||
                    form.watch('visaPackage') === '5 Visa' ||
                    form.watch('visaPackage') === '6 Visa' ||
                    form.watch('visaPackage') === '7 Visa') && (
                    <>
                      <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                        <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                          <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                        </span>
                        <div className='flex flex-col ml-2'>
                          <AlertDescription>
                            <p className='text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-["•"] before:absolute before:left-0 before:top-0'>
                              Please note that a licensee is required to lease
                              exclusive office space within an IFZA Building in
                              order to obtain a visa package with an entitlement
                              to four (4) or more visas.
                            </p>
                          </AlertDescription>
                        </div>
                      </Alert>
                    </>
                  )}

                  {/* Show fields only if Visa Package is NOT "0 visa" */}

                  {/* Radio field for "Establishment Card" */}
                  {form.watch('visaPackage') !== '0 visa' &&
                    form.watch('visaPackage') !== '' && (
                      <>
                        <FormField
                          control={form.control}
                          name='establishmentCard'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Do you want to apply for an Establishment Card ?{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <RadioGroup
                                  className='flex align-center'
                                  value={field.value}
                                  onValueChange={field.onChange}
                                >
                                  <RadioGroupItem
                                    className='mt-1'
                                    value='Yes'
                                    id='establishment_yes'
                                  />
                                  <label htmlFor='establishment_yes'>Yes</label>

                                  <RadioGroupItem
                                    className='mt-1'
                                    value='No'
                                    id='establishment_no'
                                  />
                                  <label htmlFor='establishment_no'>No</label>
                                </RadioGroup>
                              </FormControl>
                              <FormDescription>
                                Please let us know if you would like to apply
                                for the Establishment Card, this can be applied
                                at a later stage as well. It is advisable to
                                apply for it the same time as the license.
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        {/* <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
                    <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                      <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
                    </span>
                    <div className='flex space-y-2 flex-col ml-3'>
                      <AlertDescription>
                        <p className='text-sm text-slate-800 dark:text-slate-400'>
                          Establishment card is necessary once you apply for the
                          Visas under this company, it is also required once you
                          apply for any utilities/phone lines etc.
                        </p>
                        <p className='text-sm text-slate-800 dark:text-slate-400'>
                          It is your choice, if you would like to apply for the
                          establishment card at the same time as the license or
                          at a later stage.
                        </p>
                      </AlertDescription>
                    </div>
                  </Alert> */}
                      </>
                    )}

                  {/* Financial Year Details */}
                  <CardHeader className='px-0 pt-4 pb-0'>
                    <CardTitle>Financial Year Details</CardTitle>
                  </CardHeader>

                  {/* Financial Year Details */}
                  <div className='flex flex-wrap space-between gap-2'>
                    {/* Financial Year Start Date */}
                    <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                      <FormField
                        control={form.control}
                        name='financialYearStartDate'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Financial Year Start Date</FormLabel>
                            <DateTimePicker
                              granularity='day'
                              value={field.value || new Date()}
                              onChange={field.onChange}
                              displayFormat={{
                                hour24: 'dd MMMM yyyy',
                              }}
                              disabled={true}
                            />
                            <FormDescription>dd-MMM-yyyy</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Financial Year End Date */}
                    <div className='w-full sm:w-[49%]'>
                      <FormField
                        control={form.control}
                        name='financialYearEndDate'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Financial Year End Date</FormLabel>
                            <DateTimePicker
                              granularity='day'
                              value={field.value}
                              onChange={field.onChange}
                              displayFormat={{
                                hour24: 'dd MMMM yyyy',
                              }}
                            />
                            <FormDescription>dd-MMM-yyyy</FormDescription>

                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {['Individual', 'Corporate'].includes(
                    form.watch('applicationType')
                  ) && (
                    <>
                      {/* Shareholding Type */}
                      <FormField
                        control={form.control}
                        name='shareholdingType'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Shareholding Type</FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={(value) => {
                                  field.onChange(value) // Update the form field value
                                  handleShareholdingTypeChange(value) // Update state if needed
                                }}
                                value={field.value}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder='Select Shareholding Type' />
                                </SelectTrigger>
                                <SelectContent>
                                  {applicationType === 'Individual' && (
                                    <SelectItem value='Individual'>
                                      Individual
                                    </SelectItem>
                                  )}
                                  {applicationType === 'Corporate' && (
                                    <SelectItem value='Corporate'>
                                      Corporate
                                    </SelectItem>
                                  )}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Display Note if Corporate is selected */}
                      {form.watch('shareholdingType') === 'Corporate' && (
                        <>
                          <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/40 mt-6'>
                            <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                              <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                            </span>
                            <div className='flex flex-col ml-2'>
                              <AlertDescription>
                                <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                  Corporate shareholder type means company is
                                  owned by a corporate company or a number of
                                  corporate companies.
                                </p>
                                <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                  Proposed share capital can be up to AED
                                  150,000. If anything above AED 150,000, we
                                  require a bank statement with proof of the
                                  funds.
                                </p>

                                <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                  {' '}
                                  Up to 5 corporate shareholders are allowed for
                                  the application. If you are looking to have
                                  more than 5 corporate shareholders, please get
                                  in touch with your Client Engagement Manager.{' '}
                                </p>
                              </AlertDescription>
                            </div>
                          </Alert>
                        </>
                      )}

                      {/* Display Note if Individual is selected */}
                      {form.watch('shareholdingType') === 'Individual' && (
                        <>
                          <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/40 mt-6'>
                            <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                              <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                            </span>
                            <div className='flex flex-col ml-2'>
                              <AlertDescription>
                                <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                  Individual shareholder type means company is
                                  owned by an individual or number of
                                  individuals (non-corporate).{' '}
                                </p>
                                <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                  Maximum Share Capital without paid share
                                  capital letter is AED 150,000, Any amount
                                  above AED 150,000 will need a Share Capital
                                  letter from the bank.
                                </p>
                                <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                  Recommended Share capital per shareholder is
                                  AED 50,000/- for any IFZA company if you
                                  intend to apply for Partner or investor visa.
                                </p>

                                <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                  {' '}
                                  Up to 10 shareholders are allowed for the
                                  application. If you are looking to have more
                                  than 10 shareholders, please get in touch with
                                  your Client Engagement Manager​{' '}
                                </p>
                              </AlertDescription>
                            </div>
                          </Alert>
                        </>
                      )}

                      {/* Number of Shareholders */}

                      <FormField
                        control={form.control}
                        name='numberOfShareholders'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Number of Shareholders{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                type='number'
                                placeholder='Enter the number of shareholders'
                                onBlur={() =>
                                  calculateShareCapitalperShareholder(form)
                                } // Call the function onBlur
                                onChange={(e) => {
                                  const value =
                                    e.target.value === ''
                                      ? undefined
                                      : +e.target.value
                                  field.onChange(value)
                                }}
                              />
                            </FormControl>
                            <FormDescription>
                              Up to 15 shareholders are allowed for the
                              application. If you are looking to have more than
                              10 shareholders, please get in touch with your
                              Client Engagement Manager
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Proposed Share Capital */}

                      <FormField
                        control={form.control}
                        name='proposedShareCapital'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Proposed Share Capital{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                type='number'
                                placeholder='Enter the proposed share capital'
                                onBlur={() => {
                                  handleShareCapitalBlur() // Call the function to check the conditions
                                  calculateTotalNumberOfShares(form)
                                  calculateShareCapitalperShareholder(form)
                                }}
                                onChange={(e) => {
                                  const value =
                                    e.target.value === ''
                                      ? undefined
                                      : +e.target.value
                                  field.onChange(value)
                                }}
                              />
                            </FormControl>
                            <FormDescription>
                              Recommended Share capital per shareholder is AED
                              50,000/- for any IFZA company. Minimum share
                              capital per shareholder is AED10,000/- for any
                              IFZA company
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Share Value */}
                      <FormField
                        control={form.control}
                        name='shareValue'
                        rules={{
                          required: 'Share Value is required.',
                          min: {
                            value: 10,
                            message: 'Share Value must be at least 10 AED.',
                          },
                        }}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Share Value{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                type='number'
                                placeholder='Enter the share value'
                                onBlur={() =>
                                  calculateTotalNumberOfShares(form)
                                } // Call the function onBlur
                                onChange={(e) => {
                                  const value =
                                    e.target.value === ''
                                      ? undefined
                                      : +e.target.value
                                  field.onChange(value)
                                }}
                              />
                            </FormControl>
                            <FormDescription>
                              Please note the minimum share value per share is
                              AED 10.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Total Number of Shares (Calculated automatically) */}
                      <FormField
                        control={form.control}
                        name='totalNumberOfShares'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Total Number of Shares</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                value={form.watch('totalNumberOfShares')}
                                readOnly
                                placeholder='This field is calculated automatically based on Proposed Share Capital and Share Value.'
                                disabled
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      {/* Share Capital per Shareholder */}

                      {/* <FormField
                    control={form.control}
                    name='shareCapitalPerShareholder'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Share Capital per Shareholder</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder='This field is calculated automatically based on Proposed Share Capital and Number of Shareholders.'
                            disabled
                          />
                        </FormControl>
                        <FormDescription>
                          This a suggested share capital calculation and can be
                          changed at a later stage, should you wish to allocate
                          the share percentage differently.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  /> */}

                      {/* Are the shareholder(s) of the existing company in IFZA? */}
                      {form.watch('shareholdingType') === 'Corporate' && (
                        <>
                          <FormField
                            control={form.control}
                            name='shareholderInIFZA'
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>
                                  Are the shareholder(s) of the existing company
                                  in IFZA ?
                                </FormLabel>
                                <FormControl>
                                  <Select
                                    onValueChange={field.onChange}
                                    value={field.value}
                                  >
                                    <SelectTrigger>
                                      <SelectValue placeholder='Select ' />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value='Yes'>Yes</SelectItem>
                                      <SelectItem value='No'>No</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </>
                      )}

                      {/* Show the note if 'Shareholding Type' is 'Individual' and 'Proposed Share Capital' is less than 48,000 */}
                      {showNote && (
                        <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20 mt-6 mb-4'>
                          <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                            <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                          </span>
                          <div className='flex flex-col ml-2'>
                            <AlertDescription>
                              <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                Please note, that the chosen capital per
                                shareholder is less than AED 48,000.{' '}
                              </p>
                              <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                For shareholders to obtain a Partner or Investor
                                Visa, minimum share capital required per
                                shareholder is AED 48,000. In this case an
                                attested degree is not required.
                              </p>
                              <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                For any shareholders to obtain managerial or
                                above occupation title on their UAE Residence &
                                Employment Visa, Attested Degree (Attested by
                                UAE Embassy of the issuing country and Ministry
                                of Foreign Affairs in UAE) will be required as
                                per the UAE immigration rules, if the share
                                capital is less than AED 48,000 per shareholder.
                              </p>
                            </AlertDescription>
                          </div>
                        </Alert>
                      )}
                    </>
                  )}
                </form>
              </Form>
            </CardContent>
          </Card>
        )}
        {/* === Step 3 Content === */}
        {currentStep === 3 && (
          <Card className='mt-6'>
            <CardHeader>
              <CardTitle>Add Company Members ( Step 5 of 6 )</CardTitle>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form className='space-y-4 fz-form'>
                  <CompanyMembers members={members} setMembers={setMembers} />
                </form>
              </Form>
            </CardContent>
          </Card>
        )}

        {/* === Step 4 Content === */}
        {currentStep === 4 && (
          <Card className='mt-6'>
            <CardHeader>
              <CardTitle>Ultimate Beneficial Ownership (Step 4 of 4)</CardTitle>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form className='space-y-4 fz-form'>
                  {/* Alert */}
                  <Alert className='border-dashed border-primary mt-2 mb-2 alert-bg-warning dark:bg-primary/40'>
                    <AlertDescription>
                      This declaration is made in accordance with UAE Cabinet
                      Resolution No. 58 of 2020 Concerning Procedures for
                      Regulating Ultimate Beneficial Ownership (the “UBO
                      Decision”).
                    </AlertDescription>
                  </Alert>

                  {/* Company Statement */}

                  <FormField
                    control={form.control}
                    name='companyStatementValue'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          1. The company agrees to the statement below :{' '}
                          <span className='text-red-500'>*</span>
                        </FormLabel>
                        <FormControl>
                          <RadioGroup
                            className='flex align-center'
                            value={field.value}
                            onValueChange={field.onChange}
                          >
                            <RadioGroupItem
                              className='mt-1'
                              value='Yes'
                              id='yes'
                            />
                            <label htmlFor='yes'>Yes</label>

                            <RadioGroupItem
                              className='mt-1'
                              value='No'
                              id='no'
                            />
                            <label htmlFor='no'>No</label>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Alert */}
                  <Alert className='border-dashed border-primary mt-4 alert-bg-warning dark:bg-primary/40'>
                    <AlertDescription>
                      The Licensee certifies that the Company Member(s)
                      (shareholder(s)) described in the License Application Form
                      is/are the UBO of the Licensee and if there is more than
                      one Company Member, then the Company Members are the UBO
                      in the same proportion as their shareholdings.{' '}
                    </AlertDescription>
                  </Alert>

                  {/* when "No" is selected */}
                  {form.watch('companyStatementValue') === 'No' && (
                    <>
                      <h3 className='text-xl mt-4 mb-4 font-semibold'>OR</h3>
                      <FormField
                        control={form.control}
                        name='agreement'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              2. The company agrees to the statement below :
                            </FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder='Select' />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value='Yes'>Yes</SelectItem>
                              </SelectContent>
                            </Select>

                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Alert */}
                      <Alert className='border-dashed border-primary mt-4 alert-bg-warning dark:bg-primary/40'>
                        <AlertDescription>
                          The Licensee certifies that the following person(s)
                          is/are the UBO of the Licensee. The full details of
                          the UBO are set out below.
                        </AlertDescription>
                      </Alert>

                      {/*Add UBO(s) */}

                      <h3 className='text-xl mt-4 mb-4 font-semibold'>
                        Add UBO(s)
                      </h3>
                      <AddUBO />
                    </>
                  )}

                  <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                    <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                      <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                    </span>
                    <div className='flex flex-col ml-2'>
                      <AlertDescription>
                        <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                          We hereby declare that the information provided in
                          this declaration is true and accurate and if such
                          information changes, we will promptly notify
                          International Free Zone Authority FZCO (“IFZA”).
                        </p>
                        <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                          We confirm that if any of the UBO information should
                          change, we will file an amended declaration within 15
                          days of becoming aware of such change in accordance
                          with Article 8(1) and Article 10(1) of the UBO
                          Decision.
                        </p>

                        <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                          We acknowledge that if any information provided by
                          me/us is subsequently found to be untrue, inaccurate
                          or misleading, IFZA may suspend or terminate our
                          license. We hereby authorize IFZA to make any
                          enquiries from any person or entity, it may deem
                          necessary in connection with this declaration.{' '}
                        </p>
                        <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                          {' '}
                          We shall maintain a Register of Beneficial Owners that
                          the Licensee is required to maintain in accordance
                          with Article 8 of the UBO Decision.{' '}
                        </p>

                        <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                          {' '}
                          We shall maintain a Register of Partners or
                          Shareholders that the Licensee is required to maintain
                          in accordance with Article 10 of the UBO Decision.
                        </p>
                        <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                          We authorize IFZA to transmit this UBO Information, in
                          such form as IFZA shall determine, to the concerned
                          regulatory authorities as set out in the UBO Decision.{' '}
                        </p>

                        <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                          {' '}
                          The signatory to this document has all necessary
                          authority to provide this declaration for and on
                          behalf of the Licensee.{' '}
                        </p>
                      </AlertDescription>
                    </div>
                  </Alert>

                  {/* UBO Declaration */}

                  <FormField
                    control={form.control}
                    name='declaration'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          UBO Declaration{' '}
                          <span className='text-red-500'>*</span>
                        </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder='Select' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value='Yes, we agree to the statement above.'>
                              Yes, we agree to the statement above.
                            </SelectItem>
                          </SelectContent>
                        </Select>

                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Alert */}
                  <Alert className='border-dashed border-primary mt-4 mb-4 alert-bg-warning dark:bg-primary/40'>
                    <AlertDescription>
                      I, the General Manager, confirm that the information given
                      in the form is true, complete and accurate. The
                      authorization is irrevocable until a written notification
                      is submitted to International Free Zone Authority (IFZA).
                    </AlertDescription>
                  </Alert>

                  {/* Confirmation */}

                  <FormField
                    control={form.control}
                    name='confirmation'
                    render={({ field }) => (
                      <FormItem className='mb-4'>
                        <FormLabel>
                          Confirmation <span className='text-red-500'>*</span>
                        </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder='Select' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value='Yes, Agreed'>
                              Yes, Agreed
                            </SelectItem>
                          </SelectContent>
                        </Select>

                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* General Manager/Authorized Signatory Name */}
                  <FormField
                    control={form.control}
                    name='authorizedSignatoryName'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          General Manager/Authorized Signatory Name{' '}
                          <span className='text-red-500'>*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            value={field.value || ''}
                            disabled
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* General Manager/Authorized Signatory Email */}
                  <FormField
                    control={form.control}
                    name='authorizedSignatoryEmail'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          General Manager/Authorized Signatory Email{' '}
                          <span className='text-red-500'>*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            type='email'
                            {...field}
                            value={field.value || ''}
                            disabled
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </form>
              </Form>
            </CardContent>
          </Card>
        )}

        {/* Buttons Section */}

        <div className={externalId
            ? 'fixed bottom-0 left-0 right-0 p-4 bg-background flex justify-center items-center space-x-4 z-10 fz-form-btns'
            : 'fixed bottom-0 right-0 pl-6 pr-6 pb-3 pt-4 bg-background flex justify-end space-x-2 z-10 fz-form-btns'
          }>
            <Button variant={'btn_outline'}>Save as Draft</Button>
          <div className='flex gap-2'>
            {/* The 'Previous' button will not appear on the first step */}
            {currentStep !== 1 && (
              <Button variant='btn_outline' onClick={prevStep}>
                Previous
              </Button>
            )}

            {/* The 'Next' button will not appear on the last step */}
            {currentStep !== 4 && (
              <Button variant='default' onClick={nextStep}>
                Next
              </Button>
            )}
            {/* The Submit button will appear only in step 6 */}
            {currentStep === 4 && (
              <Button
                variant={'default'}
                onClick={() => {
                  onSubmit(form.getValues())
                }}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                    Processing...
                  </>
                ) : (
                  'Submit'
                )}
              </Button>
            )}
          </div>
        </div>
      </Main>
    </>
  )
}