import { z } from 'zod'

export const applicationFormSchema = z.object({
  appUpdatesEmail: z.string().email({ message: 'Invalid email address.' }),
  whatsAppNumber: z.string().optional(),
  applicationDate: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine((date) => !isNaN(date.getTime()), {
      message: 'Invalid date format',
    }),

  visaType: z.string().min(1, { message: 'Select a choice.' }),
  tradeLicenseValidated: z.boolean().refine((val) => val === true, {
    message:
      'You must confirm the trade license is valid for at least 60 days.',
  }),
  visaApplicationType: z.string().min(1, { message: 'Select a choice.' }),
  nationality: z.string().min(1, { message: 'Select a choice.' }),
  residentVisaStamping: z.string().min(1, { message: 'Select a choice.' }),
  familyOnHoldLetter: z.string().optional(),
  renewalDisclaimer: z
    .boolean()
    .refine((val) => val === true, {
      message: 'Select this option.',
    })
    .optional(),
  visaFree: z.string().min(1, { message: 'Select a choice.' }).optional(),

  outsideCountryInstructions: z
    .boolean()
    .refine((val) => val === true, {
      message: 'You must confirm you will be outside UAE.',
    })
    .optional(),

  outsideCountry: z
    .boolean()
    .refine((val) => val === true, {
      message: 'You must confirm you have no active visa in UAE.',
    })
    .optional(),
  updatedRVCopy: z
    .any()
    .refine((val) => val instanceof File && val.size > 0, {
      message: 'Upload a file here.',
    })
    .optional(),
  idCardUpload: z
    .any()
    .refine((val) => val instanceof File && val.size > 0, {
      message: 'Upload a file here.',
    })
    .optional(),

  currentVisaStatus: z
    .string()
    .min(1, { message: 'Select a choice.' })
    .optional(),
  visaValidUntil: z.date().optional(),
  title: z.string().min(1, { message: '*' }),
  firstName: z.string().min(2, { message: 'Required.' }),
  middleName: z.string().optional(),
  lastName: z.string().min(2, { message: 'Required.' }),
  title1: z.string().optional(),
  arabicName: z.string().min(1, { message: 'Select a choice.' }),
  firstNameArabic: z.string().optional(),
  middleNameArabic: z.string().optional(),
  lastNameArabic: z.string().optional(),
  emailAddress: z.string().email({ message: 'Invalid email address.' }),
  phone: z.string().min(5, { message: 'You must enter at least 5 digits.' }),
  streetAddress: z.string().optional(),
  addressLine: z.string().optional(),
  cityAddress: z.string().optional(),
  stateProvince: z.string().optional(),
  country: z.string().optional(),
  streetAddress1: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  addressLine2: z.string().optional(),
  city: z.string().min(2, { message: 'Enter a value for this field.' }),
  province: z.string().optional(),
  country1: z.string().min(1, { message: 'Select a choice.' }),

  emiratesID: z.string().min(1, { message: 'Select a choice.' }),
  emiratesIDNumber: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  emiratesIDExpiryDate: z.date().optional(),
  emiratesIDCopy: z
    .any()
    .refine((val) => val instanceof File && val.size > 0, {
      message: 'Upload a file here.',
    })
    .optional(),

  nationality1: z.string().min(1, { message: 'Select a choice.' }),
  passportNumber: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  placeOfIssue: z.string().min(2, { message: 'Enter a value for this field.' }),
  placeOfIssueArabic: z.string().optional(),
  passportType: z.string().min(1, { message: 'Select a choice.' }),
  agreementPassportRules: z.boolean().refine((val) => val === true, {
    message: 'You must accept the terms and conditions.',
  }),

  eVisaApplicationType: z
    .any()
    // .min(1, { message: 'Select a choice.' })
    .optional(),

  Agreed: z
    .boolean()
    .refine((val) => val === true, {
      message: 'Choose this option.',
    })
    .optional(),
  countryOfIssuance: z.string().min(1, { message: 'Select a choice.' }),
  coloredPassport: z
    .any()
    .refine((val) => val instanceof File && val.size > 0, {
      message: 'Upload a file here.',
    }),
  coloredPassport2: z
    .any()
    .refine((val) => val instanceof File && val.size > 0, {
      message: 'Upload a file here.',
    }),
  passportIssueDate: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine(
      (date) => {
        const today = new Date() // Get today's date
        return date <= today // Validate if the date is not in the future
      },
      {
        message: "Passport Issuing date can't be in the future", // Custom error message
      }
    ),
  passportExpiryDate: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine(
      (date) => {
        const today = new Date()
        const expiryDate = new Date(date)

        // Calculate the difference between the expiry date and today's date
        const diffInTime = expiryDate.getTime() - today.getTime()
        const diffInDays = diffInTime / (1000 * 3600 * 24) // Convert from milliseconds to days

        // Ensure the passport expiry date is at least 210 days in the future (7 months)
        return diffInDays >= 210
      },
      {
        message: "Passport Expiry Date can't be less than 7 months", // Custom error message
      }
    ),
  cityOfBirth: z.string().min(2, { message: 'Enter a value for this field.' }),
  cityOfBirthArabic: z.string().optional(),
  countryOfBirth: z.string().min(1, { message: 'Select a choice.' }),
  dateOfBirth: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine((date) => !isNaN(date.getTime()), {
      message: 'Invalid date format',
    }),
  gender: z.string().min(1, { message: 'Select a choice.' }),
  previousNationality: z.string().optional(),
  maritalStatus: z.string().min(1, { message: 'Select a choice.' }),
  religion: z.string().min(1, { message: 'Select a choice.' }),
  religionSubCategory: z
    .string()
    .min(1, { message: 'Select a choice.' })
    .optional(),
  fatherFullName: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  motherFullName: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  motherFullNameArabic: z.string().optional(),
  photoOfApplicant: z
    .any()
    .refine((val) => val instanceof File && val.size > 0, {
      message: 'Upload a file here.',
    }),

  visaApplicantFiles: z
    .any()
    .refine((val) => val instanceof File && val.size > 0, {
      message: 'Upload a file here.',
    }),

  typeOfEmployment: z.string().optional(),
  employmentDuration: z
    .number()
    .min(0, { message: 'Enter a value greater than or equal to 0.' }) // Validate min value
    .max(24, { message: 'Enter a value less than or equal to 24.' }), // Validate max value
  jobTitleChange: z.string().optional(),
  jobTitle: z.string().min(1, { message: 'Select a choice.' }),
  educationQualification: z.string().min(1, { message: 'Select a choice.' }),
  employmentStartDate: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine((date) => !isNaN(date.getTime()), {
      message: 'Invalid date format',
    }),
  probationPeriod: z.string().min(1, { message: 'Select a choice.' }),
  employmentTerminationNotice: z
    .string()
    .min(1, { message: 'Select a choice.' }),
  returnTicket: z.string().min(1, { message: 'Select a choice.' }),
  ticketEntitlementPeriod: z
    .string()
    .min(1, { message: 'Select a choice.' })
    .optional(),
  annualLeaveEntitlement: z.string().min(1, { message: 'Select a choice.' }),
  workingDays: z
    .number()
    .min(22, { message: 'Enter a value greater than or equal to 22.' }) // Validate min value
    .max(99, { message: 'Enter a value less than or equal to 99.' }) // Validate max value
    .optional(),
  calendarDays: z
    .number()
    .min(30, { message: 'Enter a value greater than or equal to 30.' }) // Validate min value
    .max(99, { message: 'Enter a value less than or equal to 99.' }) // Validate max value
    .optional(),
  salaryChange: z.string().optional(),
  basicSalary: z
    .number()
    .min(10, { message: 'You must enter at least 2 digits.' }) // Minimum 2 digits
    .max(9999999, { message: 'Maximum limit : 7 digits.' }), // Maximum 7 digits

  transportationAllowance: z.number().optional(),
  accommodationAllowance: z.number().optional(),
  otherAllowance: z.number().optional(),
  totalMonthlySalary: z.number().optional(),
  preferredPaymentMethod: z.string().min(1, { message: 'Select a choice.' }),
  visaFee: z.number().refine((val) => val === 3750, {
    message: 'Visa Fee must match the required amount : 3750',
  }), // Visa Fee must match the required amount: 3750.
  statusChange: z
    .number()
    .min(160, { message: 'Enter a value greater than or equal to 160.' }) // Validate min value
    .max(1600, { message: 'Enter a value less than or equal to 1600.' }), // Validate max value

  vipStampingFee: z.number().refine((val) => val === 1500, {
    message: 'VIP Stamping Fee must match the required amount : 1500',
  }),
  partnerInvestorVisa: z.number().refine((val) => val === 1000, {
    message: 'Partner/Investor Visa must match the required amount : 1000',
  }),
  totalAmountToBePaid: z.number().optional(),
  proofOfPayment: z.any().optional(),
  companyName: z.string().min(2, { message: 'Enter a value for this field.' }),
  tradeLicenseNumber: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  establishmentCardNumber: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  authorizedSignatory: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  emailAddressOfGeneralManager: z
    .string()
    .email({ message: 'Invalid email address.' }),
  termsAndConditions: z.boolean().refine((val) => val === true, {
    message: 'You must agree to the Terms & Conditions Agreement.',
  }),

  affirmInformation: z.boolean().refine((val) => val === true, {
    message: 'You must accept the terms and conditions.',
  }),
})