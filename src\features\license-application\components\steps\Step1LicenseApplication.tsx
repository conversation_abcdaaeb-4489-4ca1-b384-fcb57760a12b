import React from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { CountryDropdown } from '@/components/ui/country-dropdown';
import { DateTimePicker } from '@/components/ui/datetime-picker';
import PhoneInput from 'react-phone-input-2';
import { FilePenLine } from 'lucide-react';

interface CompanyInformationProps {
  form: any;
  loadingFields: any;
  setLoadingFields: any;
  apiScores: any;
  setApiScores: any;
  checkCompany: (name: string) => Promise<any>;
  transliterate: (name: string) => string;
  setSimilarNames: any;
  calculateSimilarity: (a: string, b: string) => number;
  currentStep: number;
}

const Step1LicenseApplication: React.FC<CompanyInformationProps> = ({
  form,
  loadingFields,
  setLoadingFields,
  apiScores,
  setApiScores,
  checkCompany,
  transliterate,
  setSimilarNames,
  calculateSimilarity,
  currentStep,
}) => {
  if (currentStep !== 1) return null;
  return (
    <Card className="mt-6">
      <CardContent>
        <Form {...form}>
          <form className="space-y-4 fz-form">
            <CardHeader className="px-0 pt-0 pb-0">
              <CardTitle>Company Information ( Step 1 of 6 )</CardTitle>
            </CardHeader>
            {/* Application Date */}
            <div className="flex flex-wrap space-between gap-y-4">
              <div className="w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0">
                <FormField
                  control={form.control}
                  name="applicationDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Application Date{' '}
                        <span className="text-red-500">*</span>
                      </FormLabel>
                      <DateTimePicker
                        granularity="day"
                        value={field.value || new Date()}
                        onChange={field.onChange}
                        displayFormat={{ hour24: 'dd MMMM yyyy' }}
                        disabled={true}
                      />
                      <FormDescription>dd-MMM-yyyy</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              {/* CEM field */}
              <div className="flex-1">
                <FormField
                  control={form.control}
                  name="cem"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>CEM</FormLabel>
                      <FormControl>
                        <Input {...field} value={field.value || ''} disabled={true} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            {/* How would you like to make payment for the application */}
            <div className="flex flex-wrap space-between gap-y-4">
              <div className="w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0">
                <FormField
                  control={form.control}
                  name="paymentType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        How would you like to make payment for the application{' '}
                        <span className="text-red-500">*</span>
                      </FormLabel>
                      <div data-field="paymentType">
                        <FormControl>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select payment type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Online Payment">Online Payment</SelectItem>
                              <SelectItem value="Bank Transfer">Bank transfer</SelectItem>
                              <SelectItem value="Cheque">Cheque</SelectItem>
                              <SelectItem value="Cash">Cash</SelectItem>
                              <SelectItem value="ATM">ATM</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              {/*Email Address */}
              <div className="flex-1">
                <FormField
                  control={form.control}
                  name="emailAddress"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Email Address{' '}
                        <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input type="email" {...field} />
                      </FormControl>
                      <FormDescription>
                        Please mention the email address where you would like to receive the copy of this form as well as the updates regarding the application and quotation.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            {/* App Updates WhatsApp Number */}
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>App Updates WhatsApp Number</FormLabel>
                  <PhoneInput
                    country={'ae'}
                    onChange={(phone) => field.onChange(phone)}
                    containerClass="fz-country-code flex h-9 w-full rounded-tl-[6px] rounded-bl-[6px] border border-input bg-transparent text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm"
                    inputClass=" flex  !h-9 !w-full rounded-tl-[6px] rounded-bl-[6px] border !border-transparent !bg-transparent px-3 text-base !shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm transform translate-y-[-2px]"
                    buttonClass="!h-100 !border-l-transparent !border-t-transparent !bg-slate-200 dark:!bg-slate-800 dark:!border-r-transparent !border-b-transparent !border-gray-200 !bg-transparent !rounded-tl-[8px] !rounded-bl-[6px]"
                    enableSearch={true}
                    searchPlaceholder="Search country..."
                  />
                  <FormDescription>
                    Please mention the mobile number where you would like to receive the copy of this form as well as the updates regarding the application and quotation.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <CardHeader className="px-0 pt-4 pb-0">
              <CardTitle>Set Your Company Name</CardTitle>
            </CardHeader>
            {/* Country of Operation (Multi-select) */}
            {/* Note Alert */}
            <Alert className="flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20">
              <span className="bg-primary/10 p-2 rounded inline-block dark:text-primary-dark">
                <FilePenLine className="w-4 h-4 stroke-primary dark:text-primary-dark" />
              </span>
              <div className="flex flex-col ml-2">
                <AlertTitle>Note : </AlertTitle>
                <AlertDescription>
                  <p className="text-sm text-slate-700 dark:text-slate-400 relative before:absolute before:left-0 before:top-0">
                    Please make sure that you provide three different company name options listed in order of priority. Your proposed company name should be written exactly the way you want it to appear. We will check the availability of the options provided in order. For example, if the first name is not available, we will check the second and then the third. The company will be automatically incorporated with the available name, in order of the options provided. We will only revert to applicants if none of the names provided are available.
                  </p>
                </AlertDescription>
              </div>
            </Alert>
            <AlertDescription>
              <p className="text-sm text-slate-700 dark:text-slate-400 relative before:absolute before:left-0 before:top-0">
                {' '}
                Please refer to the company guidelines{' '}
                <a
                  href="https://cdn.ifza.com/docs/01_new/references/companyguidelines_190821.pdf"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 underline"
                >
                  here
                </a>
                .
              </p>
            </AlertDescription>
            <FormField
              control={form.control}
              name="countryOfOperation"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Country of Operation{' '}
                    <span className="text-red-500">*</span>
                  </FormLabel>
                  <div data-field="countryOfOperation">
                    <FormControl>
                      <CountryDropdown
                        multiple
                        placeholder="Country"
                        defaultValue={field.value as string[]}
                        onChange={(countries) => {
                          const codes = countries.map((c) => c.alpha3);
                          setTimeout(() => {
                            field.onChange(codes);
                          }, 0);
                        }}
                      />
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Company Name Options Group */}
            <div className="border border-warning p-4 mt-6 rounded-md alert-bg-warning dark:bg-primary/20">
              <h3 className="text-base font-semibold text-primary dark:text-primary-light mb-4">
                Company Name Options
              </h3>
              {/* Option 1 - English */}
              <div className="flex flex-wrap space-between gap-y-4">
                <div className="w-full sm:w-[49%] sm:mr-[1%] mb-6">
                  <FormField
                    control={form.control}
                    name="option1English"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Option 1 - English</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="Enter the Option 1 - English Name"
                            onBlur={async () => {
                              const englishValue = field.value?.trim() || '';
                              if (!englishValue) return;
                              const key = field.name.replace('English', '').toLowerCase();
                              setLoadingFields((prev: any) => ({ ...prev, [key]: true }));
                              try {
                                const res = await checkCompany(englishValue);
                                if (!res.valid) {
                                  form.setError(field.name as any, { message: res.message });
                                } else {
                                  form.clearErrors(field.name as any);
                                  form.setValue(field.name.replace('English', 'Arabic') as any, transliterate(englishValue));
                                }
                                setApiScores((prev: any) => ({ ...prev, [key]: res.score }));
                                const filteredSimilar = (res.similar || []).filter((item: any) => item.score >= 80);
                                setSimilarNames((prev: any) => ({ ...prev, [key]: filteredSimilar }));
                              } catch (err) {
                                // handle error
                              } finally {
                                setLoadingFields((prev: any) => ({ ...prev, [key]: false }));
                              }
                            }}
                          />
                        </FormControl>
                        {form.formState.errors.option1English && <FormMessage />}
                        <div className="min-h-[24px] mt-1">
                          {loadingFields.option1 ? (
                            <div className="flex items-center gap-2 text-primary dark:text-primary-dark text-sm">
                              <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="#c7a51f" strokeWidth="4" fill="none" />
                                <path className="opacity-75" fill="#c7a51f" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z" />
                              </svg>
                              <span>Checking...</span>
                            </div>
                          ) : (
                            field.value?.trim() && apiScores.option1 >= 80 && apiScores.option1 < 100 && (
                              <p className="text-sm text-primary dark:text-primary-light">
                                Option 1 matched {apiScores.option1}% with existing company name.
                              </p>
                            )
                          )}
                        </div>
                      </FormItem>
                    )}
                  />
                </div>
                {/* Option 1 - Arabic */}
                <div className="flex-1">
                  <FormField
                    control={form.control}
                    name="option1Arabic"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Option 1 - Arabic</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="Auto-populated by default Arabic translation"
                          />
                        </FormControl>
                        <FormDescription>
                          If left blank, IFZA will automatically provide a system generated Arabic translation of your company name.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              {/* Option 2 - English/Arabic */}
              <div className="flex flex-wrap space-between gap-y-4">
                <div className="w-full sm:w-[49%] sm:mr-[1%] mb-6">
                  <FormField
                    control={form.control}
                    name="option2English"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Option 2 - English</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="Enter the Option 2 - English Name"
                            onBlur={async () => {
                              const englishValue = field.value?.trim() || '';
                              if (!englishValue) return;
                              const key = field.name.replace('English', '').toLowerCase();
                              setLoadingFields((prev: any) => ({ ...prev, [key]: true }));
                              try {
                                const res = await checkCompany(englishValue);
                                if (!res.valid) {
                                  form.setError(field.name as any, { message: res.message });
                                } else {
                                  form.clearErrors(field.name as any);
                                  form.setValue(field.name.replace('English', 'Arabic') as any, transliterate(englishValue));
                                }
                                setApiScores((prev: any) => ({ ...prev, [key]: res.score }));
                                const filteredSimilar = (res.similar || []).filter((item: any) => item.score >= 80);
                                setSimilarNames((prev: any) => ({ ...prev, [key]: filteredSimilar }));
                              } catch (err) {
                                // handle error
                              } finally {
                                setLoadingFields((prev: any) => ({ ...prev, [key]: false }));
                              }
                            }}
                          />
                        </FormControl>
                        {form.formState.errors.option2English && <FormMessage />}
                        <div className="min-h-[48px] mt-1 space-y-1">
                          {loadingFields.option2 ? (
                            <div className="flex items-center gap-2 text-primary dark:text-primary-dark text-sm">
                              <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="#c7a51f" strokeWidth="4" fill="none" />
                                <path className="opacity-75" fill="#c7a51f" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z" />
                              </svg>
                              <span>Checking...</span>
                            </div>
                          ) : (
                            <>
                              {field.value?.trim() && apiScores.option2 >= 80 && apiScores.option2 < 100 && (
                                <p className="text-sm text-primary dark:text-primary-light">
                                  Option 2 matched {apiScores.option2}% with existing company name.
                                </p>
                              )}
                              {form.getValues('option1English')?.trim() && (
                                <p className="text-sm text-primary dark:text-primary-light">
                                  Option 2 matched {calculateSimilarity(field.value.trim(), form.getValues('option1English').trim())}% with Option 1.
                                </p>
                              )}
                            </>
                          )}
                        </div>
                      </FormItem>
                    )}
                  />
                </div>
                {/* Option 2 - Arabic */}
                <div className="flex-1">
                  <FormField
                    control={form.control}
                    name="option2Arabic"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Option 2 - Arabic</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="Auto-populated by default Arabic translation"
                          />
                        </FormControl>
                        <FormDescription>
                          If left blank, IFZA will automatically provide a system generated Arabic translation of your company name.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              {/* Option 3 - English/Arabic */}
              <div className="flex flex-wrap space-between gap-y-4">
                <div className="w-full sm:w-[49%] sm:mr-[1%] mb-6">
                  <FormField
                    control={form.control}
                    name="option3English"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Option 3 - English</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="Enter the Option 3 - English Name"
                            onBlur={async () => {
                              const englishValue = field.value?.trim() || '';
                              if (!englishValue) return;
                              const key = field.name.replace('English', '').toLowerCase();
                              setLoadingFields((prev: any) => ({ ...prev, [key]: true }));
                              try {
                                const res = await checkCompany(englishValue);
                                if (!res.valid) {
                                  form.setError(field.name as any, { message: res.message });
                                } else {
                                  form.clearErrors(field.name as any);
                                  form.setValue(field.name.replace('English', 'Arabic') as any, transliterate(englishValue));
                                }
                                setApiScores((prev: any) => ({ ...prev, [key]: res.score }));
                                const filteredSimilar = (res.similar || []).filter((item: any) => item.score >= 80);
                                setSimilarNames((prev: any) => ({ ...prev, [key]: filteredSimilar }));
                              } catch (err) {
                                // handle error
                              } finally {
                                setLoadingFields((prev: any) => ({ ...prev, [key]: false }));
                              }
                            }}
                          />
                        </FormControl>
                        {form.formState.errors.option3English && <FormMessage />}
                        <div className="min-h-[72px] mt-1 space-y-1">
                          {loadingFields.option3 ? (
                            <div className="flex items-center gap-2 text-primary dark:text-primary-dark text-sm">
                              <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="#c7a51f" strokeWidth="4" fill="none" />
                                <path className="opacity-75" fill="#c7a51f" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z" />
                              </svg>
                              <span>Checking...</span>
                            </div>
                          ) : (
                            <>
                              {field.value?.trim() && apiScores.option3 >= 80 && apiScores.option3 < 100 && (
                                <p className="text-sm text-primary dark:text-primary-light">
                                  Option 3 matched {apiScores.option3}% with existing company name.
                                </p>
                              )}
                              {form.getValues('option1English')?.trim() && (
                                <p className="text-sm text-primary dark:text-primary-light">
                                  Option 3 matched {calculateSimilarity(field.value.trim(), form.getValues('option1English').trim())}% with Option 1.
                                </p>
                              )}
                              {form.getValues('option2English')?.trim() && (
                                <p className="text-sm text-primary dark:text-primary-light">
                                  Option 3 matched {calculateSimilarity(field.value.trim(), form.getValues('option2English').trim())}% with Option 2.
                                </p>
                              )}
                            </>
                          )}
                        </div>
                      </FormItem>
                    )}
                  />
                </div>
                {/* Option 3 - Arabic */}
                <div className="flex-1">
                  <FormField
                    control={form.control}
                    name="option3Arabic"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Option 3 - Arabic</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="Auto-populated by default Arabic translation"
                          />
                        </FormControl>
                        <FormDescription>
                          If left blank, IFZA will automatically provide a system generated Arabic translation of your company name.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>
{/* Agreement to the Terms & Conditions */}

<FormField
                    control={form.control}
                    name='agreementTerms'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Agreement to the Terms & Conditions{' '}
                          <span className='text-red-500'>*</span>
                        </FormLabel>
                         <div data-field='agreementTerms'>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder='Select' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value='I, hereby confirm that I have read and accept the Terms & Conditions'>
                              I, hereby confirm that I have read and accept the Terms & Conditions
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        </div>

                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Alert className='border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                    <AlertDescription>
                      Terms & Conditions are available{' '}
                      <a
                        href='https://files.ifza.com/external/92561fb670f9e384622387016a0d266b04d1eeeb68011885c30444762e0d674c'
                        target='_blank'
                        rel='noopener noreferrer'
                        className='text-blue-500 underline'
                      >
                        here
                      </a>
                    </AlertDescription>
                  </Alert>
            
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default Step1LicenseApplication;
