import React from 'react'
import { useFormContext, useWatch } from 'react-hook-form'
import { FilePenLine } from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Checkbox } from '@/components/ui/checkbox'
import { CountryDropdown } from '@/components/ui/country-dropdown'
import { DateTimePicker } from '@/components/ui/datetime-picker'
import FileUploadField from '@/components/ui/fileUpload'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { ApplicationFormValues } from '../../types/application-form-types'

interface Step3FormProps {
  setDate: React.Dispatch<React.SetStateAction<Date | undefined>>
}

const Step3VisaApplication: React.FC<Step3FormProps> = ({ setDate }) => {
  const form = useFormContext<ApplicationFormValues>()
  const nationality = useWatch({ control: form.control, name: 'nationality' })
  const countryOfIssuance = useWatch({ control: form.control, name: 'countryOfIssuance' })
  const religion = useWatch({ control: form.control, name: 'religion' })

  return (
    <>
    <form className="space-y-4 fz-form">
      <div className='flex flex-wrap space-between gap-y-4'>
        <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
          <FormField
            control={form.control}
            name='nationality1'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Nationality{' '}
                  <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <CountryDropdown
                    placeholder='Country'
                    defaultValue={field.value as string}
                    onChange={(c) => field.onChange(c.alpha3)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className='flex-1'>
          <FormField
            control={form.control}
            name='arabicName'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Do you have Arabic Name in your Passport ?{' '}
                  <span style={{ color: 'red' }}>*</span>
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select ' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='Yes'>Yes</SelectItem>
                    <SelectItem value='No'>No</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      <FormField
        control={form.control}
        name='passportNumber'
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              Passport Number{' '}
              <span className='text-red-500'>*</span>
            </FormLabel>
            <FormControl>
              <Input
                {...field}
                placeholder='Enter Passport Number'
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className='flex flex-wrap space-between gap-y-4'>
        <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
          <FormField
            control={form.control}
            name='placeOfIssue'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Place of Issue{' '}
                  <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder='Enter Place of Issue'
                  />
                </FormControl>
                <FormDescription>
                  Issuing place of the passport
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className='flex-1'>
          <FormField
            control={form.control}
            name='placeOfIssueArabic'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Place of Issue (Arabic)</FormLabel>
                <FormControl>
                  <Input {...field} placeholder='Enter' />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      <FormField
        control={form.control}
        name='passportType'
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              Passport Type{' '}
              <span className='text-red-500'>*</span>
            </FormLabel>
            <FormControl>
              <Select
                disabled={
                  nationality !== 'SYR' && nationality !== 'LBN'
                }
                value={field.value}
                onValueChange={(value) => field.onChange(value)}
              >
                <SelectTrigger
                  className={
                    nationality !== 'SYR' && nationality !== 'LBN'
                      ? 'bg-gray-100 cursor-not-allowed'
                      : ''
                  }
                >
                  <SelectValue placeholder='Select' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='Ordinary'>
                    Ordinary
                  </SelectItem>
                  {(nationality === 'SYR' ||
                    nationality === 'LBN') && (
                    <SelectItem value='Travel Document'>
                      Travel Document
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
            </FormControl>
            <FormDescription>
              Please note only Ordinary Passports are accepted for
              Visas, it is not allowed to apply for Visas on
              Diplomatic passports.
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
        <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
          <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
        </span>
        <div className='flex flex-col ml-2'>
          <AlertTitle>
            You agree to the statement below by checking the box :
          </AlertTitle>

          <AlertDescription>
            <p className="text-sm dark:text-red-500 pl-4 relative before:content-['1.'] before:absolute before:left-0 before:top-0">
              The passport provided is a normal/ordinary passport.
            </p>
            <p className="text-sm dark:text-red-500 pl-4 relative before:content-['2.'] before:absolute before:left-0 before:top-0">
              It is not a travel document, provisional, and/or
              diplomatic passport.
            </p>
            <p className="text-sm dark:text-red-500 pl-4 relative before:content-['3.'] before:absolute before:left-0 before:top-0">
              It is the responsibility of the partner/client to
              ensure that the correct passport is provided at the
              time of application.
            </p>
            <p className="text-sm dark:text-red-500 pl-4 relative before:content-['4.'] before:absolute before:left-0 before:top-0">
              Any Visa(s) delayed or rejected because of not
              complying to the statements above will be the sole
              responsibility of the partner/client.{' '}
            </p>
          </AlertDescription>
          <p className="text-sm dark:text-red-500 pl-4 relative before:content-['5.'] before:absolute before:left-0 before:top-0">
            No refunds/provisions will be possible in case of
            non-compliance.{' '}
          </p>
        </div>
      </Alert>

      <FormField
        control={form.control}
        name='agreementPassportRules'
        render={({ field }) => (
          <FormItem>
            <div className='flex w-full items-center justify-between'>
              <FormLabel className='mr-4'>
                Agreement to passport rules{' '}
                <span className='text-red-500'>*</span>
              </FormLabel>

              <div className='flex items-center justify-center flex-1 space-x-2'>
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={(checked: boolean) =>
                      field.onChange(checked)
                    }
                  />
                </FormControl>
                <FormLabel className='text-sm text-slate-700'>
                  Agreed
                </FormLabel>
              </div>
            </div>

            <FormMessage />
          </FormItem>
        )}
      />

      <div className='flex flex-wrap space-between gap-y-4'>
        <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
          <FormField
            control={form.control}
            name='countryOfIssuance'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Country of Issuance{' '}
                  <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <CountryDropdown
                    placeholder='Country'
                    defaultValue={field.value as string}
                    onChange={(c) => field.onChange(c.alpha3)}
                  />
                </FormControl>
                <FormDescription>
                  Issuing country of the passport
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className='flex-1'>
          <FormField
            control={form.control}
            name='coloredPassport'
            render={() => (
              <FormItem>
                <FormLabel>
                  Colored Passport Copy Page 1{' '}
                  <span style={{ color: 'red' }}>*</span>
                </FormLabel>
                <FormControl>
                  <FileUploadField
                    accept='.jpg,.jpeg'
                    onchoose={(file) => {
                      form.setValue('coloredPassport', file || '')
                      form.clearErrors('coloredPassport')
                    }}
                  />
                </FormControl>
                <FormDescription>
                  Please upload page 1 of your passport. We accept
                  only image/photo in the format of .jpg OR .jpeg
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
      {(countryOfIssuance === 'SYR' ||
        countryOfIssuance === 'IND' ||
        countryOfIssuance === 'TUR') && (
        <>
          <FormField
            control={form.control}
            name='coloredPassport2'
            render={() => (
              <FormItem>
                <FormLabel>
                  Colored Passport Copy Page 2{' '}
                  <span style={{ color: 'red' }}>*</span>
                </FormLabel>
                <FormControl>
                  <FileUploadField
                    accept='.jpg,.jpeg'
                    onchoose={(file) => {
                      form.setValue(
                        'coloredPassport2',
                        file || ''
                      )
                      form.clearErrors('coloredPassport2')
                    }}
                  />
                </FormControl>
                <FormDescription>
                  Please upload page 2 of your passport. We accept
                  only image/photo in the format of .jpg OR .jpeg
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </>
      )}

      <div className='flex flex-wrap space-between gap-y-4'>
        <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
          <FormField
            control={form.control}
            name='passportIssueDate'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Passport Issue Date{' '}
                  <span className='text-red-500'>*</span>
                </FormLabel>
                <DateTimePicker
                  granularity='day'
                  value={field.value}
                  onChange={(date) => {
                    field.onChange(date)
                    setDate(date)
                  }}
                  displayFormat={{
                    hour24: 'dd MMMM yyyy',
                  }}
                />
                <FormDescription>dd/MM/yyyy</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className='flex-1'>
          {' '}
          <FormField
            control={form.control}
            name='passportExpiryDate'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {' '}
                  Passport Expiry Date{' '}
                  <span className='text-red-500'>*</span>
                </FormLabel>
                <DateTimePicker
                  granularity='day'
                  value={field.value}
                  onChange={(date) => {
                    field.onChange(date)
                    setDate(date)
                  }}
                  displayFormat={{
                    hour24: 'dd MMMM yyyy',
                  }}
                />
                <FormMessage />
                <FormDescription>
                  dd/MM/yyyy <br />
                  Please note that your passport should be valid
                  for 7 months plus at the time of application.
                  The Visa stamping process may not be completed
                  otherwise.
                </FormDescription>
              </FormItem>
            )}
          />
        </div>
      </div>
      <div className='flex flex-wrap space-between gap-y-4'>
        <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
          {' '}
          <FormField
            control={form.control}
            name='cityOfBirth'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  City of Birth{' '}
                  <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder='Enter City of Birth'
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className='flex-1'>
          {' '}
          <FormField
            control={form.control}
            name='cityOfBirthArabic'
            render={({ field }) => (
              <FormItem>
                <FormLabel>City of Birth (Arabic)</FormLabel>
                <FormControl>
                  <Input {...field} placeholder='Enter' />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
      <div className='flex flex-wrap space-between gap-y-4'>
        <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
          {' '}
          <FormField
            control={form.control}
            name='countryOfBirth'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Country of Birth{' '}
                  <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <CountryDropdown
                    placeholder='Country'
                    defaultValue={field.value as string}
                    onChange={(c) => field.onChange(c.alpha3)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className='flex-1'>
          {' '}
          <FormField
            control={form.control}
            name='dateOfBirth'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {' '}
                  Date of Birth{' '}
                  <span className='text-red-500'>*</span>
                </FormLabel>
                <DateTimePicker
                  granularity='day'
                  value={field.value}
                  onChange={(date) => {
                    field.onChange(date)
                    setDate(date)
                  }}
                  displayFormat={{
                    hour24: 'dd MMMM yyyy',
                  }}
                />
                <FormDescription>dd/MM/yyyy</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
      <div className='flex flex-wrap space-between gap-y-4'>
        <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
          {' '}
          <FormField
            control={form.control}
            name='gender'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Gender <span className='text-red-500'>*</span>
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select gender' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='Male'>Male</SelectItem>
                    <SelectItem value='Female'>Female</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className='flex-1'>
          {' '}
          <FormField
            control={form.control}
            name='previousNationality'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {' '}
                  Previous Nationality (if any)
                </FormLabel>
                <FormControl>
                  <Input {...field} placeholder='Enter' />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
      <div className='flex flex-wrap space-between gap-y-4'>
        <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
          {' '}
          <FormField
            control={form.control}
            name='maritalStatus'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Marital Status{' '}
                  <span className='text-red-500'>*</span>
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select ' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='Married'>
                      Married
                    </SelectItem>
                    <SelectItem value='Single'>Single</SelectItem>
                    <SelectItem value='Divorced'>
                      Divorced
                    </SelectItem>
                    <SelectItem value='Widowed'>
                      Widowed
                    </SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className='flex-1'>
          <FormField
            control={form.control}
            name='religion'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Religion <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select Religion' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='Islam'>Islam</SelectItem>
                      <SelectItem value='Christianity'>
                        Christianity
                      </SelectItem>
                      <SelectItem value='Hinduism'>
                        Hinduism
                      </SelectItem>
                      <SelectItem value='Buddhism'>
                        Buddhism
                      </SelectItem>
                      <SelectItem value='Sikhism'>
                        Sikhism
                      </SelectItem>
                      <SelectItem value='Judaism'>
                        Judaism
                      </SelectItem>
                      <SelectItem value='Bahaei'>
                        Bahaei
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
      {religion === 'Islam' && (
        <>
          <FormField
            control={form.control}
            name='religionSubCategory'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Religion Sub-Category{' '}
                  <span style={{ color: 'red' }}>*</span>
                </FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select ' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='Sunni'>Sunni</SelectItem>
                      <SelectItem value='Shia'>Shia</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </>
      )}

      <FormField
        control={form.control}
        name='fatherFullName'
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              Father's Full Name{' '}
              <span style={{ color: 'red' }}>*</span>
            </FormLabel>
            <FormControl>
              <Input {...field} placeholder='Enter' />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className='flex flex-wrap space-between gap-y-4'>
        <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
          <FormField
            control={form.control}
            name='motherFullName'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Mother's Full Name{' '}
                  <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <Input {...field} placeholder='Enter' />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className='flex-1'>
          <FormField
            control={form.control}
            name='motherFullNameArabic'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Mother's Full Name (Arabic)</FormLabel>
                <FormControl>
                  <Input {...field} placeholder='Enter' />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
    </form>
    </>
  )
}

export default Step3VisaApplication