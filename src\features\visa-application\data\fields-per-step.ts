import { ApplicationFormValues } from '../types/application-form-types'

export const fieldsPerStep: Record<number, (keyof ApplicationFormValues)[]> = {
  1: [
    'appUpdatesEmail',
    'whatsAppNumber',
    'applicationDate',
    'visaType',
    'familyOnHoldLetter',
    'tradeLicenseValidated',
    'renewalDisclaimer',
    'visaApplicationType',
    'visaFree',
    'outsideCountryInstructions',
    'outsideCountry',
    'currentVisaStatus',
    'nationality',
    'Agreed',
    'updatedRVCopy',
    'idCardUpload',
    'eVisaApplicationType',
    'residentVisaStamping',
    'visaValidUntil',
  ],
  2: [
    'title',
    'firstName',
    'middleName',
    'lastName',
    'title1',
    'firstNameArabic',
    'middleNameArabic',
    'lastNameArabic',
    'emailAddress',
    'phone',
    'streetAddress',
    'addressLine',
    'cityAddress',
    'stateProvince',
    'country',
    'streetAddress1',
    'addressLine2',
    'city',
    'province',
    'country1',
    'emiratesID',
    'emiratesIDNumber',
    'emiratesIDExpiryDate',
    'emiratesIDCopy',
  ],
  3: [
    'nationality1',
    'arabicName',
    'passportNumber',
    'placeOfIssue',
    'placeOfIssueArabic',
    'passportType',
    'agreementPassportRules',
    'countryOfIssuance',
    'coloredPassport',
    'coloredPassport2',
    'passportIssueDate',
    'passportExpiryDate',
    'cityOfBirth',
    'cityOfBirthArabic',
    'countryOfBirth',
    'dateOfBirth',
    'gender',
    'previousNationality',
    'maritalStatus',
    'religion',
    'religionSubCategory',
    'fatherFullName',
    'motherFullName',
    'motherFullNameArabic',
  ],
  4: [
    'typeOfEmployment',
    'employmentDuration',
    'jobTitleChange',
    'jobTitle',
    'employmentStartDate',
    'probationPeriod',
    'employmentTerminationNotice',
    'educationQualification',
    'returnTicket',
    'ticketEntitlementPeriod',
    'annualLeaveEntitlement',
    'workingDays',
    'calendarDays',
  ],
  5: [
    'salaryChange',
    'basicSalary',
    'transportationAllowance',
    'accommodationAllowance',
    'otherAllowance',
    'totalMonthlySalary',
  ],
  6: [
    'companyName',
    'tradeLicenseNumber',
    'establishmentCardNumber',
    'authorizedSignatory',
    'emailAddressOfGeneralManager',
    'termsAndConditions',
  ],
  7: [
    'photoOfApplicant',
    'visaApplicantFiles',
    'proofOfPayment',
    'preferredPaymentMethod',
    'visaFee',
    'statusChange',
    'vipStampingFee',
    'partnerInvestorVisa',
    'totalAmountToBePaid',
    'firstName',
    'lastName',
    'affirmInformation',
  ],
}