import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { But<PERSON> } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { CountryDropdown } from '@/components/ui/country-dropdown'
import FileUploadField from '@/components/ui/fileUpload'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import MultiSelectDropdown from '@/components/ui/multiSelect-dropdown'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import Typewriter from "@/components/ui/typewritter"
import { cn } from '@/lib/utils'
import { format } from 'date-fns'
import { Calendar as CalendarIcon, FilePenLine, Lightbulb, PlusIcon, TrashIcon } from 'lucide-react'
import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import PhoneInput from 'react-phone-input-2'
import 'react-phone-input-2/lib/style.css'
import { z } from 'zod'



interface RowData {
  id: number
  option: string
  field1: string
  field2: string
  field3: string
  field4: string
  checked: boolean
}

const applicationFormSchema = z.object({
  textInputField: z.string().min(2, { message: 'Input Field must be at least 2 characters.' }),
  multiSelectInputField: z.string().min(2, { message: 'Input Field must be at least 2 characters.' }),
  fileUploadInputField: z.string().min(2, { message: 'Input Field must be at least 2 characters.' }),
  prefixName: z.string().min(2, { message: 'Input Field must be at least 2 characters.' }),
  firstName: z.string().min(2, { message: 'First Name must be at least 2 characters.' }),
  lastName: z.string().min(2, { message: 'Last Name must be at least 2 characters.' }),
  partnerEmail: z.string().email({ message: 'Invalid email address.' }),
  emailField: z.string().email({ message: 'Invalid contact email address.' }),
  mobileNumber: z.number(),
  inputDate: z.date(),
  selectDropdown: z.string(),
  radioGroup: z.string(),
  singleSelectCountry: z.string(),
  multiSelectCountry: z.array(z.string()),
})


type ApplicationFormValues = z.infer<typeof applicationFormSchema>

const countries = [
  { value: 'us', label: 'United States' },
  { value: 'uk', label: 'United Kingdom' },
  { value: 'ca', label: 'Canada' },
  { value: 'au', label: 'Australia' },
  { value: 'fr', label: 'France' },
  { value: 'de', label: 'Germany' },
  { value: 'jp', label: 'Japan' },
  { value: 'br', label: 'Brazil' },
];

export default function ComponentBlocksScreen() {
  const [value, setValue] = React.useState('option1')
  const form = useForm<ApplicationFormValues>({
    defaultValues: {
      textInputField: '',
      multiSelectInputField: '',
      fileUploadInputField: '',
      emailField: '',
      prefixName: '',
      firstName: '',
      lastName: '',
      mobileNumber: 0,
      inputDate: new Date(),
      selectDropdown: '',
      radioGroup: '',
      singleSelectCountry: '',
      multiSelectCountry: [],
    },
  })


  // Row data of Table with input fields
  const [rows, setRows] = useState<RowData[]>([
      {
        id: 1,
        option: '',
        field1: '',
        field2: '',
        field3: '',
        field4: '',
        checked: false,
      },
      // ...additional rows
    ])
  
    function handleDelete(id: number) {
      setRows(rows.filter((row) => row.id !== id))
    }
  
    function handleOptionChange(id: number, value: string) {
      setRows(
        rows.map((row) => (row.id === id ? { ...row, option: value } : row))
      )
    }
  
    function handleFieldChange(id: number, field: keyof Omit<RowData, 'id' | 'option' | 'checked'>, value: string) {
      setRows(
        rows.map((row) => (row.id === id ? { ...row, [field]: value } : row))
      )
    }
  
    function handleCheckedChange(id: number, checked: boolean) {
      setRows(
        rows.map((row) => (row.id === id ? { ...row, checked } : row))
      )
    }
  
    function handleAddRow() {
      const newRow: RowData = {
        id: rows.length ? Math.max(...rows.map((r) => r.id)) + 1 : 1,
        option: '',
        field1: '',
        field2: '',
        field3: '',
        field4: '',
        checked: false,
      }
      setRows([...rows, newRow])
    }

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        {/* <TopNav links={topNav} /> */}
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-2 flex items-center justify-between space-y-2'>
            {/* <h1 className='text-2xl font-bold tracking-tight lg:text-[28px]'>These are the component blocks with</h1> */}
            <h1 className="text-3xl flex items-start justify-start">
      <p className="whitespace-pre-wrap">
        <span>{"These are the component blocks with "}</span>
        <Typewriter
          text={[
            "pefect code",
            "proper comments",
            "all essential features",
            "pixel perfect design",
            "awesome styles",
          ]}
          speed={70}
          className="text-yellow-500"
          waitTime={1500}
          deleteSpeed={40}
          cursorChar={"_"}
        />
      </p>
    </h1>
            <div className='flex items-center space-x-2 lg:relative sm:fixed lg:w-auto sm:space-x-2 fixed bottom-0 right-0 p-3 bg-background w-full text-right flex justify-end z-10 sm:z-auto sm:bg-transparent sm:p-0'>
            </div>
        </div>

        {/* Default layout for the card, card content, card header, card title, card description, form, form control, form description, form field, form item, form label, form message, input, select, select content, select item, select trigger, select value, radio group */}
        <Card className='mt-6'>
          <CardHeader>
            <CardTitle>Card Title</CardTitle>
          </CardHeader>
          <CardContent>
              <Form {...form}>
                <form className='space-y-4 fz-form'>


                  {/* single text input field */}
                      <FormField
                        control={form.control}
                        name='textInputField' render={({ field }) => (
                          <FormItem>
                              <FormLabel>Text Field</FormLabel>
                              <FormControl>
                                <Input  placeholder='Text Input Field' {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                        )}
                      />
                  {/* end of text input field */}
<FormField
                        control={form.control}
                        name='singleSelectCountry'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Country of Registration of the Parent Company
                            </FormLabel>
                            <FormControl>
                              <CountryDropdown
                                placeholder='Country'
                                defaultValue={field.value as string} 
                                onChange={(c) => field.onChange(c.alpha3)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                                              control={form.control}
                                              name='multiSelectCountry'
                                              render={({ field }) => (
                                                <FormItem>
                                                  <FormLabel>
                                                    Country of Registration of the Parent Company
                                                  </FormLabel>
                                                  <FormControl>
                                                    <CountryDropdown
                                                      multiple
                                                      placeholder='Country'
                                                      defaultValue={field.value as string[]}
                                                      onChange={(countries) => {
                                                        const codes = countries.map((c) => c.alpha3)
                                                        field.onChange(codes)
                                                      }}
                                                    />
                                                  </FormControl>
                                                  <FormMessage />
                                                </FormItem>
                                              )}
                                            />
                  {/* Multi select input field */}
                      <FormField
                        control={form.control}
                        name='multiSelectInputField' render={() => (
                          <FormItem>
                              <FormLabel>Multi Select with search Field</FormLabel>
                              <FormControl>
                              <MultiSelectDropdown options={countries}/>
                              
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                        )}
                      />
                  {/* end of Multi select input field */}

                  {/* File upload input field */}
                      <FormField
                        control={form.control}
                        name='fileUploadInputField' render={() => (
                          <FormItem>
                              <FormLabel>Upload File</FormLabel>
                              <FormControl>
                              <FileUploadField accept="application/pdf" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                        )}
                      />
                  {/* end of File upload input field */}
                  
                  {/* 2 column input fields */}
                  <div className='flex flex-wrap space-between gap-y-4'>
                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        <FormField
                          control={form.control}
                          name='emailField'
                          render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email <span className="text-red-500">*</span></FormLabel>
                            <FormControl>
                              <Input type='email' {...field} placeholder='Enter Contact Email'/>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                          )}
                        />
                      </div>
                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='mobileNumber'
                          render={({ field }) => (
                            <FormItem>
                            <FormLabel>Mobile Number</FormLabel>
                            <PhoneInput
                              country={'ae'}
                              value={field.value.toString()}
                              onChange={phone => field.onChange(phone)}
                              containerClass="fz-country-code flex h-9 w-full rounded-tl-[6px] rounded-bl-[6px] border border-input bg-transparent text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm"
                              inputClass=" flex  !h-9 !w-full rounded-tl-[6px] rounded-bl-[6px] border !border-transparent !bg-transparent px-3 text-base !shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm transform translate-y-[-2px]"
                              buttonClass="!h-100 !border-l-transparent !border-t-transparent !bg-slate-200 dark:!bg-slate-800 dark:!border-r-transparent !border-b-transparent !border-gray-200 !bg-transparent !rounded-tl-[8px] !rounded-bl-[6px]"
                              enableSearch={true}
                              searchPlaceholder="Search country..."
                              />
                            <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                      </div>
                    </div>
                  {/* End of 2 column input fields */}
                  
                  {/* 2 columns with First name Last name input */}
                    <div className='flex flex-wrap space-between gap-y-4'>
                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                          <FormField
                                  control={form.control}
                                  name='inputDate'
                                  render={({ field }) => (
                                      <FormItem>
                                        <FormLabel>Select Date</FormLabel>
                                        <Popover>
                                          <PopoverTrigger asChild>
                                            <FormControl>
                                              <Button
                                                variant={'outline'}
                                                className={cn(
                                                'w-full pl-3 text-left font-normal bg-transparent',
                                                !field.value && 'text-muted-foreground'
                                                )}
                                              >
                                                {field.value ? (
                                                field.value instanceof Date ? format(field.value, 'MMM d, yyyy') : ''
                                                ) : (
                                                <span>Pick a date</span>
                                                )}
                                                <CalendarIcon className='ml-auto h-4 w-4 opacity-50 text-slate-900 dark:text-slate-500 ' />
                                              </Button>
                                            </FormControl>
                                          </PopoverTrigger>
                                          <PopoverContent className='w-auto p-0' align='start'>
                                            <Calendar
                                              mode='single'
                                              selected={field.value}
                                              onSelect={field.onChange}
                                              disabled={(date: Date) =>
                                                date > new Date() || date < new Date('1900-01-01')
                                              }
                                            />
                                          </PopoverContent>
                                        </Popover>
                                        
                                        <FormMessage />
                                      </FormItem>
                                )}
                              />
                      </div>

                      {/* Full name with prefix dropdown */}
                      <div className='flex-1'>
                        <div className='flex items-end space-x-2'>
                          <FormField
                            control={form.control}
                            name='prefixName'
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Prefix</FormLabel>
                                <FormControl>
                                  <Select {...field}>
                                    <SelectTrigger>
                                      <SelectValue placeholder='Prefix' />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value='Mr'>Mr</SelectItem>
                                      <SelectItem value='Mrs'>Mrs</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <div className='w-full'>
                            <FormField
                              control={form.control}
                              name='firstName'
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>First Name</FormLabel>
                                  <FormControl>
                                    <Input {...field} placeholder='First Name' />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                          <div className='w-full'>
                            <FormField
                              control={form.control}
                              name='lastName'
                              render={({ field }) => (
                              <FormItem>
                                <FormLabel>Last Name</FormLabel>
                                <FormControl>
                                  <Input {...field} placeholder='Last Name'/>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                              )}
                            />
                          </div>
                        </div>
                      </div>
                      {/* End of Full name with prefix dropdown */}

                    </div>
                  {/* End of 2 columns with First name Last name input */}

                  {/* Dropdown input field */}
                  <FormField
                    control={form.control}
                    name='selectDropdown'
                    render={({ field }) => (
                    <FormItem>
                        <FormLabel>Select options from the dropdown?</FormLabel>
                        <FormControl>
                          <Select {...field}>
                            <SelectTrigger>
                              <SelectValue placeholder='Select options' />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value='country1'>Country 1</SelectItem>
                              <SelectItem value='country2'>Country 2</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  {/* end of dropdown input field */}

                  {/* Card title with description  */}
                  <CardHeader className='px-0 pt-0 pb-0'>
                    <CardTitle>Card title inside card content</CardTitle>
                  </CardHeader>

                  <CardDescription className='!mt-2'>
                    <p className='text-sm text-slate-700 dark:text-slate-400'>
                      This is a card description. You can use it to describe the card content.
                    </p>
                  </CardDescription>
                  {/* End of Card title with description  */}

                  {/* Table with input fields */}
                  <div className='fz-input-table space-y-4'>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead> </TableHead>
                          <TableHead>Activity Name</TableHead>
                          <TableHead>Activity Code</TableHead>
                          <TableHead>DED License Type</TableHead>
                          <TableHead>TPA</TableHead>
                          <TableHead>Regulatory Authority</TableHead>
                          <TableHead>Main Activity</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {rows.map((row) => (
                          <TableRow key={row.id}>
                            <TableCell>
                              <Button variant="ghost" onClick={() => handleDelete(row.id)}>
                                <TrashIcon className="w-4 h-4" />
                              </Button>
                            </TableCell>
                            <TableCell>
                              <Select value={row.option} onValueChange={(value) => handleOptionChange(row.id, value)}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select option" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="option1">Option 1</SelectItem>
                                  <SelectItem value="option2">Option 2</SelectItem>
                                </SelectContent>
                              </Select>
                            </TableCell>
                            <TableCell>
                              <Input 
                                value={row.field1} 
                                onChange={(e) => handleFieldChange(row.id, 'field1', e.target.value)} 
                                placeholder="Field 1" 
                              />
                            </TableCell>
                            <TableCell>
                              <Input 
                                value={row.field2} 
                                onChange={(e) => handleFieldChange(row.id, 'field2', e.target.value)} 
                                placeholder="Field 2" 
                              />
                            </TableCell>
                            <TableCell>
                              <Input 
                                value={row.field3} 
                                onChange={(e) => handleFieldChange(row.id, 'field3', e.target.value)} 
                                placeholder="Field 3" 
                              />
                            </TableCell>
                            <TableCell>
                              <Input 
                                value={row.field4} 
                                onChange={(e) => handleFieldChange(row.id, 'field4', e.target.value)} 
                                placeholder="Field 4" 
                              />
                            </TableCell>
                            <TableCell>
                              <Checkbox 
                                checked={row.checked} 
                                onCheckedChange={(checked) => handleCheckedChange(row.id, checked === true)}
                              />
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                    <Button type="button"  variant="link" className="mt-4 underline" onClick={handleAddRow}>
                      <PlusIcon></PlusIcon> Add New
                    </Button>
                  </div>
                  {/* End of Table with input fields */}

                  {/* Check box with message below  */}
                  <Alert className="bg-amber-500/10 alert-bg-warning dark:bg-primary/20 border-none">
                    <div className='flex items-center space-x-2'>
                      <Checkbox id="accept-rules" onCheckedChange={() => {}}/>
                      <label className="text-sm font-normal ml-2" htmlFor="accept-rules">I accept the statement below to abide by the activity rules</label>
                    </div>
                      <Alert className="border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20">
                        <AlertDescription>
                        We undertake that we will obtain all approvals, permits, licenses or other permissions from UAE government agencies that may be required at any time for particular products or services, and that we will cease to trade in any particular products or services to the extent that such trading is restricted or prohibited by UAE governmental agencies.
                        </AlertDescription>
                      </Alert>
                  </Alert>
                  {/* End of Check box with message below  */}

                  {/* Info message with Lightbulb icon*/}
                  <Alert className="flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20">
                      <span className="bg-blue-50 p-2 rounded inline-block dark:text-primary-dark">
                          <Lightbulb className="w-4 h-4 stroke-blue-500 dark:text-primary-dark" />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertDescription>
                          <p className="text-sm text-slate-700 dark:text-slate-400">
                              Please mention if the Registry Extract was issued previously (In the last 30 Days). If yes, we can proceed with the Certificate of Incumbency, else you will have to apply for a Registry extract document first.                 
                          </p>
                        </AlertDescription>
                      </div>
                  </Alert>
                  {/* End of Info message with Lightbulb*/}

                  {/* Warning note alert with bullet points */}
                      <Alert className="flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20">
                          <span className="hidden md:block bg-primary/10 p-2 rounded inline-block dark:text-primary-dark">
                              <FilePenLine className="w-4 h-4 stroke-primary dark:text-primary-dark" />
                          </span>
                          <div className='flex flex-col ml-2'>
                            <AlertTitle>Note</AlertTitle>
                            <AlertDescription>
                              <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                Registry extract/Certificate of Incumbency/ Letter of Good standing cannot be issued for Branch Companies.
                              </p>   
                              <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                              In order to issue a Letter of Good standing, a Registry extract must be applied and paid for.
                              </p>                   
                            </AlertDescription>
                          </div>
                      </Alert>
                  {/* End of Warning note alert with bullet points */}
                  

                   {/* Radio group */}
                   <FormField
                            control={form.control}
                            name='radioGroup'
                            render={() => (
                      <FormItem>
                        <FormLabel>Are you going to operate as Franchise?</FormLabel>
                        <FormControl>
                          <RadioGroup className='flex align-center' value={value} onValueChange={setValue}>
                            <RadioGroupItem className='mt-1' value='option1' id='option1' />
                            <label htmlFor='option1'>Yes</label>
                            <RadioGroupItem className='mt-1' value='option2' id='option2' />
                            <label htmlFor='option2'>No</label>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  {/* End of Radio group */}
                  
                  {/* check box field */}
                  <FormItem>
                    <div className='flex items-center space-x-2'>
                      <Checkbox id="check" onCheckedChange={() => {}}  />
                      <label className="text-sm font-normal ml-2" htmlFor="check">I accept the statement below to abide by the activity rules</label>
                    </div>
                  </FormItem>
                 {/* end of check box field */}

                </form>

                {/* Form buttons */}
                <div className='fixed bottom-0 right-0 p-3 bg-background flex justify-end space-x-2 z-10 fz-form-btns'>
                  <Button variant={'btn_outline'}>Reset</Button>
                  <Button variant={'default'}>Submit</Button>
                </div>
                {/* End of Form buttons */}
                
              </Form>
          </CardContent>
        </Card>
      </Main>

    </>
  )
}

