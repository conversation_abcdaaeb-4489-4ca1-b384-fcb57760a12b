import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { FilePenLine } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';

interface ShareCapitalAndShareholdersProps {
  form: UseFormReturn<any>;
  localNumberOfShareholders: string;
  setLocalNumberOfShareholders: (val: string) => void;
  localProposedCapital: string;
  setLocalProposedCapital: (val: string) => void;
  localShareValue: string;
  setLocalShareValue: (val: string) => void;
  calculateShareCapitalperShareholder: (form: UseFormReturn<any>) => void;
  calculateTotalNumberOfShares: (form: UseFormReturn<any>) => void;
}

const Step3LicenseApplication: React.FC<ShareCapitalAndShareholdersProps> = ({
  form,
  localNumberOfShareholders,
  setLocalNumberOfShareholders,
  localProposedCapital,
  setLocalProposedCapital,
  localShareValue,
  setLocalShareValue,
  calculateShareCapitalperShareholder,
  calculateTotalNumberOfShares,
}) => {
  return (
    <Card className='mt-6'>
      <CardContent>
        <Form {...form}>
          <form className='space-y-4 fz-form'>
            <CardHeader className='px-0 pt-0 pb-0'>
              <CardTitle>Shareholder Structure ( Step 3 of 6 )</CardTitle>
            </CardHeader>
            {/* Shareholding Type : Radio field */}
            <FormField
              control={form.control}
              name='shareholdingType'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Shareholding Type{' '}
                    <span className='text-red-500'>*</span>
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      className='flex align-center'
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      <RadioGroupItem
                        className='mt-1'
                        value='Individual'
                        id='Individual'
                      />
                      <label htmlFor='Individual'>Individual</label>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {form.watch('shareholdingType') === 'Individual' && (
              <>
                <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                  <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                    <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                  </span>
                  <div className='flex flex-col ml-2'>
                    <AlertDescription>
                      <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                        Individual shareholder type means company is owned by an individual or number of individuals (non corporate)
                      </p>
                      <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                        For corporate applications please reach out to your Client Engagement Manager
                      </p>
                    </AlertDescription>
                  </div>
                </Alert>
              </>
            )}

            {/* Number of Shareholders */}
            <FormField
              control={form.control}
              name="numberOfShareholders"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Number of Shareholders <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter the number of shareholders"
                      value={localNumberOfShareholders}
                      onChange={(e) => {
                        const value = e.target.value;
                        setLocalNumberOfShareholders(value);

                        if (value === '') {
                          field.onChange(undefined);
                          form.setError('numberOfShareholders', {
                            type: 'manual',
                            message: 'This field is required',
                          });
                        } else {
                          const numberValue = Number(value);
                          if (!isNaN(numberValue)) {
                            field.onChange(numberValue);
                            form.clearErrors('numberOfShareholders');
                            calculateShareCapitalperShareholder(form);
                          }
                        }
                      }}
                      onBlur={() => calculateShareCapitalperShareholder(form)}
                    />
                  </FormControl>
                  <FormDescription>
                    Up to 10 shareholders are allowed for the application. If you are looking to have more than 10 shareholders, please get in touch with your Client Engagement Manager.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Proposed Share Capital */}
            <FormField
              control={form.control}
              name="proposedShareCapital"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Proposed Share Capital <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter the proposed share capital"
                      value={localProposedCapital}
                      onChange={(e) => {
                        const value = e.target.value;
                        setLocalProposedCapital(value);

                        if (value === '') {
                          field.onChange(undefined);
                          form.setError('proposedShareCapital', {
                            type: 'manual',
                            message: 'This field is required',
                          });
                        } else {
                          const numberValue = Number(value);
                          if (!isNaN(numberValue)) {
                            field.onChange(numberValue);
                            form.clearErrors('proposedShareCapital');
                          }
                        }
                      }}
                      onBlur={() => {
                        calculateTotalNumberOfShares(form);
                        calculateShareCapitalperShareholder(form);
                      }}
                    />
                  </FormControl>
                  <FormDescription>
                    Recommended Share capital per shareholder is AED 50,000/- for any IFZA company.
                    Minimum share capital per shareholder is AED 10,000/- for any IFZA company.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Share Value */}
            <FormField
              control={form.control}
              name="shareValue"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Share Value <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter the share value"
                      value={localShareValue}
                      onChange={(e) => {
                        const value = e.target.value;
                        setLocalShareValue(value);

                        if (value === '') {
                          field.onChange(undefined);
                          form.setError('shareValue', {
                            type: 'manual',
                            message: 'This field is required',
                          });
                        } else {
                          const numberValue = Number(value);
                          if (!isNaN(numberValue)) {
                            field.onChange(numberValue);
                            form.clearErrors('shareValue');
                          }
                        }
                      }}
                      onBlur={() => calculateTotalNumberOfShares(form)}
                    />
                  </FormControl>
                  <FormDescription>
                    Please note the minimum share value per share is AED 10.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Total Number of Shares (Calculated automatically) */}
            <FormField
              control={form.control}
              name='totalNumberOfShares'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Total Number of Shares</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      value={form.watch('totalNumberOfShares')}
                      readOnly
                      placeholder='This field is calculated automatically based on Proposed Share Capital and Share Value.'
                      disabled
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {/* Share Capital per Shareholder */}
            <FormField
              control={form.control}
              name='shareCapitalPerShareholder'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Share Capital per Shareholder</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder='This field is calculated automatically based on Proposed Share Capital and Number of Shareholders.'
                      disabled
                    />
                  </FormControl>
                  <FormDescription>
                    This a suggested share capital calculation and can be
                    changed at a later stage, should you wish to allocate
                    the share percentage differently.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
              <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
              </span>
              <div className='flex flex-col ml-2'>
                <AlertDescription>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    Please note, that the chosen capital per shareholder is less than AED 48,000.
                  </p>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    For shareholders to obtain a Partner or Investor Visa, minimum share capital required per shareholder is AED 48,000. In this case the an attested degree is not required.
                  </p>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    For any shareholders to obtain managerial or above occupation title on their UAE Residence & Employment Visa, Attested Degree will be required as per the UAE immigration rules, if the share capital is less than AED 48,000 per shareholder.
                  </p>
                </AlertDescription>
              </div>
            </Alert>

            {/* Show Alert if Proposed Share Capital is greater than AED 150,000 */}
            {form.watch('proposedShareCapital') >= 150000 && (
              <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                  <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                </span>
                <div className='flex flex-col ml-2'>
                  <AlertDescription>
                    <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                      Whereas a licensee has a share capital of more than AED 150,000, shareholders are required to pay up the full capital and provide proof of payment of the capital.
                    </p>
                  </AlertDescription>
                </div>
              </Alert>
            )}
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default Step3LicenseApplication;
