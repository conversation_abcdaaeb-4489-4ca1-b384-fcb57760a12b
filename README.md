Tech Stack
UI: ShadcnUI (TailwindCSS)

Build Tool: Vite

Routing: TanStack Router

Type Checking: TypeScript

Linting/Formatting: <PERSON>slint & Prettier

Icons: Lucide Icons

Run Locally
Clone the project
https://github.com/IFZA-Dev-Ops/PortalWeb.git

Go to the project directory

  cd PortalWeb
Install dependencies

  npm install
Start the server

  npm run dev

License
Licensed under the MIT License
