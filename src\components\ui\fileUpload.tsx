import { useRef, useState } from 'react'
import { Upload } from 'lucide-react'
import { CircleX } from 'lucide-react'
import { Input } from '@/components/ui/input'

interface FileUploadFieldProps {
  accept: string
  onchoose?: (file: File | null) => void // Callback to handle file selection
}

export default function FileUploadField({
  accept,
  onchoose,
}: FileUploadFieldProps) {
  const inputRef = useRef<HTMLInputElement | null>(null) // Reference for the file input element
  const [fileChosen, setFileChosen] = useState(false) // State to track if a file is selected

  const handleClear = () => {
    if (inputRef.current) {
      inputRef.current.value = '' // Clear the input value
      setFileChosen(false) // Reset fileChosen state
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files // This can be null

    if (files && files.length > 0) {
      // Safely check if files is not null and contains at least one file
      const file = files[0] // Get the selected file
      setFileChosen(true) // Mark that a file has been selected
      if (onchoose) {
        onchoose(file) // Pass the file to the onchoose callback
      }
    }
  }

  return (
    <div className='relative w-full flex items-center'>
      {/* File input field */}
      <Input
        ref={inputRef}
        id='picture'
        type='file'
        accept={accept} // File types that can be selected
        className='file:pt-0.5'
        onChange={handleChange} // Call handleChange on file selection
      />
      <Upload className='absolute bottom-1.5 right-3 w-4 text-slate-600' />

      {/* Clear icon to remove the selected file */}
      {fileChosen && (
        <CircleX
          onClick={handleClear}
          className='absolute top-2 right-16 w-4 text-slate-600 cursor-pointer'
        />
      )}
    </div>
  )
}
