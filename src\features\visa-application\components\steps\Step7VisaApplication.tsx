import React from 'react'
import { useFormContext, useWatch } from 'react-hook-form'
import { FilePenLine } from 'lucide-react'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { CardHeader, CardTitle } from '@/components/ui/card'
import FileUploadField from '@/components/ui/fileUpload'
import { ApplicationFormValues } from '../../types/application-form-types'

const Step7VisaApplication: React.FC = () => {
  const form = useFormContext<ApplicationFormValues>()
  const preferredPaymentMethod = useWatch({ control: form.control, name: 'preferredPaymentMethod' })
  const visaApplicationType = useWatch({ control: form.control, name: 'visaApplicationType' })
  const visaType = useWatch({ control: form.control, name: 'visaType' })
  const residentVisaStamping = useWatch({ control: form.control, name: 'residentVisaStamping' })
  const jobTitle = useWatch({ control: form.control, name: 'jobTitle' })

  return (
    <>
    <form className="space-y-4 fz-form">
      <div className='flex flex-wrap space-between gap-y-4'>
        <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
          <FormField
            control={form.control}
            name='photoOfApplicant'
            render={() => (
              <FormItem>
                <FormLabel>
                  Photo of the applicant (passport size){' '}
                  <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <FileUploadField
                    accept='.jpg,.jpeg,.png'
                    onchoose={(file) => {
                      form.setValue(
                        'photoOfApplicant',
                        file || ''
                      )
                      form.clearErrors('photoOfApplicant')
                    }}
                  />
                </FormControl>
                <FormDescription>
                  Passport size photo.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className='flex-1'>
          <FormField
            control={form.control}
            name='visaApplicantFiles'
            render={() => (
              <FormItem>
                <FormLabel>
                  Visa Applicant Files{' '}
                  <span style={{ color: 'red' }}>*</span>
                </FormLabel>
                <FormControl>
                  <FileUploadField
                    accept='.pdf,.jpg,.jpeg'
                    onchoose={(file) => {
                      form.setValue(
                        'visaApplicantFiles',
                        file || ''
                      )
                      form.clearErrors('visaApplicantFiles')
                    }}
                  />
                </FormControl>
                <FormDescription>
                  Please upload the attachments like Passport
                  Copy, Passport Size Picture, Cancellation
                  Document, Entry Stamp Copy or Tourist Visa copy.
                  You can find the guide for accepted document
                  types at the following link.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      {preferredPaymentMethod ===
        'Already Paid with the License Application' && (
        <FormField
          control={form.control}
          name='proofOfPayment'
          render={() => (
            <FormItem>
              <FormLabel>Proof of Payment</FormLabel>
              <FormControl>
                <FileUploadField
                  accept='.pdf,.jpg,.jpeg,.png'
                  onchoose={(file) => {
                    form.setValue('proofOfPayment', file || '')
                    form.clearErrors('proofOfPayment')
                  }}
                />
              </FormControl>
              <FormDescription>
                Please upload the proof of payment for the Visa
                Application. If payment was made earlier with the
                License application, you can also attach it.
                Without the proof of payment we are unable to
                proceed with your application.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <CardHeader className='px-0 pt-0 pb-0'>
        <CardTitle>Payments </CardTitle>
      </CardHeader>

      <FormField
        control={form.control}
        name='preferredPaymentMethod'
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              Preferred Payment Method{' '}
              <span className='text-red-500'>*</span>
            </FormLabel>
            <FormControl>
              <Select
                onValueChange={field.onChange}
                value={field.value}
              >
                <SelectTrigger>
                  <SelectValue placeholder='Choose' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='Cash Payment (IFZA Offices only)'>
                    Cash Payment (IFZA Offices only)
                  </SelectItem>
                  <SelectItem value='Cheque'>Cheque</SelectItem>
                  <SelectItem value='Bank Transfer'>
                    Bank Transfer
                  </SelectItem>
                  <SelectItem value='ATM Cash Deposit'>
                    ATM Cash Deposit
                  </SelectItem>
                  <SelectItem value='Credit Card (Visa/Master Card)'>
                    Credit Card (Visa/Master Card)
                  </SelectItem>
                  <SelectItem value='Already Paid with the License Application'>
                    Already Paid with the License Application
                  </SelectItem>
                </SelectContent>
              </Select>
            </FormControl>
            <FormDescription>
              Please select the preferred payment method from the
              list below For more details, please visit,
              ifza.com/payments
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <CardHeader className='px-0 pt-0 pb-0'>
        <CardTitle>Payment Details</CardTitle>
      </CardHeader>

      <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
        <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
          <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
        </span>
        <div className='flex flex-col ml-2'>
          <AlertTitle>DISCLAIMER :</AlertTitle>
          <AlertDescription>
            <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
              International transfers may take up to 7-10 working
              days. Till the payment is reflected, your
              application will be kept on hold.
            </p>
          </AlertDescription>
        </div>
      </Alert>

      <FormField
        control={form.control}
        name='visaFee'
        render={({ field }) => (
          <FormItem>
            <FormLabel>Visa Fee</FormLabel>
            <FormControl>
              <Input
                {...field}
                placeholder=' '
                onChange={(e) => {
                  const value =
                    e.target.value === ''
                      ? undefined
                      : +e.target.value
                  field.onChange(value)
                }}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      {visaApplicationType === 'In-Country' &&
        (visaType === '' ||
          (visaType !== 'Employment Visa Renewal' &&
            visaType !==
            'Work Permit Renewal')) && (
          <>
            <FormField
              control={form.control}
              name='statusChange'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status Change</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder=' '
                      onChange={(e) => {
                        const value =
                          e.target.value === ''
                            ? undefined
                            : +e.target.value
                        field.onChange(value)
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        )}
      {residentVisaStamping === 'VIP' && (
        <>
          <FormField
            control={form.control}
            name='vipStampingFee'
            render={({ field }) => (
              <FormItem>
                <FormLabel>VIP Stamping Fee</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type='number'
                    placeholder='Enter '
                    onChange={(e) => {
                      const value =
                        e.target.value === ''
                          ? undefined
                          : +e.target.value
                      field.onChange(value)
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </>
      )}

      {(jobTitle === 'Partner' ||
        jobTitle === 'Investor') && (
        <>
          <FormField
            control={form.control}
            name='partnerInvestorVisa'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Partner/Investor Visa</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder=' '
                    onChange={(e) => {
                      const value =
                        e.target.value === ''
                          ? undefined
                          : +e.target.value
                      field.onChange(value)
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </>
      )}

      <FormField
        control={form.control}
        name='totalAmountToBePaid'
        render={({ field }) => (
          <FormItem>
            <FormLabel>Total Amount to be Paid</FormLabel>
            <FormControl>
              <Input {...field} placeholder=' ' disabled />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <Alert className='border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
        <AlertDescription>
          This is the amount due for the Visa Application as per
          the selection of the required services. Please proceed
          to{' '}
          <a
            href='https://ifza.com/en/payments/'
            target='_blank'
            rel='noopener noreferrer'
            className='text-blue-500 underline'
          >
            https://ifza.com/en/payments/{' '}
          </a>
          for information on different payment methods available.
        </AlertDescription>
      </Alert>

      <CardHeader className='px-0 pt-0 pb-0'>
        <CardTitle>Important Information</CardTitle>
      </CardHeader>

      <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
        <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
          <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
        </span>
        <div className='flex flex-col ml-2'>
          <AlertDescription>
            <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
              The General Directorate of Residency and Foreign
              Affairs – Dubai have updated the guidelines on
              Personal Photo Specifications for all GDRFA
              applications effective immediately.
            </p>
            <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
              Detailed Guides can be found at{' '}
              <a
                href='https://beta.smartservices.ica.gov.ae/echannels/web/client/manual/icao/icao_english.pdf'
                target='_blank'
                rel='noopener noreferrer'
                className='text-blue-500 underline'
              >
                this link
              </a>
            </p>
          </AlertDescription>
        </div>
      </Alert>

      <div className='flex-1'>
        <div className='flex items-center space-x-2'>
          <div className='flex flex-col w-full'>
            <FormLabel>
              Name of the person completing the application form
            </FormLabel>
            <div className='flex space-x-2 mt-4'>
              <div className='w-full'>
                <FormField
                  control={form.control}
                  name='firstName'
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder='First Name'
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className='w-full'>
                <FormField
                  control={form.control}
                  name='lastName'
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder='Last Name'
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <FormField
        control={form.control}
        name='affirmInformation'
        render={({ field }) => (
          <FormItem>
            <div className='flex items-center'>
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={(checked: boolean) =>
                    field.onChange(checked)
                  }
                />
              </FormControl>
              <FormLabel>
                I affirm that all the information provided above
                is accurate and true. I acknowledge that any
                delays or rejections resulting from inaccuracies
                in the information are my sole responsibility.
              </FormLabel>
            </div>

            <FormMessage />
          </FormItem>
        )}
      />
    </form>
    </>
  )
}

export default Step7VisaApplication