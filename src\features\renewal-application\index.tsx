import { useState, useEffect } from 'react'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { FilePenLine, Lightbulb, Loader2 } from 'lucide-react'
import PhoneInput from 'react-phone-input-2'
import { toast } from '@/hooks/use-toast'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { CountryDropdown } from '@/components/ui/country-dropdown'
import { DateTimePicker } from '@/components/ui/datetime-picker'
import FileUploadField from '@/components/ui/fileUpload'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'

const applicationFormSchema = z.object({
  yourEmailAddress: z.string().email({ message: 'Invalid email address.' }),
  ifzaRegistered: z.string().optional(),
  companyName: z.string().min(2, { message: 'Enter a value for this field.' }),
  tradeLicenseNumber: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  visaPackage: z.string().min(1, { message: 'Select a choice.' }),
  tradeLicenseValidity: z.string().min(1, { message: 'Select a choice.' }),
  establishmentCardType: z.string().optional(),
  firstName: z.string().min(2, { message: 'Required.' }),
  lastName: z.string().min(2, { message: 'Required.' }),
  generalManagerEmail: z.string().email({ message: 'Invalid email address.' }),
  generalManagerPhoneNumber: z
    .string()
    .min(5, { message: 'You must enter at least 5 digits.' }),
  financialYearEndDate: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine((date) => !isNaN(date.getTime()), {
      message: 'Invalid date format',
    }),
  countriesOfOperation: z
    .string()
    .min(1, { message: 'Choose your option(s).' }),
  uploadAdditionalDocuments: z.any().optional(),
  preparation: z.string().min(1, { message: 'Select a choice.' }),
  registration: z.string().min(1, { message: 'Select a choice.' }),
  ubo: z.enum(['a', 'b'], {
    required_error: 'Please select one option.',
  }),
  tpaAgreement: z.boolean().refine((val) => val === true, {
    message: 'Choose this option.',
  }),
  authorizedRepresentativeName: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  termsAndConditions: z.boolean().refine((val) => val === true, {
    message: 'You must accept the terms and conditions.',
  }),
  financialYearDisclaimer: z.boolean().refine((val) => val === true, {
    message: 'You must accept the terms and conditions.',
  }),
})
const currentYear = new Date().getFullYear()
const defaultYearEndDate = new Date(currentYear, 11, 31) // December = 11

type ApplicationFormValues = z.infer<typeof applicationFormSchema>

export default function Renewalscreen() {
  const [isFormSubmitted, setIsFormSubmitted] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  useState<boolean>(false)
  const form = useForm<ApplicationFormValues>({
    resolver: zodResolver(applicationFormSchema),
    defaultValues: {
      yourEmailAddress: '',
      ifzaRegistered: '',
      companyName: '',
      tradeLicenseNumber: '',
      visaPackage: '',
      tradeLicenseValidity: '',
      establishmentCardType: '',
      firstName: '',
      lastName: '',
      generalManagerEmail: '',
      generalManagerPhoneNumber: '',
      financialYearEndDate: defaultYearEndDate,
      countriesOfOperation: '',
      uploadAdditionalDocuments: null,
      preparation: '',
      registration: '',
      ubo: undefined,
      tpaAgreement: false,
      authorizedRepresentativeName: '',
      termsAndConditions: false,
      financialYearDisclaimer: false,
    },
    shouldUnregister: true,
    mode: 'onChange',
  })

  const handleReset = () => {
    form.reset()
    setIsFormSubmitted(false)
  }
  const onSubmit = async () => {
    const formValues = form.getValues()
    console.log('Form Data :', formValues)

    const isValid = await form.trigger()

    if (isValid) {
      console.log('Form Errors : All fields have been filled correctly.')
      setIsLoading(true)

      setTimeout(() => {
        toast({
          title: 'Success!',
          description:
            'Your Renewal Application has been submitted successfully.',
          variant: 'success',
        })
        setIsLoading(false)
      }, 1500)
    } else {
      console.log('Form Errors :', form.formState.errors)
      toast({
        title: 'Error',
        description: 'Please fill in all the required fields.',
        variant: 'destructive',
      })
    }
  }

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header></Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-2 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight lg:text-[28px]'>
            License Renewal Application
          </h1>
          <div className='flex items-center space-x-2 lg:relative sm:fixed lg:w-auto sm:space-x-2 fixed bottom-0 right-0 p-3 bg-background w-full text-right flex justify-end z-10 sm:z-auto sm:bg-transparent sm:p-0'></div>
        </div>

        <Card className='mt-6'>
          <CardContent>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className='space-y-4 mb-4'
              >
                <CardHeader className='px-0 pt-4 pb-0'>
                  <CardTitle>Renewals Dept.</CardTitle>
                </CardHeader>

                {/* Your Email Address */}
                <div className='flex flex-wrap space-between gap-y-4'>
                  <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                    <FormField
                      control={form.control}
                      name='yourEmailAddress'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Your Email Address{' '}
                            <span style={{ color: 'red' }}>*</span>{' '}
                          </FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormDescription>
                            To receive the completed form and quotation.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  {/* IFZA Registered Professional Partner */}
                  <div className='flex-1'>
                    <FormField
                      control={form.control}
                      name='ifzaRegistered'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            IFZA Registered Professional Partner
                          </FormLabel>
                          <FormControl>
                            <Input {...field} placeholder='Enter ' />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Company Name */}
                <div className='flex flex-wrap space-between gap-y-4'>
                  <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                    <FormField
                      control={form.control}
                      name='companyName'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Company Name <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Input placeholder='Enter ' {...field} />
                          </FormControl>
                          <FormDescription>
                            As per the Trade License.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  {/* Trade License Number */}

                  <div className='flex-1'>
                    <FormField
                      control={form.control}
                      name='tradeLicenseNumber'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Trade License Number{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Input placeholder='Enter ' {...field} />
                          </FormControl>
                          <FormDescription>
                            As per the Trade License.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Visa Package */}

                <div className='flex flex-wrap space-between gap-y-4'>
                  <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                    <FormField
                      control={form.control}
                      name='visaPackage'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Visa Package <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder='Select' />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value='0 Visa'>0 Visa</SelectItem>
                                <SelectItem value='1 Visa'>1 Visa</SelectItem>
                                <SelectItem value='2 Visa'>2 Visa</SelectItem>
                                <SelectItem value='3 Visa'>3 Visa</SelectItem>
                                <SelectItem value='4 Visa (Requires Office Space)'>
                                  4 Visa (Requires Office Space)
                                </SelectItem>
                                <SelectItem value='5 Visa (Requires Office Space)'>
                                  5 Visa (Requires Office Space)
                                </SelectItem>
                                <SelectItem value='6 Visa (Requires Office Space)'>
                                  6 Visa (Requires Office Space)
                                </SelectItem>
                                <SelectItem value='7+Visa (Requires Office Space)'>
                                  7+Visa (Requires Office Space)
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormDescription>
                            Please select the Visa Package you require for the
                            upcoming year. This includes all visas currently
                            sponsored by the company.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Trade License Validity */}

                  <div className='flex-1'>
                    <FormField
                      control={form.control}
                      name='tradeLicenseValidity'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Trade License Validity{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder='Select' />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value='1 Year'>1 Year</SelectItem>
                                <SelectItem value='2 Years - 15% discount'>
                                  2 Years - 15% discount
                                </SelectItem>
                                <SelectItem value='3 Years - 20% discount'>
                                  3 Years - 20% discount
                                </SelectItem>
                                <SelectItem value='5 Years - 30% discount'>
                                  5 Years - 30% discount
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Establishment Card Type */}

                <FormField
                  control={form.control}
                  name='establishmentCardType'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Establishment Card Type</FormLabel>
                      <FormControl>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder='Select' />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='Apply New Establishment Card'>
                              Apply New Establishment Card
                            </SelectItem>
                            <SelectItem value='Establishment Card Renewal'>
                              Establishment Card Renewal
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Company Member Details */}

                <div className='flex-1'>
                  <div className='flex items-center space-x-2'>
                    <div className='flex flex-col w-full'>
                      <FormLabel>
                        Company Member Details{' '}
                        <span className='text-red-500'>*</span>
                      </FormLabel>
                      <div className='flex space-x-2 mt-4'>
                        <div className='w-full'>
                          <FormField
                            control={form.control}
                            name='firstName'
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input {...field} placeholder='First Name' />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        <div className='w-full'>
                          <FormField
                            control={form.control}
                            name='lastName'
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input {...field} placeholder='Last Name' />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <FormDescription>
                  The General Manager/Director/Shareholder who is consenting to
                  the information provided within this form. (Signatory of the
                  form)
                </FormDescription>

                {/* General Manager Email */}

                <div className='flex flex-wrap space-between gap-y-4'>
                  <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                    <FormField
                      control={form.control}
                      name='generalManagerEmail'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            General Manager Email{' '}
                            <span style={{ color: 'red' }}>*</span>
                          </FormLabel>
                          <FormControl>
                            <Input type='Enter App Updates Email' {...field} />
                          </FormControl>

                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* General Manager's Phone Number */}

                  <div className='flex-1'>
                    <FormField
                      control={form.control}
                      name='generalManagerPhoneNumber'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            General Manager's Phone Number{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <PhoneInput
                            country={'ae'}
                            value={field.value.toString()}
                            onChange={(phone) => field.onChange(phone)}
                            containerClass='fz-country-code flex h-9 w-full rounded-tl-[6px] rounded-bl-[6px] border border-input bg-transparent text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm'
                            inputClass=' flex  !h-9 !w-full rounded-tl-[6px] rounded-bl-[6px] border !border-transparent !bg-transparent px-3 text-base !shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm transform translate-y-[-2px]'
                            buttonClass='!h-100 !border-l-transparent !border-t-transparent !bg-slate-200 dark:!bg-slate-800 dark:!border-r-transparent !border-b-transparent !border-gray-200 !bg-transparent !rounded-tl-[8px] !rounded-bl-[6px]'
                            enableSearch={true}
                            searchPlaceholder='Search country...'
                          />
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Financial Year End Date */}

                <div className='flex flex-wrap space-between gap-y-4'>
                  <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                    <FormField
                      control={form.control}
                      name='financialYearEndDate'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Financial Year End Date{' '}
                            <span className='text-red-500'>*</span>{' '}
                          </FormLabel>
                          <DateTimePicker
                            granularity='day'
                            value={field.value}
                            onChange={field.onChange}
                            displayFormat={{
                              hour24: 'dd MMMM yyyy',
                            }}
                          />
                          <FormDescription>
                            dd-MMM-yyyy <br />
                            This must be between 6 and 18 months from license
                            incorporation/migration (most recent license issue
                            date). Subsequent financial years are 12 months in
                            duration. Example: License issue date 01.08.2023
                            Financial Year End: 31.12.2024
                          </FormDescription>

                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  {/* Multi Select */}
                  {/* Countries of Operation */}

                  <div className='flex-1'>
                    <FormField
                      control={form.control}
                      name='countriesOfOperation'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Countries of Operation{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <CountryDropdown
                              placeholder='Country'
                              defaultValue={field.value}
                              onChange={(country) => {
                                field.onChange(country.alpha3)
                              }}
                            />
                          </FormControl>
                          <FormDescription>
                            {' '}
                            List the countries in which the company operates/has
                            a business presence. (Select at least one)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Upload additional documents */}

                <FormField
                  control={form.control}
                  name='uploadAdditionalDocuments'
                  render={() => (
                    <FormItem>
                      <FormLabel>Upload additional documents</FormLabel>
                      <FormControl>
                        <FileUploadField
                          accept='.pdf,.jpg,.png'
                          onchoose={(file) => {
                            form.setValue(
                              'uploadAdditionalDocuments',
                              file || ''
                            )
                            form.clearErrors('uploadAdditionalDocuments')
                          }}
                        />
                      </FormControl>
                      <FormDescription>
                        Companies with corporate shareholders: Upload
                        Certificate of Good Standing or equivalent. Individual
                        shareholders: Upload renewed passports (if necessary).
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
                  <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                    <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
                  </span>
                  <div className='flex space-y-2 flex-col ml-3'>
                    <AlertDescription>
                      <p className='text-sm text-slate-800 dark:text-slate-400'>
                        IFZA offers assistance with bookkeeping, financial
                        statement preparation, Corporate Tax and VAT support
                        (including registrations). Refer to our current
                        pricelist and services :{' '}
                        <a
                          href='https://cdn.ifza.com/marketing/IFZA_Corporate_Consulting_Price%20List_Oct24.pdf'
                          target='_blank'
                          rel='noopener noreferrer'
                          className='text-blue-500 underline'
                        >
                          IFZA Corporate Consulting Price List
                        </a>
                      </p>
                    </AlertDescription>
                  </div>
                </Alert>

                {/* Do you require assistance with Bookkeeping and Financial Statement Preparation ? */}

                <FormField
                  control={form.control}
                  name='preparation'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Do you require assistance with Bookkeeping and Financial
                        Statement Preparation ?{' '}
                        <span style={{ color: 'red' }}>*</span>
                      </FormLabel>
                      <FormControl>
                        <RadioGroup
                          className='flex align-center'
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          <RadioGroupItem
                            className='mt-1'
                            value='Yes'
                            id='yes'
                          />
                          <label htmlFor='yes'>Yes</label>
                          <RadioGroupItem className='mt-1' value='No' id='no' />
                          <label htmlFor='no'>No</label>
                        </RadioGroup>
                      </FormControl>
                      <FormDescription>
                        IFZA offers assistance with bookkeeping, financial
                        statement preparation, Corporate Tax and VAT support
                        (including registrations). Refer to our current
                        pricelist and services.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Do you require support for Corporate Tax or VAT registration ? */}

                <FormField
                  control={form.control}
                  name='registration'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Do you require support for Corporate Tax or VAT
                        registration ? <span style={{ color: 'red' }}>*</span>
                      </FormLabel>
                      <FormControl>
                        <RadioGroup
                          className='flex align-center'
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          <RadioGroupItem
                            className='mt-1'
                            value='Yes'
                            id='option1'
                          />
                          <label htmlFor='option1'>Yes</label>
                          <RadioGroupItem
                            className='mt-1'
                            value='No'
                            id='option2'
                          />
                          <label htmlFor='option2'>No</label>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='ubo'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        UBO – Ultimate Beneficial Owner{' '}
                        <span className='text-red-500'>*</span>
                      </FormLabel>
                      <FormControl>
                        <RadioGroup
                          className='flex flex-col space-y-4 mt-2'
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          {/* Option A */}
                          <FormItem className='flex items-start space-x-3 space-y-0'>
                            <FormControl>
                              <RadioGroupItem value='a' id='a' />
                            </FormControl>
                            <FormLabel htmlFor='a' className='font-normal'>
                              a - I confirm that the shareholder(s) listed in
                              the license are the UBO(s), based on their
                              shareholding percentages.
                            </FormLabel>
                          </FormItem>

                          {/* Option B */}
                          <FormItem className='flex items-start space-x-3 space-y-0'>
                            <FormControl>
                              <RadioGroupItem value='b' id='b' />
                            </FormControl>
                            <FormLabel htmlFor='b' className='font-normal'>
                              b - If there is a UBO not included within the
                              license, you need to submit a UBO form using the
                              following link:{' '}
                              <a
                                href='https://ifza.com/en/compliance-uboform/'
                                target='_blank'
                                rel='noopener noreferrer'
                                className='text-blue-600 underline'
                              >
                                https://ifza.com/en/compliance-uboform/
                              </a>
                            </FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='tpaAgreement'
                  render={({ field }) => (
                    <FormItem>
                      <div className='flex items-center'>
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={(checked: boolean) =>
                              field.onChange(checked)
                            }
                          />
                        </FormControl>
                        <FormLabel className='ml-2 space-y-2'>
                          <p>
                            <strong>Third Party Approval (TPA):</strong>
                          </p>
                          <p>
                            If your license includes an activity that requires
                            approval from a third-party authority or regulator,
                            you must submit the valid TPA Approval during the
                            renewal process.
                          </p>
                          <p>
                            Failure to do so may delay your renewal and result
                            in the removal of the activity from your license,
                            subject to amendment fees.
                          </p>
                        </FormLabel>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Authorized Representative Name */}
                <FormField
                  control={form.control}
                  name='authorizedRepresentativeName'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Authorized Representative Full Name{' '}
                        <span className='text-red-500'>*</span>
                      </FormLabel>
                      <FormControl>
                        <Input placeholder='Enter full name' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* T&C Disclaimer Checkbox */}
                <FormField
                  control={form.control}
                  name='termsAndConditions'
                  render={({ field }) => (
                    <FormItem>
                      <div className='flex items-center'>
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={(checked: boolean) =>
                              field.onChange(checked)
                            }
                          />
                        </FormControl>
                        <FormLabel className='ml-2'>
                          I confirm that I am authorized to renew the company’s
                          license, and that the information provided is accurate
                          and final. I have read and accept the{' '}
                          <a
                            href='https://cdn.ifza.com/docs/01_new/references/tnc_190821.pdf'
                            target='_blank'
                            rel='noopener noreferrer'
                            className='text-blue-600 underline'
                          >
                            Terms and Conditions
                          </a>
                          .
                        </FormLabel>
                      </div>

                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='financialYearDisclaimer'
                  render={({ field }) => (
                    <FormItem>
                      <div className='flex items-center'>
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={(checked: boolean) =>
                              field.onChange(checked)
                            }
                          />
                        </FormControl>
                        <FormLabel className='ml-2'>
                          I confirm that the company’s Financial Year follows
                          the Gregorian calendar by default, as per UAE FTA
                          guidelines. If different, I will notify IFZA with a
                          signed Board Resolution at{' '}
                          <a
                            href='mailto:<EMAIL>'
                            className='text-blue-600 underline'
                          >
                            <EMAIL>
                          </a>
                          .
                        </FormLabel>
                      </div>

                      <FormMessage />
                    </FormItem>
                  )}
                />

                <AlertDescription>
                  <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                    {' '}
                    Click "Submit" and check your email to sign the form.
                  </p>
                </AlertDescription>
              </form>
              <div className='fixed bottom-0 right-0 p-3 bg-background flex justify-end space-x-2 z-10 fz-form-btns'>
                <Button
                  variant={'btn_outline'}
                  onClick={handleReset}
                  disabled={isLoading}
                >
                  Reset
                </Button>
                <Button
                  variant={'default'}
                  onClick={onSubmit}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                      Processing...
                    </>
                  ) : (
                    'Submit'
                  )}
                </Button>
              </div>
            </Form>
          </CardContent>
        </Card>
      </Main>
    </>
  )
}
