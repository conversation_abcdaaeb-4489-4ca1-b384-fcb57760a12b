import { toast } from '@/hooks/use-toast'
import {
  createVisaLetterRequest,
  mainpulateVisaLetterRequestData,
} from '@/services/visaletterrequest'
import { zodResolver } from '@hookform/resolvers/zod'
import { FilePenLine, Lightbulb, Loader2 } from 'lucide-react'
import { useState , useEffect } from 'react'
import { useForm } from 'react-hook-form'
import PhoneInput from 'react-phone-input-2'
import { z } from 'zod'
// import { ProfileDropdown } from '@/components/profile-dropdown'
// import { Search } from '@/components/search'
// import { ThemeSwitch } from '@/components/theme-switch'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { ThemeSwitch } from '@/components/theme-switch'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { DateTimePicker } from '@/components/ui/datetime-picker'
import FileUploadField from '@/components/ui/fileUpload'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'

const applicationFormSchema = z
  .object({
    registeredCompanyName: z
      .string()
      .min(2, { message: 'Enter a value for this field.' }),

    managerName: z
      .string()
      .min(2, { message: 'Enter a value for this field.' })
      .optional(),

    tradeLicenseNumber: z
      .string()
      .min(2, { message: 'Enter a value for this field.' }),
    title: z.string().min(2, { message: '*' }),

    firstName: z
      .string()
      .min(2, { message: 'First Name must be at least 2 characters.' }),
    middleName: z.string().optional(),
    lastName: z
      .string()
      .min(2, { message: 'Last Name must be at least 2 characters.' }),

    title2: z.string().optional(),
    firstName2: z.string().optional(),
    middleName2: z.string().optional(),
    lastName2: z.string().optional(),

    title3: z.string().optional(),
    firstName3: z.string().optional(),
    middleName3: z.string().optional(),
    lastName3: z.string().optional(),

    title4: z.string().optional(),
    firstName4: z.string().optional(),
    middleName4: z.string().optional(),
    lastName4: z.string().optional(),

    title5: z.string().optional(),
    firstName5: z.string().optional(),
    middleName5: z.string().optional(),
    lastName5: z.string().optional(),

    title6: z.string().optional(),
    firstName6: z.string().optional(),
    middleName6: z.string().optional(),
    lastName6: z.string().optional(),

    title7: z.string().optional(),
    firstName7: z.string().optional(),
    middleName7: z.string().optional(),
    lastName7: z.string().optional(),

    title8: z.string().optional(),
    firstName8: z.string().optional(),
    middleName8: z.string().optional(),
    lastName8: z.string().optional(),

    title9: z.string().optional(),
    firstName9: z.string().optional(),
    middleName9: z.string().optional(),
    lastName9: z.string().optional(),

    title10: z.string().optional(),
    firstName10: z.string().optional(),
    middleName10: z.string().optional(),
    lastName10: z.string().optional(),
    passportNum: z
      .string()
      .min(1, { message: 'Enter a value for this field.' })
      .optional(),

    residenceVisaCopy: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),

    yourName: z.string().min(2, { message: 'Your Name is required.' }),
    phoneNumber: z.string().optional(),
    yourEmailAddress: z.string().email({ message: 'Invalid email address.' }),
    whatsAppNumber: z.string().optional(),
    paymentType: z.string().min(1, { message: 'Select a choice.' }),
    paymentProof: z.any().optional(), // Optional without any validation
    letterDocumentsRequired: z.string().min(1, { message: 'Select a choice.' }),
    amendmentType: z
      .string()
      .refine((value) => value.trim().length > 0, {
        message: 'Amendment Type is required',
      })
      .optional(),

    amendmentOfficialDocument: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),

    updatedPassportSizedPicture: z.any().optional(), // Optional without any validation

    passportCopy: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),

    isCancellationRequired: z.string().optional(),

    goldenVisa: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),

    wasApplicantDeclared: z
      .string()
      .min(1, { message: 'Select this choice.' })
      .optional(), // check if wasApplicantDeclared is selected
    flightTicket: z.any().optional(),
    dhaLetter: z.any().optional(),
    medicalTestResult: z.any().optional(),

    requiredDocuments: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),
    uploadRv: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),
    passportCo: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),

    residenceVisa: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),
    letterShouldBeAddressedTo: z
      .string()

      .min(2, { message: 'Enter a value for this field.' })
      .optional(),

    lastDateOfExit: z
      .union([z.date(), z.string().transform((str) => new Date(str))])
      .refine((date) => !isNaN(date.getTime()), {
        message: 'Invalid date format',
      })
      .optional(),

    signedLetter: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),

    salaryAmendmentLetter: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),
    bankStatement: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),

    currentBasic: z.number().min(0).optional(),

    currentAccommodation: z.number().min(0).optional(),

    currentTransportation: z.number().min(0).optional(),

    currentOtherAllowance: z.number().min(0).optional(),

    currentSalary: z
      .string()
      .min(1, { message: 'Enter a number for this field.' })
      .optional(),

    newBasic: z.number().min(0).optional(),

    newAccommodation: z.number().min(0).optional(),
    newTransportation: z.number().min(0).optional(),
    newOtherAllowance: z.number().min(0).optional(),

    amendedSalary: z
      .string()
      .min(1, { message: 'Enter a number for this field.' })
      .optional(),

    serviceType: z.string().min(1, { message: 'Select a choice.' }).optional(),
    reasonForLetter: z
      .string()
      .min(1, { message: 'Select a choice.' })
      .optional(),

    letterAddressedTo: z
      .string()
      .min(2, { message: 'Enter a value for this field.' })
      .optional(),

    isNOCForEmployeeOrInvestor: z
      .string()
      .min(1, { message: 'Select a choice.' })
      .optional(),

    isMinimumSalaryAED30K: z
      .string()
      .min(1, { message: 'Select a choice.' })
      .optional(),

    auditedFinancialReport: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),

    religion: z.boolean().optional(), // check if religion is selected
    passportNumber: z.boolean().optional(), // check if passport number is selected
    nationality: z.boolean().optional(), // check if nationality is selected
    designation: z.boolean().optional(), // check if designation is selected
    applicantName: z.boolean().optional(), // check if applicant name is selected

    oldName: z.string().optional(),

    newName: z.string().optional(),

    newPassportCopy: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),
    oldPassportCopy: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),

    newPassportCo: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),
    oldPassportCo: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),

    currentDesignation: z.string().nonempty('Select a choice.').optional(),
    newDesignation: z.string().nonempty('Select a choice.').optional(),

    attestedMofaDegree: z.any().optional(), // Optional without any validation
  })

  .refine(
    (data) => {
      if (data.letterDocumentsRequired === 'Visa Amendment Letter') {
        return data.amendmentType && data.amendmentType.trim().length > 0
      }
      return true
    },
    {
      message:
        'Amendment Type is required when "Visa Amendment Letter" is selected.',
      path: ['amendmentType'],
    }
  )

  .refine(
    (document) => {
      // Access the 'religion' field directly from the form data
      const religion = document?.religion // Get the religion value from the form

      if (religion) {
        // If 'religion' is selected, ensure that 'amendmentOfficialDocument' contains a file
        return document instanceof File && document.size > 0
      }
      return true // If 'religion' is not selected, skip the validation
    },
    {
      message: 'Religion Amendment Official Document is required.',
    }
  )

  .refine(
    (data) => {
      if (data.letterDocumentsRequired === 'Salary Certificate') {
        return data.managerName && data.managerName.trim().length > 0
      }
      return true
    },
    {
      message:
        'Manager Name is required when "Salary Certificate" is selected.',
    }
  )

  .refine(
    (document) => {
      // Access the 'applicantName', 'nationality', and 'passportNumber' fields from the form data
      const applicantName = document?.applicantName
      const nationality = document?.nationality
      const passportNumber = document?.passportNumber

      // Check if any of these fields are provided
      if (applicantName || nationality || passportNumber) {
        // Ensure that both 'newPassportCopy' and 'oldPassportCopy' contain valid files
        const newPassportCopy = document?.newPassportCopy
        const oldPassportCopy = document?.oldPassportCopy

        if (
          !(newPassportCopy instanceof File && newPassportCopy.size > 0) ||
          !(oldPassportCopy instanceof File && oldPassportCopy.size > 0)
        ) {
          return false // Fail validation if either file is missing
        }
      }

      return true // Pass validation if conditions are not met
    },
    {
      message:
        'New Passport Copy and Old Passport Copy are required when Applicant Name, Nationality, or Passport Number is provided.',
    }
  )

  .refine(
    (document) => {
      // Access the 'applicantName' and 'nationality' fields from the form data
      const applicantName = document?.applicantName
      const nationality = document?.nationality

      // Check if any of these fields are provided
      if (applicantName || nationality) {
        // Ensure that both 'newPassportCopy' and 'oldPassportCopy' contain valid files
        const newPassportCo = document?.newPassportCo
        const oldPassportCo = document?.oldPassportCo

        if (
          !(newPassportCo instanceof File && newPassportCo.size > 0) ||
          !(oldPassportCo instanceof File && oldPassportCo.size > 0)
        ) {
          return false // Fail validation if either file is missing
        }
      }

      return true // Pass validation if conditions are not met
    },
    {
      message:
        'New Passport Copy and Old Passport Copy are required when Applicant Name or Nationality is provided.',
    }
  )

  .refine(
    (document) => {
      // Check if 'designation' is selected
      const designation = document?.designation

      if (designation) {
        // Ensure 'currentDesignation' is not empty
        const currentDesignation = document?.currentDesignation
        if (!currentDesignation || currentDesignation.trim() === '') {
          return false
        }

        // Ensure 'newDesignation' is not empty
        const newDesignation = document?.newDesignation
        if (!newDesignation || newDesignation.trim() === '') {
          return false
        }
      }

      return true // Pass validation if 'designation' is not selected
    },
    {
      message:
        'Current Designation and New Designation (On Residence Visa) are required when Designation is selected.',
    }
  )

  .refine(
    (data) => {
      if (data.isCancellationRequired === 'Yes') {
        return data.goldenVisa && data.goldenVisa !== ''
      }
      return true
    },
    {
      message:
        'Email confirmation for golden visa pre-approval is required when "Is this cancellation required for a golden visa ?" is Yes.',
    }
  )

  .refine((data) => {
    // Check if the 'wasApplicantDeclared' field is true
    if (data.wasApplicantDeclared) {
      // Ensure that the 'registeredCompanyName' field is not empty
      return (
        data.registeredCompanyName && data.registeredCompanyName.trim() !== ''
      )
    }
    return true // If 'wasApplicantDeclared' is false, skip this validation
  })

  .refine(
    (data) =>
      data.wasApplicantDeclared !== 'Yes' ||
      (data.flightTicket instanceof File && data.flightTicket.size > 0),
    {
      message: 'Upload a file here.',
      path: ['flightTicket'],
    }
  )

  .refine(
    (data) =>
      data.wasApplicantDeclared !== 'Yes' ||
      (data.dhaLetter instanceof File && data.dhaLetter.size > 0),
    {
      message: 'Upload a file here.',
      path: ['dhaLetter'],
    }
  )

  .refine(
    (data) =>
      data.wasApplicantDeclared !== 'Yes' ||
      (data.medicalTestResult instanceof File &&
        data.medicalTestResult.size > 0),
    {
      message: 'Upload a file here.',
      path: ['medicalTestResult'],
    }
  )

  .refine(
    (data) => {
      const requiredDocuments = [
        'Dubai Residence Visa Cancellation (Inside UAE)',
        'Dubai Residence Visa Cancellation (Outside UAE) - Less than 6 Months',
        'Dubai Residence Visa Cancellation (Outside UAE) - More than 6 Months',
        'Fujairah Residence Visa Cancellation (Inside UAE)',
        'Fujairah Residence Visa Cancellation (Outside UAE) - Less than 6 Months',
        'Fujairah Residence Visa Cancellation (Outside UAE) - More than 6 Months',
        'Re-entry Pass (Outside UAE for more than 6 months)',
        'Work Permit Cancellation',
      ]

      const letterDocumentsRequired = data.letterDocumentsRequired

      // Check if the selected document matches one of the required ones
      if (requiredDocuments.includes(letterDocumentsRequired)) {
        // Ensure that the requiredDocuments field is filled if one of the specified conditions is met
        return (
          data.requiredDocuments &&
          data.requiredDocuments instanceof File &&
          data.requiredDocuments.size > 0
        )
      }

      return true // No need to enforce the condition if not matching
    },
    {
      message: 'Upload a file here.',
    }
  )

  .refine(
    (data) => {
      if (
        data.letterDocumentsRequired === 'Re-entry (At the airport)' ||
        data.letterDocumentsRequired ===
          'Re-entry Pass (Outside UAE for more than 6 months)'
      ) {
        return !!data.lastDateOfExit
      }
      return true
    },
    {
      message:
        'Last Date of Exit is required when "Re-entry (At the airport)" or "Re-entry Pass (Outside UAE for more than 6 months)" is selected.',
      path: ['lastDateOfExit'],
    }
  )

  .refine(
    (data) => {
      const requiredDocuments = [
        'Dubai Residence Visa Cancellation (Inside UAE)',
        'Dubai Residence Visa Cancellation (Outside UAE) - Less than 6 Months',
        'Dubai Residence Visa Cancellation (Outside UAE) - More than 6 Months',
        'Fujairah Residence Visa Cancellation (Inside UAE)',
        'Fujairah Residence Visa Cancellation (Outside UAE) - Less than 6 Months',
        'Fujairah Residence Visa Cancellation (Outside UAE) - More than 6 Months',
        'Re-entry Pass (Outside UAE for more than 6 months)',
        'Salary Certificate',
      ]

      const letterDocumentsRequired = data.letterDocumentsRequired

      // Check if the selected document matches one of the required ones
      if (requiredDocuments.includes(letterDocumentsRequired)) {
        // Ensure that the uploadRv field contains a file if one of the specified conditions is met
        return (
          data.uploadRv &&
          data.uploadRv instanceof File &&
          data.uploadRv.size > 0
        )
      }

      return true // No need to enforce the condition if the document doesn't match
    },
    {
      message: 'Upload a file here.',
    }
  )

  .refine(
    (data) => {
      // Check if the value in 'letterDocumentsRequired' is "Employee List"
      if (data.letterDocumentsRequired === 'Employee List') {
        // Ensure that 'letterShouldBeAddressedTo' exists and is not empty
        return (
          data.letterShouldBeAddressedTo &&
          data.letterShouldBeAddressedTo.trim().length > 0
        )
      }
      return true // No need to enforce the condition if the 'Employee List' condition is not met
    },
    {
      message:
        'Letter should be addressed to is required when "Employee List" is selected.',
    }
  )

  .refine(
    (data) => {
      const requiredDocuments = [
        'Dubai Residence Visa Cancellation (Inside UAE)',
        'Dubai Residence Visa Cancellation (Outside UAE) - Less than 6 Months',
        'Dubai Residence Visa Cancellation (Outside UAE) - More than 6 Months',
        'Re-entry Pass (Outside UAE for more than 6 months)',
      ]

      const letterDocumentsRequired = data.letterDocumentsRequired

      // Check if the selected document matches one of the required ones
      if (requiredDocuments.includes(letterDocumentsRequired)) {
        // Ensure that the uploadRv field contains a file if one of the specified conditions is met
        return (
          data.passportCo &&
          data.passportCo instanceof File &&
          data.passportCo.size > 0
        )
      }

      return true // No need to enforce the condition if the document doesn't match
    },
    {
      message: 'Upload a file here.',
    }
  )

  .refine(
    (data) => {
      if (data.letterDocumentsRequired === 'Re-entry (At the airport)') {
        return (
          data.signedLetter instanceof File &&
          data.signedLetter.size > 0 &&
          data.residenceVisa instanceof File &&
          data.residenceVisa.size > 0 &&
          data.passportCopy instanceof File &&
          data.passportCopy.size > 0
        )
      }
      return true
    },
    {
      message:
        'Signed Letter (with company letterhead), Residence Visa, and Passport Copy are required when "Re-entry (At the airport)" is selected.',
    }
  )

  .refine(
    (data) => {
      if (data.letterDocumentsRequired === 'Salary Amendment Letter') {
        return (
          data.salaryAmendmentLetter &&
          data.salaryAmendmentLetter instanceof File &&
          data.salaryAmendmentLetter.size > 0
        )
      }
      return true
    },
    {
      message:
        'Salary Amendment Letter is required when "Salary Amendment Letter" is selected.',
    }
  )
  .refine(
    (data) => {
      if (data.letterDocumentsRequired === 'Salary Amendment Letter') {
        return (
          data.bankStatement &&
          data.bankStatement instanceof File &&
          data.bankStatement.size > 0
        )
      }
      return true
    },
    {
      message:
        'Bank Statement is required when "Salary Amendment Letter" is selected.',
    }
  )
  .refine(
    (data) => {
      if (data.letterDocumentsRequired === 'Salary Amendment Letter') {
        return data.currentSalary && data.currentSalary.trim() !== ''
      }
      return true
    },
    {
      message:
        'Current Salary is required when "Salary Amendment Letter" is selected.',
    }
  )
  .refine(
    (data) => {
      if (data.letterDocumentsRequired === 'Salary Amendment Letter') {
        return data.amendedSalary && data.amendedSalary.trim() !== ''
      }
      return true
    },
    {
      message:
        'Amended Salary is required when "Salary Amendment Letter" is selected.',
    }
  )

  .refine(
    (data) => {
      if (data.letterDocumentsRequired === 'Salary Certificate') {
        return data.serviceType && data.serviceType.trim() !== ''
      }
      return true
    },
    {
      message:
        'Service Type is required when "Salary Certificate" is selected.',
    }
  )
  .refine(
    (data) => {
      if (data.letterDocumentsRequired === 'Salary Certificate') {
        return data.reasonForLetter && data.reasonForLetter.trim() !== ''
      }
      return true
    },
    {
      message:
        'Reason for the Letter is required when "Salary Certificate" is selected.',
    }
  )

  .refine(
    (data) => {
      if (data.reasonForLetter === 'Others') {
        return data.letterAddressedTo && data.letterAddressedTo.trim() !== ''
      }
      return true
    },
    {
      message:
        'Letter Addressed To is required when "Others" is selected in Reason for the Letter.',
    }
  )

  .refine(
    (data) => {
      if (data.letterDocumentsRequired === 'NOC for Golden Visa') {
        return (
          data.isNOCForEmployeeOrInvestor &&
          data.isNOCForEmployeeOrInvestor.trim() !== ''
        )
      }
      return true
    },
    {
      message:
        'Is the NOC for a Employee or Investor / Partner Visa is required when "NOC for Golden Visa.',
    }
  )

  .refine(
    (data) => {
      if (data.isNOCForEmployeeOrInvestor === 'Employee Visa') {
        return (
          data.isMinimumSalaryAED30K && data.isMinimumSalaryAED30K.trim() !== ''
        )
      }
      return true
    },
    {
      message:
        'Is the minimum salary AED 30,000 is required when "Employee Visa. is selected.',
    }
  )

  .refine(
    (data) => {
      if (data.isNOCForEmployeeOrInvestor === 'Investor / Partner Visa') {
        return (
          data.auditedFinancialReport &&
          data.auditedFinancialReport instanceof File &&
          data.auditedFinancialReport.size > 0
        )
      }
      return true
    },
    {
      message:
        'Audited financial reports are required when "Investor / Partner Visa" is selected.',
    }
  )

  .refine(
    (data) => {
      const requiredDocuments = [
        'Dubai Residence Visa Cancellation (Inside UAE)',
        'Dubai Residence Visa Cancellation (Outside UAE) - Less than 6 Months',
        'Dubai Residence Visa Cancellation (Outside UAE) - More than 6 Months',
        'Re-entry Pass (Outside UAE for more than 6 months)',
      ]

      const letterDocumentsRequired = data.letterDocumentsRequired

      // Check if the selected document matches one of the required ones
      if (requiredDocuments.includes(letterDocumentsRequired)) {
        // Ensure that the uploadRv field contains a file if one of the specified conditions is met
        return (
          data.passportCo &&
          data.passportCo instanceof File &&
          data.passportCo.size > 0
        )
      }

      return true // No need to enforce the condition if the document doesn't match
    },
    {
      message: 'Upload a file here.',
    }
  )

  .refine(
    (data) => {
      if (
        data.passportNum === 'Dubai Residence Visa Cancellation (Inside UAE)' ||
        data.passportNum ===
          'Dubai Residence Visa Cancellation (Outside UAE) - Less than 6 Months'
      ) {
        return data.passportNum && data.passportNum.trim() !== ''
      }
      return true
    },
    {
      message:
        'Passport Number is required when "Dubai Residence Visa Cancellation (Inside UAE)" OR "Dubai Residence Visa Cancellation (Outside UAE) - Less than 6 Months" is selected.',
    }
  )

  .refine(
    (data) => {
      if (data.letterDocumentsRequired === 'Labour Card') {
        return (
          data.residenceVisaCopy &&
          data.residenceVisaCopy instanceof File &&
          data.residenceVisaCopy.size > 0
        )
      }
      return true
    },
    {
      message:
        'Residence Visa Copy is required when "Labour Card" is selected.',
      path: ['residenceVisaCopy'],
    }
  )

  .refine(
    (data) =>
      data.letterDocumentsRequired !== 'Salary Amendment Letter' ||
      (data.currentBasic !== undefined && data.currentBasic >= 0),
    {
      message: 'Enter a value for this field.',
      path: ['currentBasic'],
    }
  )

  .refine(
    (data) =>
      data.letterDocumentsRequired !== 'Salary Amendment Letter' ||
      (data.currentAccommodation !== undefined &&
        data.currentAccommodation >= 0),
    {
      message: 'Enter a value for this field.',
      path: ['currentAccommodation'],
    }
  )

  .refine(
    (data) =>
      data.letterDocumentsRequired !== 'Salary Amendment Letter' ||
      (data.currentTransportation !== undefined &&
        data.currentTransportation >= 0),
    {
      message: 'Enter a value for this field.',
      path: ['currentTransportation'],
    }
  )
  .refine(
    (data) =>
      data.letterDocumentsRequired !== 'Salary Amendment Letter' ||
      (data.currentOtherAllowance !== undefined &&
        data.currentOtherAllowance >= 0),
    {
      message: 'Enter a value for this field.',
      path: ['currentOtherAllowance'],
    }
  )

  .refine(
    (data) =>
      data.letterDocumentsRequired !== 'Salary Amendment Letter' ||
      (data.newBasic !== undefined && data.newBasic >= 0),
    {
      message: 'Enter a value for this field.',
      path: ['newBasic'],
    }
  )

  .refine(
    (data) =>
      data.letterDocumentsRequired !== 'Salary Amendment Letter' ||
      (data.newAccommodation !== undefined && data.newAccommodation >= 0),
    {
      message: 'Enter a value for this field.',
      path: ['newAccommodation'],
    }
  )

  .refine(
    (data) =>
      data.letterDocumentsRequired !== 'Salary Amendment Letter' ||
      (data.newTransportation !== undefined && data.newTransportation >= 0),
    {
      message: 'Enter a value for this field.',
      path: ['newTransportation'],
    }
  )

  .refine(
    (data) =>
      data.letterDocumentsRequired !== 'Salary Amendment Letter' ||
      (data.newOtherAllowance !== undefined && data.newOtherAllowance >= 0),
    {
      message: 'Enter a value for this field.',
      path: ['newOtherAllowance'],
    }
  )

type ApplicationFormValues = z.infer<typeof applicationFormSchema>

export default function VisaLetterRequestscreen() {
  const [isFormSubmitted, setIsFormSubmitted] = useState(false)
  console.log('isFormSubmitted', isFormSubmitted)
  const [isLoading, setIsLoading] = useState(false)
  useState<boolean>(false)
  // const [date, setDate] = useState<Date | undefined>(undefined)
  // console.log('date', date)
  const form = useForm<ApplicationFormValues>({
    resolver: zodResolver(applicationFormSchema),
    defaultValues: {
      letterDocumentsRequired: '',
      amendmentType: '',
      amendmentOfficialDocument: '',
      updatedPassportSizedPicture: '',
      oldName: '',
      newName: '',
      newPassportCopy: '',
      oldPassportCopy: '',
      newPassportCo: '',
      oldPassportCo: '',
      currentDesignation: '',
      newDesignation: '',
      attestedMofaDegree: '',
      isCancellationRequired: '',
      goldenVisa: '',
      wasApplicantDeclared: '',
      flightTicket: null,
      dhaLetter: null,
      medicalTestResult: null,
      requiredDocuments: '',
      uploadRv: '',
      passportCo: '',
      letterShouldBeAddressedTo: '',
      signedLetter: '',
      lastDateOfExit: undefined,
      residenceVisa: '',
      passportCopy: '',
      salaryAmendmentLetter: '',
      bankStatement: '',

      currentBasic: 0,
      currentAccommodation: 0,
      currentTransportation: 0,
      currentOtherAllowance: 0,

      currentSalary: '',

      newBasic: 0,
      newAccommodation: 0,
      newTransportation: 0,
      newOtherAllowance: 0,

      amendedSalary: '',
      serviceType: '',
      reasonForLetter: '',
      letterAddressedTo: '',
      isNOCForEmployeeOrInvestor: '',
      isMinimumSalaryAED30K: '',
      auditedFinancialReport: '',
      registeredCompanyName: '',
      managerName: '',
      tradeLicenseNumber: '',
      title: '',
      firstName: '',
      middleName: '',
      lastName: '',
      title2: '',
      firstName2: '',
      middleName2: '',
      lastName2: '',
      title3: '',
      firstName3: '',
      middleName3: '',
      lastName3: '',
      title4: '',
      firstName4: '',
      middleName4: '',
      lastName4: '',
      title5: '',
      firstName5: '',
      middleName5: '',
      lastName5: '',
      title6: '',
      firstName6: '',
      middleName6: '',
      lastName6: '',
      title7: '',
      firstName7: '',
      middleName7: '',
      lastName7: '',
      title8: '',
      firstName8: '',
      middleName8: '',
      lastName8: '',
      title9: '',
      firstName9: '',
      middleName9: '',
      lastName9: '',
      title10: '',
      firstName10: '',
      middleName10: '',
      lastName10: '',
      passportNum: '',
      residenceVisaCopy: '',
      yourName: '',
      phoneNumber: '',
      yourEmailAddress: '',
      whatsAppNumber: '',
      paymentType: '',
      paymentProof: '',
    },
    shouldUnregister: true,
  })

  const [localCurrnetBasic, setLocalCurrentBasic] = useState(
    form.watch('currentBasic')?.toString() || ''
  )

  const [localCurrentAccommodation, setLocalCurrentAccommodation] = useState(
    form.watch('currentAccommodation')?.toString() || ''
  )

  const [localCurrentTransportation, setLocalCurrentTransportation] = useState(
    form.watch('currentTransportation')?.toString() || ''
  )

  const [localCurrentOtherAllowance, setLocalCurrentOtherAllowance] = useState(
    form.watch('currentOtherAllowance')?.toString() || ''
  )

  const [localNewBasic, setLocalNewBasic] = useState(
    form.watch('newBasic')?.toString() || ''
  )

  const [localNewAccommodation, setLocalNewAccommodation] = useState(
    form.watch('newAccommodation')?.toString() || ''
  )

  const [localNewTransportation, setLocalNewTransportation] = useState(
    form.watch('newTransportation')?.toString() || ''
  )

  const [localNewOtherAllowance, setLocalNewOtherAllowance] = useState(
    form.watch('newOtherAllowance')?.toString() || ''
  )

  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (!name) return

      if (name === 'currentBasic') {
        setLocalCurrentBasic(value?.currentBasic?.toString() || '')
      } else if (name === 'currentAccommodation') {
        setLocalCurrentAccommodation(
          value?.currentAccommodation?.toString() || ''
        )
      } else if (name === 'currentTransportation') {
        setLocalCurrentTransportation(
          value?.currentTransportation?.toString() || ''
        )
      } else if (name === 'currentOtherAllowance') {
        setLocalCurrentOtherAllowance(
          value?.currentOtherAllowance?.toString() || ''
        )
      } else if (name === 'newBasic') {
        setLocalNewBasic(value?.newBasic?.toString() || '')
      } else if (name === 'newAccommodation') {
        setLocalNewAccommodation(value?.newAccommodation?.toString() || '')
      } else if (name === 'newTransportation') {
        setLocalNewTransportation(value?.newTransportation?.toString() || '')
      } else if (name === 'newOtherAllowance') {
        setLocalNewOtherAllowance(value?.newOtherAllowance?.toString() || '')
      }
    })

    return () => subscription.unsubscribe()
  }, [form])

  const handleReset = () => {
    form.reset()
    setIsFormSubmitted(false)
  }
  const onSubmit = (data: ApplicationFormValues) => {
    // Set loading state to true when submission starts
    setIsLoading(true)

    // console.log('data', data)
    const mainpulatedData = mainpulateVisaLetterRequestData(data)

    createVisaLetterRequest(
      mainpulatedData,
      () => {
        setIsFormSubmitted(true)
        setIsLoading(false)

        toast({
          title: 'Success!',
          description:
            'Your visa letter request has been submitted successfully.',
          variant: 'success',
        })
      },
      (error) => {
        setIsFormSubmitted(true)
        setIsLoading(false)

        toast({
          title: 'Error!',
          description:
            error?.response?.data?.data ||
            error?.message ||
            'Something went wrong.',
          variant: 'error',
        })
      }
    )
  }
  const [selectedAmendments, setSelectedAmendments] = useState<string[]>([])
  const [maxSelectionError, setMaxSelectionError] = useState<boolean>(false)

  const handleCheckboxChange = (value: string, checked: boolean) => {
    setSelectedAmendments((prev) => {
      const updated = checked
        ? [...prev, value]
        : prev.filter((item) => item !== value)

      if (updated.length > 2) {
        setMaxSelectionError(true)
        return prev
      }

      setMaxSelectionError(false)

      form.setValue('amendmentType', updated.join(','), {
        shouldValidate: true,
      })

      if (updated.length > 0) {
        form.clearErrors('amendmentType')
      }

      return updated
    })
  }

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        {/* <TopNav links={topNav} /> */}
        <div className='ml-auto flex items-center space-x-4'>
          {/* <Search /> */}
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-2 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight lg:text-[28px]'>
            Immigration / Visa Letter Request
          </h1>
          <div className='flex items-center space-x-2 lg:relative sm:fixed lg:w-auto sm:space-x-2 fixed bottom-0 right-0 p-3 bg-background w-full text-right flex justify-end z-10 sm:z-auto sm:bg-transparent sm:p-0'></div>
        </div>

        <Card className='mt-6'>
          <CardContent>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className='space-y-4 mb-4'
              >
                <CardHeader className='px-0 pt-0 pb-0'>
                  <CardTitle>Letter/Request Details</CardTitle>
                </CardHeader>
                {/* Letter/Documents Required */}
                <FormField
                  control={form.control}
                  name='letterDocumentsRequired'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Letter / Documents Required{' '}
                        <span style={{ color: 'red' }}>*</span>
                      </FormLabel>
                      <FormControl>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder='Select ' />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='Amendment Letter (Company Name)'>
                              Amendment Letter (Company Name)
                            </SelectItem>
                            <SelectItem value='Attested Employment Contract'>
                              Attested Employment Contract
                            </SelectItem>
                            <SelectItem value='DNRD Report'>
                              DNRD Report
                            </SelectItem>
                            <SelectItem value='Dubai Residence Visa Cancellation (Inside UAE)'>
                              Dubai Residence Visa Cancellation (Inside UAE)
                            </SelectItem>
                            <SelectItem value='Dubai Residence Visa Cancellation (Outside UAE) - Less than 6 Months'>
                              Dubai Residence Visa Cancellation (Outside UAE) -
                              Less than 6 Months
                            </SelectItem>
                            <SelectItem value='Dubai Residence Visa Cancellation (Outside UAE) - More than 6 Months'>
                              Dubai Residence Visa Cancellation (Outside UAE) -
                              More than 6 Months
                            </SelectItem>
                            <SelectItem value='E-Visa Cancellation'>
                              E-Visa Cancellation
                            </SelectItem>
                            <SelectItem value='E-Visa Withdrawal'>
                              E-Visa Withdrawal
                            </SelectItem>
                            <SelectItem value='E-visa Amendment'>
                              E-visa Amendment
                            </SelectItem>
                            <SelectItem value='Employee List'>
                              Employee List
                            </SelectItem>
                            <SelectItem value='Fujairah Residence Visa Cancellation (Inside UAE)'>
                              Fujairah Residence Visa Cancellation (Inside UAE)
                            </SelectItem>
                            <SelectItem value='Fujairah Residence Visa Cancellation (Outside UAE) - Less than 6 Months'>
                              Fujairah Residence Visa Cancellation (Outside UAE)
                              - Less than 6 Months
                            </SelectItem>
                            <SelectItem value='Fujairah Residence Visa Cancellation (Outside UAE) - More than 6 Months'>
                              Fujairah Residence Visa Cancellation (Outside UAE)
                              - More than 6 Months
                            </SelectItem>
                            <SelectItem value='Labour Card'>
                              Labour Card
                            </SelectItem>
                            <SelectItem value='NOC for Golden Visa'>
                              NOC for Golden Visa
                            </SelectItem>
                            <SelectItem value='Re-entry (At the airport)'>
                              Re-entry (At the airport)
                            </SelectItem>
                            <SelectItem value='Re-entry Pass (Outside UAE for more than 6 months)'>
                              Re-entry Pass (Outside UAE for more than 6 months)
                            </SelectItem>
                            <SelectItem value='Salary Amendment Letter'>
                              Salary Amendment Letter
                            </SelectItem>
                            <SelectItem value='Salary Certificate'>
                              Salary Certificate
                            </SelectItem>
                            <SelectItem value='Visa Amendment Letter'>
                              Visa Amendment Letter
                            </SelectItem>
                            <SelectItem value='Visa Cancellation (within the UAE) and Work Permit Issuance'>
                              Visa Cancellation (within the UAE) and Work Permit
                              Issuance
                            </SelectItem>
                            <SelectItem value='Visa Cancellation (Outside the UAE) and Work Permit Issuance'>
                              Visa Cancellation (Outside the UAE) and Work
                              Permit Issuance
                            </SelectItem>
                            <SelectItem value='Work Permit Cancellation'>
                              Work Permit Cancellation
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormDescription>
                        Please select the letter/document that you are looking
                        for.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {/* show these when "Letter/Documents Required" is 'Work Permit Cancellation'*/}
                {form.watch('letterDocumentsRequired') ===
                  'Work Permit Cancellation' && (
                  <>
                    {/* Instructions Text */}
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle className='underline'>
                          REQUIRED DOCUMETS :
                        </AlertTitle>
                        <AlertDescription>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['1.'] before:absolute before:left-0 before:top-0">
                            Signed End of Service Letter by the Employee and the
                            company, as well as the company stamp.
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['2.'] before:absolute before:left-0 before:top-0">
                            Work permit renewal or cancellation request can be
                            submitted 29 days AFTER EXPIRY. On the 30th day,
                            fines may incur.
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Form can be downloaded from this{' '}
                            <a
                              href='https://files.ifza.com/external/file/lx6sy530d9fbe09e145b9ba0c786559b3b493'
                              className='text-blue-500 underline'
                              rel='noopener noreferrer'
                              target='_blank'
                            >
                              Link
                            </a>{' '}
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>
                  </>
                )}
                {form.watch('letterDocumentsRequired') ===
                  'Salary Certificate' && (
                  <>
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle>Please note :</AlertTitle>
                        <AlertDescription>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            the salary mentioned on the letter will be exactly
                            as per the signed Employment contract.{' '}
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>
                  </>
                )}
                {form.watch('letterDocumentsRequired') ===
                  'Re-entry Pass (Outside UAE for more than 6 months)' && (
                  <>
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle className='underline '>
                          REQUIRED DOCUMENTS :
                        </AlertTitle>
                        <AlertDescription>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Please provide us with a signed letter (with company
                            letterhead and stamp) explaining why the applicant
                            has been outside UAE from more than 6 months.
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Any additional documents like bank statements/flight
                            tickets will be beneficial, but are not mandatory.
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>
                  </>
                )}
                {/* show this when "Letter/Documents Required" is 'Re-entry Pass (Outside UAE for more than 6 months)' */}
                {form.watch('letterDocumentsRequired') ===
                  'Re-entry Pass (Outside UAE for more than 6 months)' && (
                  <>
                    {/* Instructions Text */}
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle className='underline text-red-500'>
                          IMPORTANT NOTES :
                        </AlertTitle>

                        <AlertDescription>
                          <p className="text-sm text-red-500 dark:text-red-500 pl-4 relative before:content-['1.'] before:absolute before:left-0 before:top-0">
                            Investor/Partner visas can stay outside UAE for up
                            to 12 months.
                          </p>
                          <p className="text-sm text-red-500 dark:text-red-500 pl-4 relative before:content-['2.'] before:absolute before:left-0 before:top-0">
                            Only Employment visas are required to enter UAE once
                            every 6 months.
                          </p>
                          <p className="text-sm text-red-500 dark:text-red-500 pl-4 relative before:content-['3.'] before:absolute before:left-0 before:top-0">
                            The applicant must only enter through Dubai, no
                            other Emirate.
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>
                  </>
                )}
                {/* show these when "Letter/Documents Required" is 'Visa Amendment Letter' */}
                {form.watch('letterDocumentsRequired') ===
                  'Visa Amendment Letter' && (
                  <>
                    {/* Instructions Text */}
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle className='underline'>
                          IMPORTANT NOTES :
                        </AlertTitle>
                        <AlertDescription>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['1.'] before:absolute before:left-0 before:top-0">
                            If your current designation is Investor/Partner,
                            designation amendment will not be possible. You will
                            need to cancel your current visa and re-apply for a
                            new visa with desired designation (all costs for
                            re-application will be applicable).
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Additionally, if your current designation is GM or
                            falls under the "EMPLOYMENT VISA" category, you
                            cannot do an amendment to be an investor/partner.
                            You will need to cancel your current visa and
                            re-apply for a new visa with desired designation
                            (all costs for re-application will be applicable).
                          </p>

                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['2.'] before:absolute before:left-0 before:top-0">
                            The amendment will only be reflected on the
                            residence visa and not the emirates ID. Please
                            contact the typing center for EID amendment.
                          </p>

                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['3.'] before:absolute before:left-0 before:top-0">
                            As per immigration, Designation and Nationality
                            amendment cannot be done together.
                          </p>

                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['4.'] before:absolute before:left-0 before:top-0">
                            The applicant must be inside UAE to proceed with the
                            amendment.
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            *If the applicant exits UAE during the amendment
                            process, the request may be rejected. In this case,
                            the money will not be refunded.
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>

                    {/* You can not select more than 2 Amendment types. */}
                    {/* Amendment Type */}

                    <FormField
                      control={form.control}
                      name='amendmentType'
                      render={() => (
                        <FormItem className='mt-4'>
                          <FormLabel>
                            Amendment Type{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <div className='flex flex-wrap space-x-4 mt-2'>
                            {[
                              'Religion',
                              'Designation (On Residence Visa)',
                              'Nationality',
                              'Passport Number',
                              'Applicant Name',
                            ].map((type) => (
                              <div
                                key={type}
                                className='flex items-center space-x-2'
                              >
                                <Checkbox
                                  id={type}
                                  checked={selectedAmendments.includes(type)}
                                  onCheckedChange={(checked) =>
                                    handleCheckboxChange(type, checked === true)
                                  }
                                />
                                <label
                                  className='text-sm font-normal ml-2'
                                  htmlFor={type}
                                >
                                  {type}
                                </label>
                              </div>
                            ))}
                          </div>
                          <FormDescription>
                            Please choose which of the following requires
                            Amendment. You can select up to 2 options.
                          </FormDescription>
                          <FormMessage>
                            {maxSelectionError
                              ? 'You can choose only up to 2 choice(s).'
                              : form.formState.errors.amendmentType?.message}
                          </FormMessage>
                        </FormItem>
                      )}
                    />
                  </>
                )}

                {/* show these when "Amendment Type" is 'Religion' */}

                {/* Instructions Text */}
                {selectedAmendments.includes('Religion') && (
                  <>
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle className='underline'>
                          REQUIRED DOCUMENTS :
                        </AlertTitle>
                        <AlertDescription>
                          Please provide an official document (in
                          Arabic/English) confirming your religion has been
                          amended.
                        </AlertDescription>
                      </div>
                    </Alert>

                    {/* Religion Amendment Official Document */}

                    <FormField
                      control={form.control}
                      name='amendmentOfficialDocument'
                      render={() => (
                        <FormItem>
                          <FormLabel>
                            Religion Amendment Official Document{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <FileUploadField
                              accept='.pdf,.jpg,.jpeg,.png'
                              onchoose={(file) => {
                                form.setValue(
                                  'amendmentOfficialDocument',
                                  file || ''
                                )
                                form.clearErrors('amendmentOfficialDocument')
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}

                {/* Updated Passport Sized Picture*/}

                {(selectedAmendments.includes('Nationality') ||
                  selectedAmendments.includes('Passport Number')) && (
                  <>
                    <FormField
                      control={form.control}
                      name='updatedPassportSizedPicture'
                      render={() => (
                        <FormItem>
                          <FormLabel>Updated Passport Sized Picture</FormLabel>
                          <FormControl>
                            <FileUploadField
                              accept='.pdf,.jpg,.jpeg,.png'
                              onchoose={(file) => {
                                form.setValue(
                                  'updatedPassportSizedPicture',
                                  file || ''
                                )
                                form.clearErrors('updatedPassportSizedPicture')
                              }}
                            />
                          </FormControl>
                          <FormDescription>
                            If you would like the amendment to reflect the new
                            picture, please upload an updated passport sized
                            picture. However, kindly note, if you would like to
                            change the picture after the amendment is complete,
                            there will be extra charges.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}
                {selectedAmendments.includes('Applicant Name') && (
                  <>
                    {/* Old Name */}
                    <div className='flex flex-wrap space-between gap-y-4'>
                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        <FormField
                          control={form.control}
                          name='oldName'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Old Name</FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  defaultValue={field.value || ''}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      {/* New Name */}
                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='newName'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>New Name</FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  defaultValue={field.value || ''}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  </>
                )}

                {/* New Passport Copy */}

                {selectedAmendments.some((type) =>
                  ['Applicant Name', 'Nationality', 'Passport Number'].includes(
                    type
                  )
                ) && (
                  <>
                    <div className='flex flex-wrap space-between gap-y-4'>
                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        <FormField
                          control={form.control}
                          name='newPassportCopy'
                          render={() => (
                            <FormItem>
                              <FormLabel>
                                New Passport Copy{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <FileUploadField
                                  accept='.pdf,.jpg,.jpeg,.png'
                                  onchoose={(file) => {
                                    form.setValue('newPassportCopy', file || '')
                                    form.clearErrors('newPassportCopy')
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Old Passport Copy */}
                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='oldPassportCopy'
                          render={() => (
                            <FormItem>
                              <FormLabel>
                                Old Passport Copy{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <FileUploadField
                                  accept='.pdf,.jpg,.jpeg,.png'
                                  onchoose={(file) => {
                                    form.setValue('oldPassportCopy', file || '')
                                    form.clearErrors('oldPassportCopy')
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  </>
                )}

                {/* show these when "Amendment Type" is 'Designation (On Residence Visa)' */}
                {selectedAmendments.includes(
                  'Designation (On Residence Visa)'
                ) && (
                  <>
                    {/* Current Designation */}
                    <div className='flex flex-wrap space-between gap-y-4'>
                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        <FormField
                          control={form.control}
                          name='currentDesignation'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Current Designation{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder='Select Current Designation' />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {[
                                      'Administrative Assistant',
                                      'Administrative Officer',
                                      'Archive Clerk',
                                      'Clerk Assistant',
                                      'Company Clerk',
                                      'Supervisor',
                                      'General Manager',
                                      'Investor',
                                      'Marketing Assistant',
                                      'Partner',
                                      'Reception Officer',
                                      'Receptionist',
                                      'Sales Representative',
                                      'Secretary',
                                      'Cook',
                                      'Executed Assistant',
                                      'Cleaner General',
                                      'Office Clerk General',
                                      'Administrative Director',
                                      'Accountant',
                                      'Managing Director',
                                      'Assistant Manager',
                                      'Administrative Advisor',
                                      'Sales Supervisor',
                                      'Sales Officer',
                                      'Administrative Supervisor',
                                      'Assistant Managing Director',
                                      'HR Director',
                                      'HR Manager',
                                      'Marketing Manager',
                                      'Office Manager',
                                      'Operations Manager',
                                      'Sales Manager',
                                      'Vice President',
                                      'Web Developer',
                                      'Software Specialist',
                                      'Finance Manager',
                                      'Engineering Manager',
                                      'Electronics Engineer',
                                      'Chairman of the board',
                                      'Senior Officer of Legal Affairs',
                                      'Human Resource Director',
                                    ].map((designation) => (
                                      <SelectItem
                                        key={designation}
                                        value={designation}
                                      >
                                        {designation}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </FormControl>
                              <FormDescription>
                                Please select the current designation of the
                                employee/shareholder :
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* New Designation */}
                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='newDesignation'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                New Designation{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder='Select New Designation' />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {[
                                      'Administrative Assistant',
                                      'Administrative Officer',
                                      'Archive Clerk',
                                      'Clerk Assistant',
                                      'Company Clerk',
                                      'Supervisor',
                                      'General Manager',
                                      'Investor',
                                      'Marketing Assistant',
                                      'Partner',
                                      'Reception Officer',
                                      'Receptionist',
                                      'Sales Representative',
                                      'Secretary',
                                      'Cook',
                                      'Executed Assistant',
                                      'Cleaner General',
                                      'Office Clerk General',
                                      'Administrative Director',
                                      'Accountant',
                                      'Managing Director',
                                      'Assistant Manager',
                                      'Administrative Advisor',
                                      'Sales Supervisor',
                                      'Sales Officer',
                                      'Administrative Supervisor',
                                      'Assistant Managing Director',
                                      'HR Director',
                                      'HR Manager',
                                      'Marketing Manager',
                                      'Office Manager',
                                      'Operations Manager',
                                      'Sales Manager',
                                      'Vice President',
                                      'Web Developer',
                                      'Software Specialist',
                                      'Finance Manager',
                                      'Engineering Manager',
                                      'Electronics Engineer',
                                      'Chairman of the board',
                                      'Senior Officer of Legal Affairs',
                                      'Human Resource Director',
                                    ].map((designation) => (
                                      <SelectItem
                                        key={designation}
                                        value={designation}
                                      >
                                        {designation}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </FormControl>
                              <FormDescription>
                                Please select the preferred designation of the
                                employee/shareholder :
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    {/* Attested MOFA degree */}

                    <FormField
                      control={form.control}
                      name='attestedMofaDegree'
                      render={() => (
                        <FormItem>
                          <FormLabel>Attested MOFA degree</FormLabel>
                          <FormControl>
                            <FileUploadField
                              accept='.jpg,.jpeg,.png'
                              onchoose={(file) => {
                                form.setValue('attestedMofaDegree', file || '')
                                form.clearErrors('attestedMofaDegree')
                              }}
                            />
                          </FormControl>
                          <FormDescription>
                            If your name is not mentioned on the TL, an attested
                            MOFA degree will be required to proceed with the
                            above designation
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}

                {/* Is this cancellation required for a golden visa ? */}
                {form.watch('letterDocumentsRequired') ===
                  'Dubai Residence Visa Cancellation (Inside UAE)' && (
                  <>
                    <FormField
                      control={form.control}
                      name='isCancellationRequired'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Is this cancellation required for a golden visa ?
                          </FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder='Select ' />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value='Yes'>Yes</SelectItem>
                              <SelectItem value='No'>No</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}
                {form.watch('isCancellationRequired') === 'Yes' && (
                  <>
                    <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
                      <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                        <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
                      </span>
                      <div className='flex space-y-2 flex-col ml-3'>
                        <AlertDescription>
                          <p className='text-sm text-slate-800 dark:text-slate-400'>
                            The applicant should stay inside the UAE until the
                            visa is cancelled.
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>

                    {/* Please provide the email confirmation for golden visa pre-approval */}
                    <FormField
                      control={form.control}
                      name='goldenVisa'
                      render={() => (
                        <FormItem>
                          <FormLabel>
                            Please provide the email confirmation for golden
                            visa pre-approval.{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <FileUploadField
                              accept='.pdf,.jpg,.jpeg,.png'
                              onchoose={(file) => {
                                form.setValue('goldenVisa', file || '')
                                form.clearErrors('goldenVisa')
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}

                {/* show these when "Letter/Documents Required" is 'E-Visa Cancellation'*/}
                {form.watch('letterDocumentsRequired') ===
                  'E-Visa Cancellation' && (
                  <>
                    {/* Instructions Text */}
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle className='underline'>
                          IMPORTANT NOTES :
                        </AlertTitle>
                        <AlertDescription>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            For applicants with unfit medical tests, the
                            cancellation process will be different. Incorrect
                            requests might result in delays/rejection.
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Please send an email to{' '}
                            <a
                              href='mailto:<EMAIL>'
                              className='text-blue-500 underline'
                              rel='noopener noreferrer'
                              target='_blank'
                            >
                              <EMAIL>
                            </a>{' '}
                            with the company details, alongside a copy of the
                            applicant's medical unfit test & the team will
                            assist you.
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>

                    {/* Was the applicant declared medically unfit : Radio ? */}

                    <FormField
                      control={form.control}
                      name='wasApplicantDeclared'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Was the applicant declared medically unfit ?{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <div data-field='wasApplicantDeclared'>
                            <FormControl>
                              <RadioGroup
                                className='flex align-center'
                                value={field.value}
                                onValueChange={field.onChange}
                              >
                                <RadioGroupItem
                                  className='mt-1'
                                  value='Yes'
                                  id='yes'
                                />
                                <label htmlFor='yes'>Yes</label>

                                <RadioGroupItem
                                  className='mt-1'
                                  value='No'
                                  id='no'
                                />
                                <label htmlFor='no'>No</label>
                              </RadioGroup>
                            </FormControl>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {form.watch('wasApplicantDeclared') === 'Yes' && (
                      <>
                        {/* Confirmed flight ticket : FileUploadField*/}

                        <FormField
                          control={form.control}
                          name='flightTicket'
                          render={() => (
                            <FormItem>
                              <FormLabel>
                                Confirmed flight ticket{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <FileUploadField
                                  accept='.pdf,.jpg,.jpeg,.png'
                                  onchoose={(file) => {
                                    form.setValue('flightTicket', file || '')
                                    form.clearErrors('flightTicket')
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        {/* DHA letter : FileUploadField*/}

                        <FormField
                          control={form.control}
                          name='dhaLetter'
                          render={() => (
                            <FormItem>
                              <FormLabel>
                                DHA letter{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <FileUploadField
                                  accept='.pdf,.jpg,.jpeg,.png'
                                  onchoose={(file) => {
                                    form.setValue('dhaLetter', file || '')
                                    form.clearErrors('dhaLetter')
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        {/* Unfit medical test result : FileUploadField*/}

                        <FormField
                          control={form.control}
                          name='medicalTestResult'
                          render={() => (
                            <FormItem>
                              <FormLabel>
                                Unfit medical test result{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <FileUploadField
                                  accept='.pdf,.jpg,.jpeg,.png'
                                  onchoose={(file) => {
                                    form.setValue(
                                      'medicalTestResult',
                                      file || ''
                                    )
                                    form.clearErrors('medicalTestResult')
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                     </>
                    )}
                        {/* Instructions Text */}

                        <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
                          <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                            <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
                          </span>
                          <div className='flex flex-col ml-2'>
                            <AlertDescription>
                              <p className='text-sm text-slate-800 dark:text-slate-400'>
                                Applicant will be required to complete an eye
                                scan with our PRO before the cancellation can be
                                finalized{' '}
                              </p>
                            </AlertDescription>
                          </div>
                        </Alert>
                  </>
                )}
                {/* Instructions Text */}
                {(form.watch('letterDocumentsRequired') ===
                  'Dubai Residence Visa Cancellation (Outside UAE) - Less than 6 Months' ||
                  form.watch('letterDocumentsRequired') ===
                    'Dubai Residence Visa Cancellation (Outside UAE) - More than 6 Months') && (
                  <>
                    <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
                      <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                        <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertDescription>
                          <p className='text-sm text-slate-800 dark:text-slate-400'>
                            If you are applying for a Golden visa, we cannot
                            process the visa to be cancelled outside the UAE.
                            Please process the cancellation once you are inside
                            the UAE.{' '}
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>

                    {/* Instructions Text */}
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle className='underline'>
                          IMPORTANT NOTE:
                        </AlertTitle>
                        <AlertDescription>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            For investor/partner designation, please provide us
                            with :{' '}
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Partner designation: Sale of Shares Document - a
                            letter with the company letter head, confirming
                            he/she will be giving all the shares to the other
                            partner
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Investor designation: Sale of Shares Document - a
                            letter with the company letter head explaining why
                            the applicant is cancelling the visa and what will
                            happen to the existing shares
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>
                  </>
                )}
                {/* Instructions Text */}
                {form.watch('letterDocumentsRequired') ===
                  'Dubai Residence Visa Cancellation (Outside UAE) - Less than 6 Months' && (
                  <>
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle className='underline'>
                          REQUIRED DOCUMENTS :
                        </AlertTitle>
                        <AlertDescription>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            These are the required documents you will need in
                            order to proceed with the application. Please
                            confirm that you have the following documents :
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['1.'] before:absolute before:left-0 before:top-0">
                            Signed End of Service Letter by the Employee and the
                            company, as well as the company stamp.{' '}
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Form can be downloaded from this{' '}
                            <a
                              href='https://files.ifza.com/external/file/lx6sy530d9fbe09e145b9ba0c786559b3b493'
                              className='text-blue-500 underline'
                              rel='noopener noreferrer'
                              target='_blank'
                            >
                              link
                            </a>
                          </p>

                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['2.'] before:absolute before:left-0 before:top-0">
                            Original passport must be submitted to IFZA
                            Reception :{' '}
                          </p>

                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Dubai Digital Park, A2 Building, Dubai Silicon Oasis{' '}
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Google Pin :{' '}
                            <a
                              href='https://www.google.com/maps/place/IFZA+DDP+Building+A2/@25.1188248,55.3778838,17z/data=!3m1!4b1!4m6!3m5!1s0x3e5f6570d49d452f:0x5993cfcf524af8a7!8m2!3d25.1188248!4d55.3778838!16s%2Fg%2F11qm3qmg1c?entry=ttu&g_ep=EgoyMDI1MDMwMy4wIKXMDSoJLDEwMjExNDUzSAFQAw%3D%3D'
                              className='text-blue-500 underline'
                              rel='noopener noreferrer'
                              target='_blank'
                            >
                              IFZA DUBAI
                            </a>
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>
                  </>
                )}
                {/* Instructions Text */}
                {form.watch('letterDocumentsRequired') ===
                  'Dubai Residence Visa Cancellation (Outside UAE) - More than 6 Months' && (
                  <>
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle className='underline'>
                          REQUIRED DOCUMENTS :
                        </AlertTitle>
                        <AlertDescription>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['1.'] before:absolute before:left-0 before:top-0">
                            Signed End of Service Letter by the Employee and the
                            company, as well as the company stamp.
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['2.'] before:absolute before:left-0 before:top-0">
                            A letter approved by the company, confirming that
                            the employee does not wish to return for employment
                            and there are no outstanding payments due to the
                            employee.{' '}
                          </p>

                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Form can be downloaded from this{' '}
                            <a
                              href='https://files.ifza.com/external/file/lx6sy530d9fbe09e145b9ba0c786559b3b493'
                              className='text-blue-500 underline'
                              rel='noopener noreferrer'
                              target='_blank'
                            >
                              link
                            </a>
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>
                  </>
                )}
                {/* show these when "Letter/Documents Required" is 'Dubai Residence Visa Cancellation (Inside UAE)''
                 */}
                {form.watch('letterDocumentsRequired') ===
                  'Dubai Residence Visa Cancellation (Inside UAE)' && (
                  <>
                    {/* Instructions Text */}
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle className='underline'>
                          IMPORTANT NOTE :
                        </AlertTitle>
                        <AlertDescription>
                          <p className='text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content- before:left-0 before:top-0'>
                            For investor/partner designation, please provide us
                            with :
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Partner designation: Sale of Shares Document - a
                            letter with the company letter head, confirming
                            he/she will be giving all the shares to the other
                            partner{' '}
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Investor designation: Sale of Shares Document - a
                            letter with the company letter head explaining why
                            the applicant is cancelling the visa and what will
                            happen to the existing shares
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>

                    {/* Instructions Text */}
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle className='underline'>
                          REQUIRED DOCUMENTS :
                        </AlertTitle>
                        <AlertDescription>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            These are the required documents you will need in
                            order to proceed with the application. Please
                            confirm that you have the following documents :
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['1.'] before:absolute before:left-0 before:top-0">
                            Signed End of Service Letter by the Employee and the
                            company, as well as the company stamp.
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Form can be downloaded from this{' '}
                            <a
                              href='https://files.ifza.com/external/file/lx6sy530d9fbe09e145b9ba0c786559b3b493'
                              target='_blank'
                              rel='noopener noreferrer'
                              className='text-blue-500 underline'
                            >
                              link.
                            </a>
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>
                  </>
                )}

                {/* Please upload the documents */}
                {(form.watch('letterDocumentsRequired') ===
                  'Dubai Residence Visa Cancellation (Inside UAE)' ||
                  form.watch('letterDocumentsRequired') ===
                    'Dubai Residence Visa Cancellation (Outside UAE) - Less than 6 Months' ||
                  form.watch('letterDocumentsRequired') ===
                    'Dubai Residence Visa Cancellation (Outside UAE) - More than 6 Months' ||
                  form.watch('letterDocumentsRequired') ===
                    'Fujairah Residence Visa Cancellation (Inside UAE)' ||
                  form.watch('letterDocumentsRequired') ===
                    'Fujairah Residence Visa Cancellation (Outside UAE) - Less than 6 Months' ||
                  form.watch('letterDocumentsRequired') ===
                    'Fujairah Residence Visa Cancellation (Outside UAE) - More than 6 Months' ||
                  form.watch('letterDocumentsRequired') ===
                    'Re-entry Pass (Outside UAE for more than 6 months)' ||
                  form.watch('letterDocumentsRequired') ===
                    'Work Permit Cancellation') && (
                  <>
                    <FormField
                      control={form.control}
                      name='requiredDocuments'
                      render={() => (
                        <FormItem>
                          <FormLabel>
                            Please upload the documents{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <FileUploadField
                              accept='.pdf,.jpg,.jpeg,.png'
                              onchoose={(file) => {
                                form.setValue('requiredDocuments', file || '')
                                form.clearErrors('requiredDocuments')
                              }}
                            />
                          </FormControl>
                          <FormDescription>
                            Please make sure the Document is Signed and stamped
                            by the relevant parties.{' '}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}
                {form.watch('letterDocumentsRequired') ===
                  'Re-entry Pass (Outside UAE for more than 6 months)' && (
                  <>
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle className='underline'>
                          IMPORTANT NOTE :
                        </AlertTitle>
                        <AlertDescription>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Additional charge of AED 100 will be charged for any
                            additional month / 30 days starting from the 7th
                            month from the date of exit.{' '}
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>
                  </>
                )}

                {/* Upload RV */}
                {(form.watch('letterDocumentsRequired') ===
                  'Dubai Residence Visa Cancellation (Inside UAE)' ||
                  form.watch('letterDocumentsRequired') ===
                    'Dubai Residence Visa Cancellation (Outside UAE) - Less than 6 Months' ||
                  form.watch('letterDocumentsRequired') ===
                    'Dubai Residence Visa Cancellation (Outside UAE) - More than 6 Months' ||
                  form.watch('letterDocumentsRequired') ===
                    'Fujairah Residence Visa Cancellation (Inside UAE)' ||
                  form.watch('letterDocumentsRequired') ===
                    'Fujairah Residence Visa Cancellation (Outside UAE) - Less than 6 Months' ||
                  form.watch('letterDocumentsRequired') ===
                    'Fujairah Residence Visa Cancellation (Outside UAE) - More than 6 Months' ||
                  form.watch('letterDocumentsRequired') ===
                    'Re-entry Pass (Outside UAE for more than 6 months)' ||
                  form.watch('letterDocumentsRequired') ===
                    'Salary Certificate') && (
                  <>
                    <FormField
                      control={form.control}
                      name='uploadRv'
                      render={() => (
                        <FormItem>
                          <FormLabel>
                            Upload RV <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <FileUploadField
                              accept='.pdf,.jpg,.jpeg,.png'
                              onchoose={(file) => {
                                form.setValue('uploadRv', file || '')
                                form.clearErrors('uploadRv')
                              }}
                            />
                          </FormControl>
                          <FormDescription>
                            Please make sure to upload Residence Visa.{' '}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}

                {/* Passport Copy */}
                {(form.watch('letterDocumentsRequired') ===
                  'Dubai Residence Visa Cancellation (Inside UAE)' ||
                  form.watch('letterDocumentsRequired') ===
                    'Dubai Residence Visa Cancellation (Outside UAE) - Less than 6 Months' ||
                  form.watch('letterDocumentsRequired') ===
                    'Dubai Residence Visa Cancellation (Outside UAE) - More than 6 Months' ||
                  form.watch('letterDocumentsRequired') ===
                    'Re-entry Pass (Outside UAE for more than 6 months)') && (
                  <>
                    <FormField
                      control={form.control}
                      name='passportCo'
                      render={() => (
                        <FormItem>
                          <FormLabel>
                            Passport Copy{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <FileUploadField
                              accept='.pdf,.jpg,.jpeg,.png'
                              onchoose={(file) => {
                                form.setValue('passportCo', file || '')
                                form.clearErrors('passportCo')
                              }}
                            />
                          </FormControl>
                          <FormDescription>
                            If the passport with the RV has expired, please
                            upload the renewed passport copy.{' '}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}
                {/* show this when "Letter/Documents Required" is 'E-Visa Withdrawal' */}
                {form.watch('letterDocumentsRequired') ===
                  'E-Visa Withdrawal' && (
                  <>
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle className='underline'>
                          IMPORTANT NOTES :
                        </AlertTitle>
                        <AlertDescription>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Kindly note, withdrawal is only applicable if the
                            application was submitted, but the E-Visa was not
                            issued
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            *Only at E-visa stage would withdrawal be possible,
                            not at RV stage There can only be two instances for
                            this :
                          </p>

                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['1.'] before:absolute before:left-0 before:top-0">
                            The client needs to urgently travel, whilst the
                            e-visa is under process and has not been issued yet
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['2.'] before:absolute before:left-0 before:top-0">
                            The application is in MOFA (for Bangladeshi,
                            Egyptian, Indonesian, Kenyan, Malaysian, Sri Lanka &
                            Tunisian nationals only), and the client does not
                            want to proceed
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>
                  </>
                )}
                {/* show these when "Letter/Documents Required" is 'Employee List' */}
                {form.watch('letterDocumentsRequired') === 'Employee List' && (
                  <>
                    {/* Instructions Text */}
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle className='underline'>
                          IMPORTANT NOTE :
                        </AlertTitle>
                        <AlertDescription>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            The employee list will show the applicant's with
                            active RV's, not company members.
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>

                    {/* "Letter should be addressed to :" field */}

                    <FormField
                      control={form.control}
                      name='letterShouldBeAddressedTo'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Letter should be addressed to :{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder='Enter Letter should be addressed to'
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Please specify who the letter should be addressed
                            to. For example: Dubai Immigration, Bank Name, RTA.
                            Letters cannot be addressed to "To Whom It May
                            Concern" or Banks which not in UAE.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}
                {/* Instructions Text */}
                {(form.watch('letterDocumentsRequired') ===
                  'Fujairah Residence Visa Cancellation (Inside UAE)' ||
                  form.watch('letterDocumentsRequired') ===
                    'Fujairah Residence Visa Cancellation (Outside UAE) - Less than 6 Months' ||
                  form.watch('letterDocumentsRequired') ===
                    'Fujairah Residence Visa Cancellation (Outside UAE) - More than 6 Months') && (
                  <>
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle className='underline'>
                          REQUIRED DOCUMENTS :
                        </AlertTitle>
                        <AlertDescription>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            These are the required documents you will need in
                            order to proceed with the application. Please
                            confirm that you have the following documents :{' '}
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['1.'] before:absolute before:left-0 before:top-0">
                            Signed Declaration Form & Cancellation Form.{' '}
                          </p>

                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            The documents mentioned above can be downloaded from
                            the following :{' '}
                            <a
                              href='https://files.ifza.com/external/a7ca900a58fae63ecb99d127896e6f14758251975507023cc610cdcfa1cf25f2'
                              className='text-blue-500 underline'
                              rel='noopener noreferrer'
                              target='_blank'
                            >
                              link
                            </a>{' '}
                          </p>

                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['2.'] before:absolute before:left-0 before:top-0">
                            Original passport and Emirates ID must be submitted
                            to IFZA Reception :{' '}
                          </p>

                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Dubai Digital Park, A2 Building, Dubai Silicon Oasis{' '}
                          </p>

                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Google Pin :{' '}
                            <a
                              href='https://www.google.com/maps/place/IFZA+DDP+Building+A2/@25.1188296,55.3756951,17z/data=!3m1!4b1!4m5!3m4!1s0x3e5f6570d49d452f:0x5993cfcf524af8a7!8m2!3d25.1188248!4d55.3778838?shorturl=1'
                              className='text-blue-500 underline'
                              rel='noopener noreferrer'
                              target='_blank'
                            >
                              IFZA Dubai
                            </a>{' '}
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>

                    {/* Instructions Text */}
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle className='underline'>
                          IMPORTANT NOTES :
                        </AlertTitle>
                        <AlertDescription>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['1.'] before:absolute before:left-0 before:top-0">
                            Kindly ensure that the Trade License is valid for a
                            minimum of three months to proceed with your
                            cancellation. If not, please contact our renewals
                            department ({' '}
                            <a
                              href='mailto:<EMAIL>'
                              className='text-blue-500 '
                              rel='noopener noreferrer'
                              target='_blank'
                            >
                              <EMAIL>
                            </a>{' '}
                            )
                          </p>

                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['2.'] before:absolute before:left-0 before:top-0">
                            If the applicant will renew the visa with IFZA
                            (Dubai), the cancellation charges will be waived
                            off, given the payment for visa is done advance.
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            PS- If you want to renew your visa, please call us
                            at our toll-free number :
                            <span className='text-red-500'>
                              {' '}
                              800-IFZA (4392), EXT 353.
                            </span>
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>
                  </>
                )}
                {/* show these when "Letter/Documents Required" is 'Labour Card'*/}
                {form.watch('letterDocumentsRequired') === 'Labour Card' && (
                  <>
                    {/* Instructions Text */}
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle className='underline'>
                          IMPORTANT NOTES :
                        </AlertTitle>
                        <AlertDescription>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Kindly note, the issuance of the labour card takes
                            up to 3-4 weeks.
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Once it issued, you will be notified on your
                            registered email address.
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>
                  </>
                )}
                {/* Instructions Text */}
                {form.watch('letterDocumentsRequired') ===
                  'Re-entry (At the airport)' && (
                  <>
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle className='underline text-red-500'>
                          IMPORTANT NOTES :
                        </AlertTitle>
                        <div className='flex flex-col ml-2'>
                          <AlertTitle className='underline text-red-500 dark:text-red-500'>
                            This service is offered as a priority service.
                            However, the re-entry permits can only be issued
                            during business hours.
                          </AlertTitle>
                        </div>
                        <AlertDescription>
                          <p className="text-sm text-red-500 dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Investor/Partner Visa Holders can stay outside UAE
                            for up to 12 months.
                          </p>
                          <p className="text-sm text-red-500 dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Employment Visa Holders are required to enter the
                            UAE once every 6 months.
                          </p>
                          <p className="text-sm text-red-500 dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Additional Charge of AED 100 will be charged for any
                            additional month / 30 days starting from the 7th
                            month from the date of exit.
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>
                  </>
                )}

                {form.watch('letterDocumentsRequired') ===
                  'Re-entry (At the airport)' && (
                  <>
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle className='underline '>
                          REQUIRED DOCUMENTS :
                        </AlertTitle>
                        <AlertDescription>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Signed letter (with company letterhead and stamp)
                            explaining why the applicant has been outside the
                            UAE for more than 6 months.
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Any additional documents like bank statements/flight
                            tickets will be beneficial, but not mandatory.
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>

                    {/* Signed Letter ( with company letterhead) */}

                    <FormField
                      control={form.control}
                      name='signedLetter'
                      render={() => (
                        <FormItem>
                          <FormLabel>
                            Signed Letter ( with company letterhead ){' '}
                            <span style={{ color: 'red' }}>*</span>
                          </FormLabel>
                          <FormControl>
                            <FileUploadField
                              accept='.pdf,.jpg,.jpeg,.png'
                              onchoose={(file) => {
                                form.setValue('signedLetter', file || '')
                                form.clearErrors('signedLetter')
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {/* Residence Visa */}

                    <FormField
                      control={form.control}
                      name='residenceVisa'
                      render={() => (
                        <FormItem>
                          <FormLabel>
                            Residence Visa{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <FileUploadField
                              accept='.pdf,.jpg,.jpeg,.png'
                              onchoose={(file) => {
                                form.setValue('residenceVisa', file || '')
                                form.clearErrors('residenceVisa')
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {/*Passport Copy */}
                    <FormField
                      control={form.control}
                      name='passportCopy'
                      render={() => (
                        <FormItem>
                          <FormLabel>
                            Passport Copy{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <FileUploadField
                              accept='.pdf,.jpg,.jpeg,.png'
                              onchoose={(file) => {
                                form.setValue('passportCopy', file || '')
                                form.clearErrors('passportCopy')
                              }}
                            />
                          </FormControl>

                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}

                {/* Last Date of Exit */}
                {(form.watch('letterDocumentsRequired') ===
                  'Re-entry (At the airport)' ||
                  form.watch('letterDocumentsRequired') ===
                    'Re-entry Pass (Outside UAE for more than 6 months)') && (
                  <>
                    <FormField
                      control={form.control}
                      name='lastDateOfExit'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Last date of Exit{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <DateTimePicker
                            granularity='day'
                            value={field.value}
                            onChange={field.onChange}
                          />
                          <FormMessage />
                          <FormDescription>dd-MMM-yyyy</FormDescription>
                        </FormItem>
                      )}
                    />
                  </>
                )}

                {form.watch('letterDocumentsRequired') ===
                  'Salary Amendment Letter' && (
                  <>
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle className='underline'>
                          REQUIRED DOCUMENTS :
                        </AlertTitle>
                        <AlertDescription>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['1.'] before:absolute before:left-0 before:top-0">
                            Signed and stamped, Salary Amendment Letter. This
                            can be downloaded at the following :{' '}
                            <a
                              href='https://files.ifza.com/external/e364894ec4535ee62f056976a4a3a8711e7f45842ca93ead76ddcc7ccab71a36'
                              target='_blank'
                              rel='noopener noreferrer'
                              className='text-blue-500 underline'
                            >
                              Link
                            </a>
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['2.'] before:absolute before:left-0 before:top-0">
                            Bank statement of the applicant reflecting the new
                            salary (bank statement should reflect at least 1
                            month salary credited into the account of the
                            applicant)
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>

                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle className='underline text-red-500 dark:text-red-500'>
                          NOTE :
                        </AlertTitle>
                        <AlertDescription>
                          <p className="text-sm text-red-500 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            If you wish to apply for a salary certificate
                            reflecting the amended salary, please await our
                            confirmation that the salary amendment has been
                            completed. You will be notified on your registered
                            email address.
                          </p>
                          <p className="text-sm text-red-500 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            If, however, you submit both the requests at the
                            same time, the salary certificate will reflect the
                            old salary. In this case, you will need to submit a
                            new request and your money will not be refunded.
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>

                    <div className='flex flex-wrap space-between gap-y-4'>
                      {/* Salary Amendment Letter */}

                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        <FormField
                          control={form.control}
                          name='salaryAmendmentLetter'
                          render={() => (
                            <FormItem>
                              <FormLabel>
                                Salary Amendment Letter{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <FileUploadField
                                  accept='.pdf,.jpg,.jpeg,.png'
                                  onchoose={(file) => {
                                    form.setValue(
                                      'salaryAmendmentLetter',
                                      file || ''
                                    )
                                    form.clearErrors('salaryAmendmentLetter')
                                  }}
                                />
                              </FormControl>
                              <FormDescription>
                                Please upload the signed and stamped amendment
                                letter.
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      {/* Bank Statement */}
                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='bankStatement'
                          render={() => (
                            <FormItem>
                              <FormLabel>
                                Bank Statement{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <FileUploadField
                                  accept='.pdf,.jpg,.jpeg,.png'
                                  onchoose={(file) => {
                                    form.setValue('bankStatement', file || '')
                                    form.clearErrors('bankStatement')
                                  }}
                                />
                              </FormControl>

                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    {/* Current Basic */}

                    <div className='flex flex-wrap space-between gap-y-4'>
                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        <FormField
                          control={form.control}
                          name='currentBasic'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Current Basic{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type='number'
                                  placeholder='Enter'
                                  value={localCurrnetBasic}
                                  onChange={(e) => {
                                    const value = e.target.value
                                    setLocalCurrentBasic(value)

                                    if (value === '') {
                                      field.onChange(undefined)
                                      form.setError('currentBasic', {
                                        type: 'manual',
                                        message: 'This field is required',
                                      })
                                    } else {
                                      const numberValue = Number(value)
                                      if (!isNaN(numberValue)) {
                                        field.onChange(numberValue)
                                        form.clearErrors('currentBasic')
                                      }
                                    }
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Current Accommodation */}

                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='currentAccommodation'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Current Accommodation{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type='number'
                                  placeholder='Enter'
                                  value={localCurrentAccommodation}
                                  onChange={(e) => {
                                    const value = e.target.value
                                    setLocalCurrentAccommodation(value)

                                    if (value === '') {
                                      field.onChange(undefined)
                                      form.setError('currentAccommodation', {
                                        type: 'manual',
                                        message: 'This field is required',
                                      })
                                    } else {
                                      const numberValue = Number(value)
                                      if (!isNaN(numberValue)) {
                                        field.onChange(numberValue)
                                        form.clearErrors('currentAccommodation')
                                      }
                                    }
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    {/* Current Transportation */}
                    <div className='flex flex-wrap space-between gap-y-4'>
                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        <FormField
                          control={form.control}
                          name='currentTransportation'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Current Transportation{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type='number'
                                  placeholder='Enter'
                                  value={localCurrentTransportation}
                                  onChange={(e) => {
                                    const value = e.target.value
                                    setLocalCurrentTransportation(value)

                                    if (value === '') {
                                      field.onChange(undefined)
                                      form.setError('currentTransportation', {
                                        type: 'manual',
                                        message: 'This field is required',
                                      })
                                    } else {
                                      const numberValue = Number(value)
                                      if (!isNaN(numberValue)) {
                                        field.onChange(numberValue)
                                        form.clearErrors(
                                          'currentTransportation'
                                        )
                                      }
                                    }
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      {/* Current Other Allowance */}

                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='currentOtherAllowance'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Current Other Allowance{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type='number'
                                  placeholder='Enter'
                                  value={localCurrentOtherAllowance}
                                  onChange={(e) => {
                                    const value = e.target.value
                                    setLocalCurrentOtherAllowance(value)

                                    if (value === '') {
                                      field.onChange(undefined)
                                      form.setError('currentOtherAllowance', {
                                        type: 'manual',
                                        message: 'This field is required',
                                      })
                                    } else {
                                      const numberValue = Number(value)
                                      if (!isNaN(numberValue)) {
                                        field.onChange(numberValue)
                                        form.clearErrors(
                                          'currentOtherAllowance'
                                        )
                                      }
                                    }
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                    <div className='flex flex-wrap space-between gap-y-4'>
                      {/* Current Salary */}
                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        <FormField
                          control={form.control}
                          name='currentSalary'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Current Salary{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  placeholder='Enter Current Salary'
                                  {...field}
                                />
                              </FormControl>
                              <FormDescription>
                                Please mention the current Salary as per your
                                signed Employment contract
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* New Basic */}

                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='newBasic'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                New Basic{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type='number'
                                  placeholder='Enter'
                                  value={localNewBasic}
                                  onChange={(e) => {
                                    const value = e.target.value
                                    setLocalNewBasic(value)

                                    if (value === '') {
                                      field.onChange(undefined)
                                      form.setError('newBasic', {
                                        type: 'manual',
                                        message: 'This field is required',
                                      })
                                    } else {
                                      const numberValue = Number(value)
                                      if (!isNaN(numberValue)) {
                                        field.onChange(numberValue)
                                        form.clearErrors('newBasic')
                                      }
                                    }
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                    {/* New Accommodation */}

                    <div className='flex flex-wrap space-between gap-y-4'>
                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        <FormField
                          control={form.control}
                          name='newAccommodation'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                New Accommodation{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type='number'
                                  placeholder='Enter'
                                  value={localNewAccommodation}
                                  onChange={(e) => {
                                    const value = e.target.value
                                    setLocalNewAccommodation(value)

                                    if (value === '') {
                                      field.onChange(undefined)
                                      form.setError('newAccommodation', {
                                        type: 'manual',
                                        message: 'This field is required',
                                      })
                                    } else {
                                      const numberValue = Number(value)
                                      if (!isNaN(numberValue)) {
                                        field.onChange(numberValue)
                                        form.clearErrors('newAccommodation')
                                      }
                                    }
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      {/* New Transportation */}

                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='newTransportation'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                New Transportation{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type='number'
                                  placeholder='Enter'
                                  value={localNewTransportation}
                                  onChange={(e) => {
                                    const value = e.target.value
                                    setLocalNewTransportation(value)

                                    if (value === '') {
                                      field.onChange(undefined)
                                      form.setError('newTransportation', {
                                        type: 'manual',
                                        message: 'This field is required',
                                      })
                                    } else {
                                      const numberValue = Number(value)
                                      if (!isNaN(numberValue)) {
                                        field.onChange(numberValue)
                                        form.clearErrors('newTransportation')
                                      }
                                    }
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    {/* New Other Allowance */}

                    <div className='flex flex-wrap space-between gap-y-4'>
                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        <FormField
                          control={form.control}
                          name='newOtherAllowance'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                New Other Allowance{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type='number'
                                  placeholder='Enter'
                                  value={localNewOtherAllowance}
                                  onChange={(e) => {
                                    const value = e.target.value
                                    setLocalNewOtherAllowance(value)

                                    if (value === '') {
                                      field.onChange(undefined)
                                      form.setError('newOtherAllowance', {
                                        type: 'manual',
                                        message: 'This field is required',
                                      })
                                    } else {
                                      const numberValue = Number(value)
                                      if (!isNaN(numberValue)) {
                                        field.onChange(numberValue)
                                        form.clearErrors('newOtherAllowance')
                                      }
                                    }
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Amended Salary */}
                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='amendedSalary'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Amended Salary{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  placeholder='Enter Amended Salary'
                                  {...field}
                                />
                              </FormControl>
                              <FormDescription>
                                Please mention the new/amended salary
                              </FormDescription>

                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  </>
                )}

                {/* show these when "Letter/Documents Required" is "Salary Certificate" */}
                {form.watch('letterDocumentsRequired') ===
                  'Salary Certificate' && (
                  <>
                    <div className='flex flex-wrap space-between gap-y-4'>
                      {/* Service Type */}
                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        <FormField
                          control={form.control}
                          name='serviceType'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Service Type{' '}
                                <span style={{ color: 'red' }}>*</span>
                              </FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder='Select Service Type' />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value='VIP'>VIP</SelectItem>
                                  <SelectItem value='Standard'>
                                    Standard
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                Standard - 3 WD's; AED 250 | VIP - 24 hours; AED
                                350
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Reason for the Letter */}
                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='reasonForLetter'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Reason for the Letter{' '}
                                <span style={{ color: 'red' }}>*</span>
                              </FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder='Select Reason for the Letter' />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value='Immigration'>
                                    Immigration
                                  </SelectItem>
                                  <SelectItem value='Others'>Others</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                Please choose from the following dropdown menu.
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  </>
                )}

                {/* Show this field when "Reason for the lettet" is "Others" */}
                {form.watch('reasonForLetter') === 'Others' && (
                  <>
                    {/* Letter should be addressed to: */}
                    <FormField
                      control={form.control}
                      name='letterAddressedTo'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Letter should be addressed to :{' '}
                            <span style={{ color: 'red' }}>*</span>
                          </FormLabel>
                          <FormControl>
                            <Input placeholder='Please Specify ' {...field} />
                          </FormControl>
                          <FormDescription>
                            Please specify who the letter should be addressed
                            to. For example: Dubai Immigration, Bank Name, RTA.
                            Letters cannot be addressed to "To Whom It May
                            Concern" or Banks which not in UAE.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}

                {/* Instructions Text */}
                {form.watch('letterDocumentsRequired') ===
                  'Salary Certificate' && (
                  <>
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle className='underline'>
                          IMPORTANT NOTES :
                        </AlertTitle>
                        <AlertDescription>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Letters addressed to immigration will be in Arabic &
                            letters addressed to the bank, embassy or consulate
                            will be in English.
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Kindly note, the language of the letter cannot be
                            amended nor can the letter be issued in another
                            language.
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>
                  </>
                )}
                {/* show these when "Letter/Documents Required" is "NOC for Golden Visa" */}
                {form.watch('letterDocumentsRequired') ===
                  'NOC for Golden Visa' && (
                  <>
                    {/* Instructions Text */}
                    <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
                      <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                        <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
                      </span>
                      <div className='flex space-y-2 flex-col ml-3'>
                        <AlertDescription>
                          <p className='text-sm text-slate-800 dark:text-slate-400'>
                            The issuance of the No Objection Certificate (NOC)
                            is based on the applicant's confirmation that they
                            have independently verified their eligibility for
                            the Golden Visa with the relevant authorities. It is
                            important to note that the issuance of this NOC does
                            not guarantee or constitute eligibility for, or
                            approval of, the Golden Visa.
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>

                    {/* Is the NOC for a Employee or Investor/Partner Visa ? */}
                    <FormField
                      control={form.control}
                      name='isNOCForEmployeeOrInvestor'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Is the NOC for a Employee or Investor / Partner Visa
                            ? <span className='text-red-500'>*</span>
                          </FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder='Choose' />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value='Employee Visa'>
                                Employee Visa
                              </SelectItem>
                              <SelectItem value='Investor / Partner Visa'>
                                Investor / Partner Visa
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}

                {/* show these when "Is the NOC for a Employee or Investor/Partner Visa ?" is "Employee Visa" */}
                {form.watch('isNOCForEmployeeOrInvestor') ===
                  'Employee Visa' && (
                  <>
                    {/* Is the minimum salary AED 30,000 ? */}

                    <FormField
                      control={form.control}
                      name='isMinimumSalaryAED30K'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Is the minimum salary AED 30,000 ?{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder='Choose' />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value='Yes'>Yes</SelectItem>
                              <SelectItem value='No'>No</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}

                {/* show these when "Is the NOC for a Employee or Investor/Partner Visa ?" is "Investor / Partner Visa" */}
                {form.watch('isNOCForEmployeeOrInvestor') ===
                  'Investor / Partner Visa' && (
                  <>
                    <FormField
                      control={form.control}
                      name='auditedFinancialReport'
                      render={() => (
                        <FormItem>
                          <FormLabel>
                            Audited financial reports{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <FileUploadField
                              accept='.jpg,.jpeg,.png'
                              onchoose={(file) => {
                                form.setValue(
                                  'auditedFinancialReport',
                                  file || ''
                                )
                                form.clearErrors('auditedFinancialReport')
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}

                <CardHeader className='px-0 pt-0 pb-0'>
                  <CardTitle>Company Details</CardTitle>
                </CardHeader>
                {/* Additional message */}
                <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
                  <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                    <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
                  </span>
                  <div className='flex space-y-2 flex-col ml-3'>
                    <AlertDescription>
                      <p className='text-sm text-slate-800 dark:text-slate-400'>
                        Please mention the details of the IFZA registered
                        company for which the letter/documents are required.
                      </p>
                    </AlertDescription>
                  </div>
                </Alert>
                {/* Registered Company Name */}

                <FormField
                  control={form.control}
                  name='registeredCompanyName'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Registered Company Name{' '}
                        <span className='text-red-500'>*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder='Enter Registered Company Name'
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Please mention the name exactly as per your Trade
                        License. If the name is not accurate it might cause
                        delays in issuance of the document.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Manager Name, as per Trade License */}
                {form.watch('letterDocumentsRequired') ===
                  'Salary Certificate' && (
                  <>
                    <FormField
                      control={form.control}
                      name='managerName'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Manager Name, as per Trade License{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder='Enter Manager Name'
                              {...field}
                            />
                          </FormControl>

                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}

                {/* Trade License Number */}
                <FormField
                  control={form.control}
                  name='tradeLicenseNumber'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Trade License Number{' '}
                        <span className='text-red-500'>*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder='Enter Trade License Number'
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Please mention the trade license number exactly per the
                        trade license. Please note: Incorrect/missing
                        information on the form will result in delays of
                        processing this application.{' '}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {/* Employee details Section*/}
                <CardHeader className='px-0 pt-0 pb-0'>
                  <CardTitle>Add Employee Details</CardTitle>
                </CardHeader>
                {/* Additional message */}
                <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
                  <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                    <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
                  </span>
                  <div className='flex space-y-2 flex-col ml-3'>
                    <AlertDescription>
                      <p className='text-sm text-slate-800 dark:text-slate-400'>
                        Please mention the details of the employee/member of the
                        company for whom the letter/documents are requested.
                      </p>
                    </AlertDescription>
                  </div>
                </Alert>
                {/* Title Dropdown */}
                <div className='flex space-x-4'>
                  <div className='flex-1'>
                    <div className='flex flex-col space-y-2'>
                      <div className='flex space-x-4 w-full'>
                        <FormField
                          control={form.control}
                          name='title'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Name <span className='text-red-500'>*</span>
                              </FormLabel>

                              <FormControl>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder='Title' />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value='Mr.'>Mr.</SelectItem>
                                    <SelectItem value='Mrs.'>Mrs.</SelectItem>
                                    <SelectItem value='Ms.'>Ms.</SelectItem>
                                    <SelectItem value='Dr.'>Dr.</SelectItem>
                                  </SelectContent>
                                </Select>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        {/* First Name Input Field */}
                        <FormField
                          control={form.control}
                          name='firstName'
                          render={({ field }) => (
                            <FormItem className='w-full'>
                              <FormLabel>First Name</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder='First Name' />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        {/* Middle Name Input Field */}
                        <FormField
                          control={form.control}
                          name='middleName'
                          render={({ field }) => (
                            <FormItem className='w-full'>
                              <FormLabel>Middle Name</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder='Middle Name' />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        {/* Last Name Input Field */}
                        <FormField
                          control={form.control}
                          name='lastName'
                          render={({ field }) => (
                            <FormItem className='w-full'>
                              <FormLabel>Last Name</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder='Last Name' />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {form.watch('letterDocumentsRequired') === 'Employee List' && (
                  <>
                    <div className='flex space-x-4'>
                      <div className='flex-1'>
                        <div className='flex flex-col space-y-2'>
                          <div className='flex space-x-4 w-full'>
                            {/* Title 2 */}
                            <FormField
                              control={form.control}
                              name='title2'
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Name 2</FormLabel>
                                  <FormControl>
                                    <Select
                                      onValueChange={field.onChange}
                                      defaultValue={field.value}
                                    >
                                      <SelectTrigger>
                                        <SelectValue placeholder='Title' />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value='Mr.'>Mr.</SelectItem>
                                        <SelectItem value='Mrs.'>
                                          Mrs.
                                        </SelectItem>
                                        <SelectItem value='Ms.'>Ms.</SelectItem>
                                        <SelectItem value='Dr.'>Dr.</SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='firstName2'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>First Name 2</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='First Name '
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='middleName2'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>Middle Name 2</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='Middle Name '
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='lastName2'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>Last Name 2</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='Last Name '
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Title 3 */}
                    <div className='flex space-x-4'>
                      <div className='flex-1'>
                        <div className='flex flex-col space-y-2'>
                          <div className='flex space-x-4 w-full'>
                            <FormField
                              control={form.control}
                              name='title3'
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Name 3 </FormLabel>
                                  <FormControl>
                                    <Select
                                      onValueChange={field.onChange}
                                      defaultValue={field.value}
                                    >
                                      <SelectTrigger>
                                        <SelectValue placeholder='Title' />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value='Mr.'>Mr.</SelectItem>
                                        <SelectItem value='Mrs.'>
                                          Mrs.
                                        </SelectItem>
                                        <SelectItem value='Ms.'>Ms.</SelectItem>
                                        <SelectItem value='Dr.'>Dr.</SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='firstName3'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>First Name 3</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='First Name '
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='middleName3'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>Middle Name 3</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='Middle Name '
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='lastName3'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>Last Name 3</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='Last Name '
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Title 4 */}
                    <div className='flex space-x-4'>
                      <div className='flex-1'>
                        <div className='flex flex-col space-y-2'>
                          <div className='flex space-x-4 w-full'>
                            <FormField
                              control={form.control}
                              name='title4'
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Name 4 </FormLabel>
                                  <FormControl>
                                    <Select
                                      onValueChange={field.onChange}
                                      defaultValue={field.value}
                                    >
                                      <SelectTrigger>
                                        <SelectValue placeholder='Title' />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value='Mr.'>Mr.</SelectItem>
                                        <SelectItem value='Mrs.'>
                                          Mrs.
                                        </SelectItem>
                                        <SelectItem value='Ms.'>Ms.</SelectItem>
                                        <SelectItem value='Dr.'>Dr.</SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='firstName4'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>First Name 4</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='First Name '
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='middleName4'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>Middle Name 4</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='Middle Name '
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='lastName4'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>Last Name 4</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='Last Name '
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Title 5 */}
                    <div className='flex space-x-4'>
                      <div className='flex-1'>
                        <div className='flex flex-col space-y-2'>
                          <div className='flex space-x-4 w-full'>
                            <FormField
                              control={form.control}
                              name='title5'
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Name 5 </FormLabel>
                                  <FormControl>
                                    <Select
                                      onValueChange={field.onChange}
                                      defaultValue={field.value}
                                    >
                                      <SelectTrigger>
                                        <SelectValue placeholder='Title' />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value='Mr.'>Mr.</SelectItem>
                                        <SelectItem value='Mrs.'>
                                          Mrs.
                                        </SelectItem>
                                        <SelectItem value='Ms.'>Ms.</SelectItem>
                                        <SelectItem value='Dr.'>Dr.</SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='firstName5'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>First Name 5</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='First Name '
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='middleName5'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>Middle Name 5</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='Middle Name '
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='lastName5'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>Last Name 5</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='Last Name '
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Title 6 */}
                    <div className='flex space-x-4'>
                      <div className='flex-1'>
                        <div className='flex flex-col space-y-2'>
                          <div className='flex space-x-4 w-full'>
                            <FormField
                              control={form.control}
                              name='title6'
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Name 6 </FormLabel>
                                  <FormControl>
                                    <Select
                                      onValueChange={field.onChange}
                                      defaultValue={field.value}
                                    >
                                      <SelectTrigger>
                                        <SelectValue placeholder='Title' />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value='Mr.'>Mr.</SelectItem>
                                        <SelectItem value='Mrs.'>
                                          Mrs.
                                        </SelectItem>
                                        <SelectItem value='Ms.'>Ms.</SelectItem>
                                        <SelectItem value='Dr.'>Dr.</SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='firstName6'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>First Name 6</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='First Name '
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='middleName6'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>Middle Name 6</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='Middle Name '
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='lastName6'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>Last Name 6</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='Last Name '
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Title 7 */}
                    <div className='flex space-x-4'>
                      <div className='flex-1'>
                        <div className='flex flex-col space-y-2'>
                          <div className='flex space-x-4 w-full'>
                            <FormField
                              control={form.control}
                              name='title7'
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Name 7 </FormLabel>
                                  <FormControl>
                                    <Select
                                      onValueChange={field.onChange}
                                      defaultValue={field.value}
                                    >
                                      <SelectTrigger>
                                        <SelectValue placeholder='Title' />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value='Mr.'>Mr.</SelectItem>
                                        <SelectItem value='Mrs.'>
                                          Mrs.
                                        </SelectItem>
                                        <SelectItem value='Ms.'>Ms.</SelectItem>
                                        <SelectItem value='Dr.'>Dr.</SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='firstName7'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>First Name 7</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='First Name '
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='middleName7'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>Middle Name 7</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='Middle Name '
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='lastName7'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>Last Name 7</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='Last Name '
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Title 8 */}
                    <div className='flex space-x-4'>
                      <div className='flex-1'>
                        <div className='flex flex-col space-y-2'>
                          <div className='flex space-x-4 w-full'>
                            <FormField
                              control={form.control}
                              name='title8'
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Name 8 </FormLabel>
                                  <FormControl>
                                    <Select
                                      onValueChange={field.onChange}
                                      defaultValue={field.value}
                                    >
                                      <SelectTrigger>
                                        <SelectValue placeholder='Title' />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value='Mr.'>Mr.</SelectItem>
                                        <SelectItem value='Mrs.'>
                                          Mrs.
                                        </SelectItem>
                                        <SelectItem value='Ms.'>Ms.</SelectItem>
                                        <SelectItem value='Dr.'>Dr.</SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='firstName8'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>First Name 8</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='First Name '
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='middleName8'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>Middle Name 8</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='Middle Name '
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='lastName8'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>Last Name 8</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='Last Name '
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Title 9 */}
                    <div className='flex space-x-4'>
                      <div className='flex-1'>
                        <div className='flex flex-col space-y-2'>
                          <div className='flex space-x-4 w-full'>
                            <FormField
                              control={form.control}
                              name='title9'
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Name 9 </FormLabel>
                                  <FormControl>
                                    <Select
                                      onValueChange={field.onChange}
                                      defaultValue={field.value}
                                    >
                                      <SelectTrigger>
                                        <SelectValue placeholder='Title' />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value='Mr.'>Mr.</SelectItem>
                                        <SelectItem value='Mrs.'>
                                          Mrs.
                                        </SelectItem>
                                        <SelectItem value='Ms.'>Ms.</SelectItem>
                                        <SelectItem value='Dr.'>Dr.</SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='firstName9'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>First Name 9</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='First Name '
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='middleName9'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>Middle Name 9</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='Middle Name'
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='lastName9'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>Last Name 9</FormLabel>
                                  <FormControl>
                                    <Input {...field} placeholder='Last Name' />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Title 10 */}
                    <div className='flex space-x-4'>
                      <div className='flex-1'>
                        <div className='flex flex-col space-y-2'>
                          <div className='flex space-x-4 w-full'>
                            <FormField
                              control={form.control}
                              name='title10'
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Name 10 </FormLabel>
                                  <FormControl>
                                    <Select
                                      onValueChange={field.onChange}
                                      defaultValue={field.value}
                                    >
                                      <SelectTrigger>
                                        <SelectValue placeholder='Title' />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value='Mr.'>Mr.</SelectItem>
                                        <SelectItem value='Mrs.'>
                                          Mrs.
                                        </SelectItem>
                                        <SelectItem value='Ms.'>Ms.</SelectItem>
                                        <SelectItem value='Dr.'>Dr.</SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='firstName10'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>First Name 10</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='First Name'
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='middleName10'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>Middle Name 10</FormLabel>
                                  <FormControl>
                                    <Input
                                      {...field}
                                      placeholder='Middle Name'
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name='lastName10'
                              render={({ field }) => (
                                <FormItem className='w-full'>
                                  <FormLabel>Last Name 10</FormLabel>
                                  <FormControl>
                                    <Input {...field} placeholder='Last Name' />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                )}

                {/* Passport Number */}
                {(form.watch('letterDocumentsRequired') ===
                  'Dubai Residence Visa Cancellation (Inside UAE)' ||
                  form.watch('letterDocumentsRequired') ===
                    'Dubai Residence Visa Cancellation (Outside UAE) - Less than 6 Months') && (
                  <>
                    <FormField
                      control={form.control}
                      name='passportNum'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Passport Number{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder='Enter Passport Number'
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Please mention the passport number on which the Visa
                            was issued.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}

                {/* Residence Visa Copy */}
                {form.watch('letterDocumentsRequired') === 'Labour Card' && (
                  <>
                    <FormField
                      control={form.control}
                      name='residenceVisaCopy'
                      render={() => (
                        <FormItem>
                          <FormLabel>
                            Residence Visa Copy{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <FileUploadField
                              accept='.pdf,.jpg,.jpeg,.png'
                              onchoose={(file) => {
                                form.setValue('residenceVisaCopy', file || '')
                                form.clearErrors('residenceVisaCopy')
                              }}
                            />
                          </FormControl>{' '}
                          <FormDescription>
                            Please upload a copy of your Dubai Residence Visa.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}

                {/* Contact Information Section*/}
                <CardHeader className='px-0 pt-0 pb-0'>
                  <CardTitle>Contact Information</CardTitle>
                </CardHeader>
                {/* Additional message */}
                <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
                  <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                    <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
                  </span>
                  <div className='flex space-y-2 flex-col ml-3'>
                    <AlertDescription>
                      <p className='text-sm text-slate-800 dark:text-slate-400'>
                        These are your contact details, we need this information
                        to get in touch with you and send you the communication
                        regarding this request.
                      </p>
                    </AlertDescription>
                  </div>
                </Alert>
                {/* Your Name  */}
                <FormField
                  control={form.control}
                  name='yourName'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Your Name <span style={{ color: 'red' }}>*</span>
                      </FormLabel>
                      <FormControl>
                        <Input placeholder='Enter Your Name' {...field} />
                      </FormControl>
                      <FormDescription>
                        {' '}
                        These are the details for the person filling this form.
                        In case, we need to get in touch with you for some
                        further information.{' '}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {/* Phone Number */}
                <FormField
                  control={form.control}
                  name='phoneNumber'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <PhoneInput
                        country={'ae'}
                        value={field.value || ''}
                        onChange={(phone) => field.onChange(phone)}
                        containerClass='fz-country-code flex h-9 w-full rounded-tl-[6px] rounded-bl-[6px] border border-input bg-transparent text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm'
                        inputClass=' flex  !h-9 !w-full rounded-tl-[6px] rounded-bl-[6px] border !border-transparent !bg-transparent px-3 text-base !shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm transform translate-y-[-2px]'
                        buttonClass='!h-100 !border-l-transparent !border-t-transparent !bg-slate-200 dark:!bg-slate-800 dark:!border-r-transparent !border-b-transparent !border-gray-200 !bg-transparent !rounded-tl-[8px] !rounded-bl-[6px]'
                        enableSearch={true}
                        searchPlaceholder='Search country...'
                      />
                      <FormDescription>
                        Please let us know where we can reach you.{' '}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {/* Your Email Address */}
                <FormField
                  control={form.control}
                  name='yourEmailAddress'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Your Email Address{' '}
                        <span style={{ color: 'red' }}>*</span>{' '}
                      </FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {/* Request Updates WhatsApp Number */}
                <FormField
                  control={form.control}
                  name='whatsAppNumber'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Request Updates Whatsapp Number</FormLabel>
                      <PhoneInput
                        country={'ae'}
                        value={field.value || ''}
                        onChange={(phone) => field.onChange(phone)}
                        containerClass='fz-country-code flex h-9 w-full rounded-tl-[6px] rounded-bl-[6px] border border-input bg-transparent text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm'
                        inputClass=' flex  !h-9 !w-full rounded-tl-[6px] rounded-bl-[6px] border !border-transparent !bg-transparent px-3 text-base !shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm transform translate-y-[-2px]'
                        buttonClass='!h-100 !border-l-transparent !border-t-transparent !bg-slate-200 dark:!bg-slate-800 dark:!border-r-transparent !border-b-transparent !border-gray-200 !bg-transparent !rounded-tl-[8px] !rounded-bl-[6px]'
                        enableSearch={true}
                        searchPlaceholder='Search country...'
                      />
                      <FormDescription>
                        Please provide your WhatsApp Mobile Number. Please note
                        all letters will be sent to this WhatsApp.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {/* Payment Information Section*/}
                <CardHeader className='px-0 pt-0 pb-0'>
                  <CardTitle>Payment Information</CardTitle>
                </CardHeader>
                {/* Payment Type */}
                <FormField
                  control={form.control}
                  name='paymentType'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        How would you like to make payment ?{' '}
                        <span style={{ color: 'red' }}>*</span>
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder='Select Payment Type' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='Online Payment'>
                            Online Payment
                          </SelectItem>
                          <SelectItem value='Bank Transfer'>
                            Bank Transfer
                          </SelectItem>
                          <SelectItem value='Cheque'>Cheque</SelectItem>
                          <SelectItem value='Cash'>Cash</SelectItem>
                          <SelectItem value='ATM'>ATM</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormControl>
                        <FormDescription>
                          {' '}
                          Please select the appropriate payment type.
                        </FormDescription>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {/* Payment Proof */}
                <FormField
                  control={form.control}
                  name='paymentProof'
                  render={() => (
                    <FormItem>
                      <FormLabel>Payment Proof</FormLabel>
                      <FormControl>
                        <FileUploadField
                          accept='.pdf,.jpg,.jpeg,.png'
                          onchoose={(file) => {
                            form.setValue('paymentProof', file || '')
                            form.clearErrors('paymentProof')
                          }}
                        />
                      </FormControl>{' '}
                      <FormDescription>
                        You can upload the proof of payment if you have already
                        made the payment towards this request. If you have not
                        made the payment yet, you can ignore this field. You
                        will be able to submit the proof of payment once you
                        receive the Invoice.{' '}
                      </FormDescription>
                    </FormItem>
                  )}
                />
              </form>
              <div className='fixed bottom-0 right-0 p-3 bg-background flex justify-end space-x-2 z-10 fz-form-btns '>
                <Button
                  variant={'btn_outline'}
                  onClick={handleReset}
                  disabled={isLoading}
                >
                  Reset
                </Button>
                <Button
                  variant={'default'}
                  onClick={form.handleSubmit(
                    (data) => {
                      console.log('Form Data:', data)
                      onSubmit(data)
                    },
                    (errors) => {
                      console.log('Form Errors:', errors)
                    }
                  )}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                      Processing...
                    </>
                  ) : (
                    'Submit'
                  )}
                </Button>
              </div>
            </Form>
          </CardContent>
        </Card>
      </Main>
    </>
  )
}
