import React, { useState } from 'react'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { PlusIcon } from 'lucide-react'
import PhoneInput from 'react-phone-input-2'
import { toast } from '@/hooks/use-toast'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import {
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { CountryDropdown } from '@/components/ui/country-dropdown'
import { DateTimePicker } from '@/components/ui/datetime-picker'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from '@/components/ui/dialog'
import FileUploadField from '@/components/ui/fileUpload'
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  FormDescription,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from '@/components/ui/table'

export interface Member {
  id: number
  typeOfMember: string
  passport: File | null

  shareholderName?: string
  shareholderNameArabic?: string
  companyLocation?: string
  shares?: string
  registrationNumber?: string
  addressOfRegisteredOffice?: string
  shareholdersResolution: File | null
  validTradeLicense: File | null
  certificateOfIncorporation: File | null
  memorandumAndArticles: File | null
  roleGeneralManager: boolean
  roleShareholder: boolean
  roleSecretary: boolean
  roleDirector: boolean
  roleRepresentative: boolean
  role1GeneralManager: boolean
  role1Shareholder: boolean
  role1Secretary: boolean
  role1Director: boolean
  title?: string
  firstName?: string
  middleName?: string
  lastName?: string
  firstNameArabic?: string
  middleNameArabic?: string
  lastNameArabic?: string
  numberOfShares?: number

  gender: string
  email: string
  mobilePhone: string
  dateOfBirth: Date
  passportNumber: string
  passportIssueDate: Date
  passportExpiryDate: Date
  passportCountryOfIssue: string
  passportPlaceOfIssue: string
  nationality: string
  previousNationality: string
  visitedUaeBefore: string
  areYouResidentValue: string
  emiratesID?: File | null
  eidNumber?: number
  eidIssueDate?: Date
  eidExpiryDate?: Date
  residentVisa?: File | null
  visaNumber?: number
  visaIssueDate?: Date
  visaExpiryDate?: Date

  addressLine?: string
  cityDistrict?: string
  country?: string
}

const memberFormSchema = z
  .object({
    typeOfMember: z.string().min(1, { message: 'Select a choice.' }),
    passport: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),

    shareholderName: z
      .string()
      .min(2, { message: 'Enter a value for this field.' })
      .optional(),
    shareholderNameArabic: z.string().optional(),
    companyLocation: z
      .string()
      .min(1, { message: 'Select a choice.' })
      .optional(),
    shares: z
      .string()
      .min(2, { message: 'Enter a value for this field.' })
      .optional(),
    registrationNumber: z
      .string()
      .min(1, { message: 'Enter a value for this field.' })
      .optional(),
    addressOfRegisteredOffice: z
      .string()
      .min(2, { message: 'Enter a value for this field.' })
      .optional(),

    shareholdersResolution: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),
    validTradeLicense: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),
    certificateOfIncorporation: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),
    memorandumAndArticles: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),

    roleGeneralManager: z.boolean().optional(),
    roleShareholder: z.boolean().optional(),
    roleSecretary: z.boolean().optional(),
    roleDirector: z.boolean().optional(),
    roleRepresentative: z.boolean().optional(),
    role1GeneralManager: z.boolean().optional(),
    role1Shareholder: z.boolean().optional(),
    role1Secretary: z.boolean().optional(),
    role1Director: z.boolean().optional(),
    title: z.string().min(2, { message: '*' }).optional(),
    firstName: z
      .string()
      .min(2, { message: 'First Name must be at least 2 characters.' })
      .optional(),
    middleName: z
      .string()
      .min(2, { message: 'Moiddle Name must be at least 2 characters.' })
      .optional(),
    lastName: z
      .string()
      .min(2, { message: 'Last Name must be at least 2 characters.' })
      .optional(),
    firstNameArabic: z.string().optional(),
    middleNameArabic: z.string().optional(),
    lastNameArabic: z.string().optional(),
    numberOfShares: z
      .number()
      .min(1, { message: 'Enter a number for this field.' })
      .optional(),

    // roleGeneralManager: z.boolean().refine((val) => val === true, {
    //   message: 'Required.',
    // }),

    // roleShareholder: z.boolean().refine((val) => val === true, {
    //   message: 'Required.',
    // }),

    // roleSecretary: z.boolean().refine((val) => val === true, {
    //   message: 'Required.',
    // }),

    // roleDirector: z.boolean().refine((val) => val === true, {
    //   message: 'Required.',
    // }),

    gender: z.string().min(4, { message: 'Select a choice.' }).optional(),
    email: z.string().email({ message: 'Invalid email address' }).optional(),
    mobilePhone: z
      .string()
      .min(5, { message: 'You must enter at least 5 digits.' })
      .optional(),
    dateOfBirth: z
      .union([z.date(), z.string().transform((str) => new Date(str))])
      .refine(
        (date) => {
          const currentDate = new Date() // Get the current date
          const age = currentDate.getFullYear() - date.getFullYear() // Calculate the age based on the year
          const month = currentDate.getMonth() - date.getMonth() // Calculate the difference in months

          // Check if the age is 18 or older
          return age > 18 || (age === 18 && month >= 0)
        },
        {
          message: 'Members must be at least 18 years old', // Error message if the age is less than 18
        }
      )
      .optional(),

    passportNumber: z
      .string()
      .min(8, { message: 'You must enter at least 8 digits.' })
      .optional(),

    passportIssueDate: z
      .union([z.date(), z.string().transform((str) => new Date(str))])
      .refine(
        (date) => {
          const today = new Date() // Get today's date
          return date <= today // Validate if the date is not in the future
        },
        {
          message: "Passport Issuing date can't be in the future", // Custom error message
        }
      )
      .optional(),

    passportExpiryDate: z
      .union([z.date(), z.string().transform((str) => new Date(str))])
      .refine(
        (date) => {
          const today = new Date()
          const expiryDate = new Date(date)

          // Calculate the difference between the expiry date and today's date
          const diffInTime = expiryDate.getTime() - today.getTime()
          const diffInDays = diffInTime / (1000 * 3600 * 24) // Convert from milliseconds to days

          // Ensure the passport expiry date is at least 120 days in the future
          return diffInDays >= 120
        },
        {
          message: "Passport Expiry Date can't be less than 4 months", // Custom error message
        }
      )
      .optional(),

    passportCountryOfIssue: z
      .string()
      .min(1, { message: 'Select a choice.' })
      .optional(),
    passportPlaceOfIssue: z
      .string()
      .min(2, { message: 'Enter a value for this field.' })
      .optional(),
    nationality: z.string().min(1, { message: 'Select a choice.' }).optional(),
    previousNationality: z
      .string()
      .min(1, {
        message: 'Select a choice.',
      })
      .optional(),
    visitedUaeBefore: z
      .string()
      .min(1, { message: 'Select a choice.' })
      .optional(),
    areYouResidentValue: z
      .string()
      .min(1, { message: 'Select a choice.' })
      .optional(),

    emiratesID: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),

    eidNumber: z
      .number()
      .min(1, { message: 'Enter a value for this field.' })
      .optional(),

    eidIssueDate: z
      .union([z.date(), z.string().transform((str) => new Date(str))])
      .refine((date) => !isNaN(date.getTime()), {
        message: 'Invalid date format',
      })
      .optional(),

    eidExpiryDate: z
      .union([z.date(), z.string().transform((str) => new Date(str))])
      .refine((date) => !isNaN(date.getTime()), {
        message: 'Invalid date format',
      })
      .optional(),

    residentVisa: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),

    visaNumber: z
      .number()
      .min(1, { message: 'Enter a value for this field.' })
      .optional(),

    visaIssueDate: z
      .union([z.date(), z.string().transform((str) => new Date(str))])
      .refine((date) => !isNaN(date.getTime()), {
        message: 'Invalid date format',
      })
      .optional(),

    visaExpiryDate: z
      .union([z.date(), z.string().transform((str) => new Date(str))])
      .refine((date) => !isNaN(date.getTime()), {
        message: 'Invalid date format',
      })
      .optional(),

    addressLine: z
      .string()
      .min(2, { message: 'Enter a value for this field.' })
      .optional(),
    cityDistrict: z
      .string()
      .min(2, { message: 'Enter a value for this field.' })
      .optional(),
    country: z.string().min(1, { message: 'Select a choice.' }).optional(),
  })

  .refine(
    (data) => {
      if (data.typeOfMember) {
        return data.passport instanceof File && data.passport.size > 0
      }
      return true
    },
    {
      message: 'Passport file is required when Type of Member is selected.',
      path: ['passport'],
    }
  )

  .refine(
    (data) => {
      if (data.typeOfMember === 'Corporate') {
        return !!data.shareholderName?.trim()
      }
      return true
    },
    {
      message:
        'Shareholder Name is required when "Type of Member" is Corporate.',
      path: ['shareholderName'],
    }
  )

  .refine(
    (data) => {
      if (data.typeOfMember === 'Corporate') {
        return !!data.companyLocation?.trim()
      }
      return true
    },
    {
      message:
        'Company Location is required when "Type of Member" is Corporate.',
      path: ['companyLocation'],
    }
  )

  .refine(
    (data) => {
      if (data.typeOfMember === 'Corporate') {
        return !!data.shares?.trim()
      }
      return true
    },
    {
      message: 'shares is required when "Type of Member" is Corporate.',
      path: ['shares'],
    }
  )

  .refine(
    (data) => {
      if (data.typeOfMember === 'Corporate') {
        return !!data.registrationNumber?.trim()
      }
      return true
    },
    {
      message:
        'Registration Number is required when "Type of Member" is Corporate.',
      path: ['registrationNumber'],
    }
  )

  .refine(
    (data) => {
      if (data.typeOfMember === 'Corporate') {
        return !!data.addressOfRegisteredOffice?.trim()
      }
      return true
    },
    {
      message:
        'Address of the Registered Office is required when "Type of Member" is Corporate.',
      path: ['addressOfRegisteredOffice'],
    }
  )

  .refine(
    (data) => {
      if (data.typeOfMember === 'Corporate' || data.typeOfMember === 'Branch') {
        return (
          data.shareholdersResolution instanceof File &&
          data.shareholdersResolution.size > 0
        )
      }
      return true
    },
    {
      message:
        'Shareholders Resolution is required when "Type of Member" is Corporate or Branch.',
      path: ['shareholdersResolution'],
    }
  )

  .refine(
    (data) => {
      if (data.typeOfMember === 'Corporate' || data.typeOfMember === 'Branch') {
        return (
          data.validTradeLicense instanceof File &&
          data.validTradeLicense.size > 0
        )
      }
      return true
    },
    {
      message:
        'Valid Trade License is required when "Type of Member" is Corporate or Branch.',
      path: ['validTradeLicense'],
    }
  )

  .refine(
    (data) => {
      if (data.typeOfMember === 'Corporate' || data.typeOfMember === 'Branch') {
        return (
          data.certificateOfIncorporation instanceof File &&
          data.certificateOfIncorporation.size > 0
        )
      }
      return true
    },
    {
      message:
        'Certificate of Incorporation is required when "Type of Member" is Corporate or Branch.',
      path: ['certificateOfIncorporation'],
    }
  )

  .refine(
    (data) => {
      if (data.typeOfMember === 'Corporate' || data.typeOfMember === 'Branch') {
        return (
          data.memorandumAndArticles instanceof File &&
          data.memorandumAndArticles.size > 0
        )
      }
      return true
    },
    {
      message:
        'Memorandum & Articles is required when "Type of Member" is Corporate or Branch.',
      path: ['memorandumAndArticles'],
    }
  )

  //  .refine(
  //     (data) =>
  //       data.roleGeneralManager ||
  //       data.roleShareholder ||
  //       data.roleSecretary ||
  //       data.roleDirector,
  //     {
  //       message: 'Choose your option(s).',
  //       path: ['roles'],
  //     }
  //   )

  // .refine((data) => {
  //   const isIndOrBranch = data.typeOfMember === 'Individual' || data.typeOfMember === 'Branch';
  //   const isCorp = data.typeOfMember === 'Corporate';

  //   if (isIndOrBranch) {
  //     return data.roleGeneralManager && data.roleShareholder && data.roleSecretary && data.roleDirector;
  //   }

  //   if (isCorp) {
  //     return data.roleRepresentative && data.role1GeneralManager && data.role1Shareholder && data.role1Secretary && data.role1Director;
  //   }

  //   return true;
  // }, {
  //   message: 'Please select all required roles.',
  //   path: ['roles'],
  // })

  .refine(
    (data) => {
      if (
        data.typeOfMember === 'Individual' ||
        (data.typeOfMember === 'Corporate' && data.role1Shareholder)
      ) {
        return data.numberOfShares && data.numberOfShares > 0
      }
      return true
    },
    {
      message:
        'Number of Shares is required when is Individual or Corporate and "Role Shareholder" is selected.',
      path: ['numberOfShares'],
    }
  )

  .refine(
    (data) => {
      if (data.areYouResidentValue === 'Yes') {
        return data.emiratesID && data.emiratesID !== ''
      }
      return true // No need to make it mandatory if the condition isn't met
    },
    {
      message: 'Emirates ID is required when "Are you UAE Resident" is Yes.',
      path: ['emiratesID'],
    }
  )

  .refine(
    (data) => {
      if (data.areYouResidentValue === 'Yes') {
        return data.eidNumber && data.eidNumber > 0
      }
      return true
    },
    {
      message: 'EID Number is required when "Are you UAE Resident" is Yes.',
      path: ['eidNumber'],
    }
  )

  .refine(
    (data) => {
      if (data.areYouResidentValue === 'Yes') {
        return !!data.eidIssueDate
      }
      return true
    },
    {
      message: 'Choose a date.',
      path: ['eidIssueDate'],
    }
  )

  .refine(
    (data) => {
      if (data.areYouResidentValue === 'Yes') {
        return !!data.eidExpiryDate
      }
      return true
    },
    {
      message: 'Choose a date.',
      path: ['eidExpiryDate'],
    }
  )

  .refine(
    (data) => {
      if (data.areYouResidentValue === 'Yes') {
        return data.residentVisa && data.residentVisa !== ''
      }
      return true
    },
    {
      message: 'Residence Visa is required when "Are you UAE Resident" is Yes.',
      path: ['residentVisa'],
    }
  )

  .refine(
    (data) => {
      if (data.areYouResidentValue === 'Yes') {
        return data.visaNumber && data.visaNumber > 0
      }
      return true
    },
    {
      message: 'Visa Number is required when "Are you UAE Resident" is Yes.',
      path: ['visaNumber'],
    }
  )

  .refine(
    (data) => {
      if (data.areYouResidentValue === 'Yes') {
        return !!data.visaIssueDate
      }
      return true
    },
    {
      message: 'Choose a date.',
      path: ['visaIssueDate'],
    }
  )

  .refine(
    (data) => {
      if (data.areYouResidentValue === 'Yes') {
        return !!data.visaExpiryDate
      }
      return true
    },
    {
      message: 'Choose a date.',
      path: ['visaExpiryDate'],
    }
  )

  .refine(
    (data) => {
      if (data.typeOfMember) {
        return !!data.addressLine?.trim() && data.addressLine.trim().length >= 2
      }
      return true
    },
    {
      message: 'Enter a value for this field.',
      path: ['addressLine'],
    }
  )

  .refine(
    (data) => {
      if (data.typeOfMember) {
        return (
          !!data.cityDistrict?.trim() && data.cityDistrict.trim().length >= 2
        )
      }
      return true
    },
    {
      message: 'Enter a value for this field.',
      path: ['cityDistrict'],
    }
  )

  .refine(
    (data) => {
      if (data.typeOfMember) {
        return !!data.country?.trim() && data.country.trim().length >= 1
      }
      return true
    },
    {
      message: 'Select a choice.',
      path: ['country'],
    }
  )

type MemberFormData = z.infer<typeof memberFormSchema>

interface CompanyMembersProps {
  members: Member[]
  setMembers: React.Dispatch<React.SetStateAction<Member[]>>
}
const CompanyMembers: React.FC<CompanyMembersProps> = ({ members, setMembers }) => {
  const [dialogOpen, setDialogOpen] = useState(false)
  // const [date, setDate] = useState<Date | undefined>(undefined)

  const form = useForm<MemberFormData>({
    // resolver: zodResolver(memberFormSchema),
    resolver: async (values, context, options) => {
      const result = await zodResolver(memberFormSchema)(
        values,
        context,
        options
      )
      console.log('Validation result :', result)
      return result
    },
    mode: 'onChange',
    defaultValues: {
      typeOfMember: '',
      passport: null,
      shareholderName: '',
      shareholderNameArabic: '',
      companyLocation: '',
      shares: '',
      registrationNumber: '',
      addressOfRegisteredOffice: '',
      shareholdersResolution: null,
      validTradeLicense: null,
      certificateOfIncorporation: null,
      memorandumAndArticles: null,
      roleGeneralManager: false,
      roleShareholder: false,
      roleSecretary: false,
      roleDirector: false,
      roleRepresentative: false,
      role1GeneralManager: false,
      role1Shareholder: false,
      role1Secretary: false,
      role1Director: false,

      title: '',
      firstName: '',
      middleName: '',
      lastName: '',
      firstNameArabic: '',
      middleNameArabic: '',
      lastNameArabic: '',
      numberOfShares: 0,

      gender: '',
      email: '',
      mobilePhone: '',
      dateOfBirth: undefined,
      passportNumber: '',
      passportIssueDate: undefined,
      passportExpiryDate: undefined,
      passportCountryOfIssue: '',
      passportPlaceOfIssue: '',
      nationality: '',
      previousNationality: '',
      visitedUaeBefore: '',
      areYouResidentValue: '',
      emiratesID: null,
      eidNumber: 1,
      eidIssueDate: new Date(),
      eidExpiryDate: new Date(),
      residentVisa: null,
      visaNumber: 1,
      visaIssueDate: new Date(),
      visaExpiryDate: new Date(),

      addressLine: '',
      cityDistrict: '',
      country: '',
    },
    shouldUnregister: true,
    shouldFocusError: true,
  })

  const handleAddRow = () => {
    setDialogOpen(true)
  }

  const handleCloseDialog = () => {
    setDialogOpen(false)
  }

  const handleMemberData = (data: MemberFormData) => {
    const newMember: Member = {
      id: members.length > 0 ? Math.max(...members.map((m) => m.id)) + 1 : 1,

      typeOfMember: data.typeOfMember,
      passport: data.passport ? data.passport : null,

      shareholderName: data.shareholderName,
      shareholderNameArabic: data.shareholderNameArabic,
      companyLocation: data.companyLocation,
      shares: data.shares,
      registrationNumber: data.registrationNumber,
      addressOfRegisteredOffice: data.addressOfRegisteredOffice,

      shareholdersResolution: data.shareholdersResolution,
      validTradeLicense: data.validTradeLicense,
      certificateOfIncorporation: data.certificateOfIncorporation,
      memorandumAndArticles: data.memorandumAndArticles,
      roleGeneralManager: data.roleGeneralManager ?? false,
      roleShareholder: data.roleShareholder ?? false,
      roleSecretary: data.roleSecretary ?? false,
      roleDirector: data.roleDirector ?? false,

      roleRepresentative: data.roleRepresentative ?? false,
      role1GeneralManager: data.role1GeneralManager ?? false,
      role1Shareholder: data.role1Shareholder ?? false,
      role1Secretary: data.role1Secretary ?? false,
      role1Director: data.role1Director ?? false,
      title: data.title,
      firstName: data.firstName,
      middleName: data.middleName,
      lastName: data.lastName,
      firstNameArabic: data.firstNameArabic,
      middleNameArabic: data.middleNameArabic,
      lastNameArabic: data.lastNameArabic,
      numberOfShares: data.numberOfShares,

      gender: data.gender ?? '',
      email: data.email ?? '',
      mobilePhone: data.mobilePhone ?? '',
      dateOfBirth: data.dateOfBirth ?? new Date(),
      passportNumber: data.passportNumber ?? '',
      passportIssueDate: data.passportIssueDate ?? new Date(),
      passportExpiryDate: data.passportExpiryDate ?? new Date(),
      passportCountryOfIssue: data.passportCountryOfIssue ?? '',
      passportPlaceOfIssue: data.passportPlaceOfIssue ?? '',
      previousNationality: data.previousNationality ?? '',
      nationality: data.nationality ?? '',
      visitedUaeBefore: data.visitedUaeBefore ?? '',
      areYouResidentValue: data.areYouResidentValue ?? '',
      emiratesID: data.emiratesID,
      eidNumber: data.eidNumber,
      eidIssueDate: data.eidIssueDate,
      eidExpiryDate: data.eidExpiryDate,
      residentVisa: data.residentVisa,
      visaNumber: data.visaNumber,
      visaIssueDate: data.visaIssueDate,
      visaExpiryDate: data.visaExpiryDate,
      // fullAddress: data.fullAddress,

      addressLine: data.addressLine,
      cityDistrict: data.cityDistrict,
      country: data.country,
    }

    setMembers((prev) => [...prev, newMember])
    handleCloseDialog()
  }

  const [roleGeneralManager, setRoleGeneralManager] = React.useState(false)
  const [roleShareholder, setRoleShareholder] = React.useState(false)
  const [roleSecretary, setRolesecretary] = React.useState(false)
  const [roleDirector, setRoleDirector] = React.useState(false)
  const [roleRepresentative, setRoleRepresentative] = React.useState(false)
  const [role1GeneralManager, setRole1GeneralManager] = React.useState(false)
  const [role1Shareholder, setRole1Shareholder] = React.useState(false)
  const [role1Secretary, setRole1Secretary] = React.useState(false)
  const [role1Director, setRole1Director] = React.useState(false)

  // const handleFormSubmit = (data: MemberFormData) => {
  //   form.reset()
  //   handleMemberData(data)
  // }
  // const atLeastOneSelected =
  //   form.watch('roleGeneralManager') ||
  //   form.watch('roleShareholder') ||
  //   form.watch('roleSecretary') ||
  //   form.watch('roleDirector')

  // const [roleGeneralManager, setRoleGeneralManager] = React.useState(false)
  // const [roleShareholder, setRoleShareholder] = React.useState(false)
  // const [roleSecretary, setRolesecretary] = React.useState(false)
  // const [roleDirector, setRoleDirector] = React.useState(false)

  const handleFormSubmit = async (data: MemberFormData) => {
    // Trigger validation for the fields
    const isValid = await form.trigger()
    // console.log('Form Data For Add Member :', data)
    // console.log('Validation Errors :', form.formState.errors)

    if (isValid) {
      // If validation passes, process the data and add the member
      handleMemberData(data)

      // Reset the form or handle next steps
      form.reset()

      // Close the dialog
      handleCloseDialog()

      // Show success message
      toast({
        title: 'Member Added Successfully',
        description: 'The new member has been added.',
        variant: 'success',
      })
    } else {
      // If validation fails, show an error message
      toast({
        title: 'Error',
        description: 'Please fill in all the required fields.',
        variant: 'destructive',
      })
    }
  }

  return (
    <>
      <Table className='w-full table-auto scrollbar-thin'>
        <TableHeader>
          <TableRow>
            <TableHead>Type of Member*</TableHead>

            <TableHead>Passport*</TableHead>

            <TableHead>Shareholder Name*</TableHead>

            <TableHead>Shares*</TableHead>

            <TableHead>Role*</TableHead>
            <TableHead>Name*</TableHead>
            <TableHead>Number of Shares*</TableHead>
            <TableHead>Gender*</TableHead>
            <TableHead>Email*</TableHead>
            <TableHead>Mobile Phone*</TableHead>
            <TableHead>Date of Birth*</TableHead>
            <TableHead>Passport Number*</TableHead>
            <TableHead>Passport Issue Date*</TableHead>
            <TableHead>Passport Expiry Date*</TableHead>
            <TableHead>Passport Country of Issue*</TableHead>
            <TableHead>Passport Place of Issue*</TableHead>
            <TableHead>Nationality*</TableHead>
            <TableHead>Previous Nationality*</TableHead>
            <TableHead>Visited UAE Before*</TableHead>
            <TableHead>Are you UAE Resident*</TableHead>
            <TableHead>Full Address*</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {members.map((member) => (
            <TableRow key={member.id}>
              <TableCell>{member.typeOfMember}</TableCell>
              <TableCell>
                {/* If a file is uploaded, show a download link, else display a placeholder */}
                {member.passport ? (
                  <a
                    href={URL.createObjectURL(member.passport)}
                    target='_blank'
                    rel='noopener noreferrer'
                  >
                    Download Passport
                  </a>
                ) : (
                  'No file uploaded'
                )}
              </TableCell>

              <TableCell>{member.shareholderName}</TableCell>

              <TableCell>{member.shares}</TableCell>

              <TableCell>
                {' '}
                {[
                  member.roleGeneralManager && 'General Manager',
                  member.roleShareholder && 'Shareholder',
                  member.roleSecretary && 'Secretary',
                  member.roleDirector && 'Director',
                ]
                  .filter(Boolean)
                  .join(', ')}
              </TableCell>
              <TableCell>
                {member.title} {member.firstName}
                {member.middleName} {member.lastName}
              </TableCell>

              <TableCell>{member.numberOfShares}</TableCell>
              <TableCell>{member.gender}</TableCell>
              <TableCell>{member.email}</TableCell>
              <TableCell>{member.mobilePhone}</TableCell>
              <TableCell>{member.dateOfBirth.toLocaleDateString()}</TableCell>
              <TableCell>{member.passportNumber}</TableCell>
              <TableCell>
                {member.passportIssueDate.toLocaleDateString()}
              </TableCell>
              <TableCell>
                {member.passportExpiryDate.toLocaleDateString()}
              </TableCell>
              <TableCell>{member.passportCountryOfIssue}</TableCell>
              <TableCell>{member.passportPlaceOfIssue}</TableCell>
              <TableCell>{member.nationality}</TableCell>
              <TableCell>{member.previousNationality}</TableCell>
              <TableCell>{member.visitedUaeBefore}</TableCell>

              <TableCell>{member.areYouResidentValue}</TableCell>
              <TableCell>
                {[member.addressLine, member.cityDistrict, member.country]
                  .filter(Boolean)
                  .join(', ')}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <Button
        type='button'
        variant='link'
        className='mt-4'
        onClick={handleAddRow}
      >
        <PlusIcon className='mr-2' /> Add New Member
      </Button>
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className='h-[90vh] pb-14 pr-2' variant='default'>
          <DialogHeader className='static'>
            <DialogTitle>Add Company Members Subform</DialogTitle>
            <DialogDescription>
              Please fill out the following details to register a new member:
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleFormSubmit)}
              className='scrollbar-thin overflow-y-auto pr-1 pl-1 space-y-4 fz-form'
            >
              <CardHeader className='px-0 pt-4 pb-0'>
                <CardTitle>Please Select </CardTitle>
              </CardHeader>
              {/* Type of Member Dropdown */}
              <FormField
                control={form.control}
                name='typeOfMember'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Type of Member</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder='Select type' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='Individual'>Individual</SelectItem>
                        <SelectItem value='Corporate'>Corporate</SelectItem>
                        <SelectItem value='Branch'>Branch</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Passport field  */}
              {form.watch('typeOfMember') && (
                <>
                  <FormField
                    control={form.control}
                    name='passport'
                    render={() => (
                      <FormItem>
                        <FormLabel>
                          Passport <span className='text-red-500'>*</span>
                        </FormLabel>
                        <FormControl>
                          <FileUploadField
                            accept='.pdf'
                            onchoose={(file) => {
                              form.setValue('passport', file || null)
                              form.clearErrors('passport')
                            }}
                          />
                        </FormControl>
                        <FormDescription>
                          Please upload the documents for the member: clear
                          passport copy (Front and Back as one PDF). Please make
                          sure it is a clear scan and all 4 corners can be seen
                          in the passport copy. You may refer to our passport
                          guidelines at the link below.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </>
              )}
              {form.watch('typeOfMember') === 'Individual' && (
                <>
                  <CardHeader className='px-0 pt-4 pb-0'>
                    <CardTitle>Requirements :</CardTitle>
                  </CardHeader>
                  {/* Template - Shareholder Resolution Link */}
                  <div className='mt-4 '>
                    <a
                      href='https://files.ifza.com/external/ee97cb018f2ad9fe51f7552a39f4976937764f090bd56939a1a9bac8c290d2c7'
                      target='_blank'
                      className='text-blue-500 underline'
                    >
                      Template - Shareholder Resolution
                    </a>
                  </div>

                  {/*Template - Shareholder Resolution Link */}
                  <div className='mt-4 '>
                    <a
                      href='https://files.ifza.com/external/fa64db73c5ba710a51384b24894d6b436cbc40e1a590795f0b1a26a70c8b9736'
                      target='_blank'
                      className='text-blue-500 underline'
                    >
                      Template - Shareholder Resolution
                    </a>
                  </div>
                </>
              )}

              {/* Show additional fields if Type of Member is "Corporate */}
              {form.watch('typeOfMember') === 'Corporate' && (
                <>
                  <CardHeader className='px-0 pt-4 pb-0'>
                    <CardTitle>Corporate Shareholder Information :</CardTitle>
                  </CardHeader>

                  <div className='flex flex-wrap space-between gap-y-4'>
                    {/*Shareholder Name  */}
                    <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                      <FormField
                        control={form.control}
                        name='shareholderName'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Shareholder Name</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    {/*Shareholder Name In Arabic */}
                    <div className='flex-1'>
                      <FormField
                        control={form.control}
                        name='shareholderNameArabic'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Shareholder Name In Arabic</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {/* Company Location (Country) Dropdown */}

                  <div className='flex flex-wrap space-between gap-y-4'>
                    <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                      <FormField
                        control={form.control}
                        name='companyLocation'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Company Location (Country)</FormLabel>
                            <FormControl>
                              <CountryDropdown
                                placeholder='Country'
                                defaultValue={field.value as string} 
                                onChange={(c) => field.onChange(c.alpha3)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <div className='flex-1'>
                      {/* Shares Input Field */}

                      <FormField
                        control={form.control}
                        name='shares'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Shares</FormLabel>
                            <FormControl>
                              <Input {...field} type='number' />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {/* Registration Number Input Field */}
                  <div className='flex flex-wrap space-between gap-y-4'>
                    <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                      <FormField
                        control={form.control}
                        name='registrationNumber'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Registration Number</FormLabel>
                            <FormControl>
                              <Input {...field} type='number' />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    {/* Address of the Registered Office Input Field */}
                    <div className='flex-1'>
                      <FormField
                        control={form.control}
                        name='addressOfRegisteredOffice'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Address of the Registered Office
                            </FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </>
              )}

              {/* Show "Requirements :" if Type of Member is Corporate or Branch */}
              {(form.watch('typeOfMember') === 'Corporate' ||
                form.watch('typeOfMember') === 'Branch') && (
                <CardHeader className='px-0 pt-4 pb-0'>
                  <CardTitle>Requirements :</CardTitle>
                </CardHeader>
              )}

              {/* Show specific links for Corporate Member */}
              {form.watch('typeOfMember') === 'Corporate' && (
                <div className='mt-4'>
                  <div className='space-y-4 mt-2'>
                    {/* Foreign - Corporate Shareholder link */}
                    <div>
                      <a
                        href='https://files.ifza.com/external/dc6754cf54b43ca3440b241ee01992d1a8e9ee7462e991f068ee0e8cf360a99a'
                        target='_blank'
                        className='text-blue-500 underline'
                      >
                        Foreign - Corporate Shareholder
                      </a>
                    </div>

                    {/* Within UAE - Corporate Shareholder link */}
                    <div>
                      <a
                        href='https://files.ifza.com/external/810be89a363acbb5cb92f1df3fc34bd32485021932d05eab4e0b8c4f345edcf4'
                        target='_blank'
                        className='text-blue-500 underline'
                      >
                        Within UAE - Corporate Shareholder
                      </a>
                    </div>
                  </div>
                </div>
              )}

              {/* Show fields if Type of Member is Corporate or Branch */}
              {(form.watch('typeOfMember') === 'Corporate' ||
                form.watch('typeOfMember') === 'Branch') && (
                <>
                  {/* Shareholders Resolution (Notarized & Attested) */}

                  <div className='flex flex-wrap space-between gap-y-4'>
                    <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                      <FormField
                        control={form.control}
                        name='shareholdersResolution'
                        render={() => (
                          <FormItem>
                            <FormLabel>
                              Shareholders Resolution <br />( Notarized &
                              Attested ){' '}
                            </FormLabel>
                            <FormControl>
                              <FileUploadField
                                accept='.pdf'
                                onchoose={(file) => {
                                  form.setValue(
                                    'shareholdersResolution',
                                    file || null
                                  )
                                  form.clearErrors('shareholdersResolution')
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Template - Shareholder Resolution Link */}
                      <div className='mt-4 '>
                        <a
                          href='https://files.ifza.com/external/fa64db73c5ba710a51384b24894d6b436cbc40e1a590795f0b1a26a70c8b9736'
                          target='_blank'
                          className='text-blue-500 underline'
                        >
                          Template - Shareholder Resolution
                        </a>
                      </div>
                    </div>

                    {/* Valid Trade License of the Corporate Shareholder or Equivalent (Notarized/ Certified True Copies) */}
                    <div className='flex-1'>
                      <FormField
                        control={form.control}
                        name='validTradeLicense'
                        render={() => (
                          <FormItem>
                            <FormLabel>
                              Valid Trade License of the Corporate Shareholder
                              or Equivalent ( Notarized / Certified True Copies
                              ){' '}
                            </FormLabel>
                            <FormControl>
                              <FileUploadField
                                accept='.pdf'
                                onchoose={(file) => {
                                  form.setValue(
                                    'validTradeLicense',
                                    file || null
                                  )
                                  form.clearErrors('validTradeLicense')
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {/* Certificate of Incorporation/Formation of the Corporate Shareholder or Equivalent (Notarized/ Certified True Copies) */}

                  <div className='flex flex-wrap space-between gap-y-4'>
                    <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                      <FormField
                        control={form.control}
                        name='certificateOfIncorporation'
                        render={() => (
                          <FormItem>
                            <FormLabel>
                              Certificate of Incorporation / Formation of the
                              Corporate Shareholder or Equivalent <br /> (
                              Notarized / Certified True Copies ){' '}
                            </FormLabel>
                            <FormControl>
                              <FileUploadField
                                accept='.pdf'
                                onchoose={(file) => {
                                  form.setValue(
                                    'certificateOfIncorporation',
                                    file || null
                                  )
                                  form.clearErrors('certificateOfIncorporation')
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    {/* Memorandum & Articles of Association of the Corporate Shareholder or Equivalent Constitutional Documents (Notarized/ Certified True Copies) */}
                    <div className='flex-1'>
                      <FormField
                        control={form.control}
                        name='memorandumAndArticles'
                        render={() => (
                          <FormItem>
                            <FormLabel>
                              Memorandum & Articles of Association of the
                              Corporate Shareholder or Equivalent Constitutional
                              Documents
                              <br /> ( Notarized / Certified True Copies ){' '}
                            </FormLabel>
                            <FormControl>
                              <FileUploadField
                                accept='.pdf'
                                onchoose={(file) => {
                                  form.setValue(
                                    'memorandumAndArticles',
                                    file || null
                                  )
                                  form.clearErrors('memorandumAndArticles')
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </>
              )}

              {/* Role selection for Individual/Branch */}
              {(form.watch('typeOfMember') === 'Individual' ||
                form.watch('typeOfMember') === 'Branch') && (
                <>
                  <div className='mt-4'>
                    <FormLabel>Role :</FormLabel>

                    <div className='flex space-x-4 mt-2'>
                      {/* General Manager Checkbox - For Branch, Individual */}

                      <div className='flex items-center space-x-2'>
                        <Checkbox
                          id='role-generalManager'
                          checked={
                            roleGeneralManager ||
                            form.watch('typeOfMember') === 'Branch'
                          } // Default checked for Branch
                          onCheckedChange={(checked: boolean) =>
                            setRoleGeneralManager(checked)
                          }
                        />
                        <label
                          className='text-sm font-normal ml-2'
                          htmlFor='role-generalManager'
                        >
                          General Manager
                        </label>
                      </div>

                      {/* Shareholder Checkbox - For Individual , Disabled for Branch */}

                      <div className='flex items-center space-x-2'>
                        <Checkbox
                          id='role-shareholder'
                          checked={roleShareholder}
                          onCheckedChange={(checked: boolean) =>
                            setRoleShareholder(checked)
                          }
                          disabled={form.watch('typeOfMember') === 'Branch'} // Disabled for Branch
                        />
                        <label
                          className='text-sm font-normal ml-2'
                          htmlFor='role-shareholder'
                        >
                          Shareholder
                        </label>
                      </div>

                      {/* Secretary Checkbox - For Individual , Disabled for Branch */}

                      <div className='flex items-center space-x-2'>
                        <Checkbox
                          id='role-secretary'
                          checked={roleSecretary}
                          onCheckedChange={(checked: boolean) =>
                            setRolesecretary(checked)
                          }
                          disabled={form.watch('typeOfMember') === 'Branch'} // Disabled for Branch
                        />
                        <label
                          className='text-sm font-normal ml-2'
                          htmlFor='role-secretary'
                        >
                          Secretary
                        </label>
                      </div>

                      {/* Director Checkbox - For Individual , Disabled for Branch */}

                      <div className='flex items-center space-x-2'>
                        <Checkbox
                          id='role-director'
                          checked={roleDirector}
                          onCheckedChange={(checked: boolean) =>
                            setRoleDirector(checked)
                          }
                          disabled={form.watch('typeOfMember') === 'Branch'} // Disabled for Branch
                        />
                        <label
                          className='text-sm font-normal ml-2'
                          htmlFor='role-director'
                        >
                          Director
                        </label>
                      </div>
                    </div>
                  </div>
                </>
              )}

              {/* Show "Representative Information " if Type of Member is Corporate */}
              {form.watch('typeOfMember') === 'Corporate' && (
                <>
                  <CardHeader className='px-0 pt-4 pb-0'>
                    <CardTitle>Representative Information :</CardTitle>
                  </CardHeader>

                  <div className='mt-4'>
                    <FormLabel>Role :</FormLabel>

                    <div className='flex space-x-4 mt-2'>
                      {/* Representative Checkbox - Only for Corporate */}

                      <div className='flex items-center space-x-2'>
                        <Checkbox
                          id='role1representative'
                          checked={
                            roleRepresentative ||
                            form.watch('typeOfMember') === 'Corporate'
                          } // Default checked for Corporate
                          onCheckedChange={(checked: boolean) =>
                            setRoleRepresentative(checked)
                          }
                        />
                        <label
                          className='text-sm font-normal ml-2'
                          htmlFor='role-representative'
                        >
                          Representative
                        </label>
                      </div>

                      <div className='flex items-center space-x-2'>
                        <Checkbox
                          id='role1generalManager'
                          checked={role1GeneralManager}
                          onCheckedChange={(checked: boolean) =>
                            setRole1GeneralManager(checked)
                          }
                        />
                        <label
                          className='text-sm font-normal ml-2'
                          htmlFor='role-generalManager'
                        >
                          General Manager
                        </label>
                      </div>

                      <div className='flex items-center space-x-2'>
                        <Checkbox
                          id='role1shareholder'
                          checked={role1Shareholder}
                          onCheckedChange={(checked: boolean) =>
                            setRole1Shareholder(checked)
                          }
                        />
                        <label
                          className='text-sm font-normal ml-2'
                          htmlFor='role-shareholder'
                        >
                          Shareholder
                        </label>
                      </div>

                      <div className='flex items-center space-x-2'>
                        <Checkbox
                          id='role1secretary'
                          checked={role1Secretary}
                          onCheckedChange={(checked: boolean) =>
                            setRole1Secretary(checked)
                          }
                        />
                        <label
                          className='text-sm font-normal ml-2'
                          htmlFor='role-secretary'
                        >
                          Secretary
                        </label>
                      </div>

                      <div className='flex items-center space-x-2'>
                        <Checkbox
                          id='role1director'
                          checked={role1Director}
                          onCheckedChange={(checked: boolean) =>
                            setRole1Director(checked)
                          }
                        />
                        <label
                          className='text-sm font-normal ml-2'
                          htmlFor='role-director'
                        >
                          Director
                        </label>
                      </div>
                    </div>
                  </div>
                </>
              )}

              {form.watch('typeOfMember') && (
                <>
                  {/* Title Dropdown */}
                  <div className='flex items-start space-x-2'>
                    <FormField
                      control={form.control}
                      name='title'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Name <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <SelectTrigger className='w-[100px]'>
                                <SelectValue placeholder='Select Title' />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value='Mr.'>Mr.</SelectItem>
                                <SelectItem value='Mrs.'>Mrs.</SelectItem>
                                <SelectItem value='Ms.'>Ms.</SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>

                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* First Name Input Field */}
                    <div className='w-full'>
                      <FormField
                        control={form.control}
                        name='firstName'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>First Name</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>

                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Middle Name Input Field */}
                    <div className='w-full'>
                      <FormField
                        control={form.control}
                        name='middleName'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Middle Name</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>

                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    {/* Last Name Input Field */}
                    <div className='w-full'>
                      <FormField
                        control={form.control}
                        name='lastName'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Last Name</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>

                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  <Alert className='border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/40 mt-4'>
                    <AlertDescription>
                      The application will be processed with our default Arabic
                      translation. Please enter the Arabic translation as per
                      your passport if the below translation is not correct.
                    </AlertDescription>
                  </Alert>

                  {/* Name In Arabic Section */}
                  <div className='mt-4'>
                    <div className='flex items-end space-x-2'>
                      {/* <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                            <FormItem>
                              <FormLabel>Name In Arabic</FormLabel>
                            </FormItem>
                          </div> */}

                      {/* First Name in Arabic Input Field */}
                      <div className='w-full'>
                        <FormField
                          control={form.control}
                          name='firstNameArabic'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>First Name in Arabic</FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  placeholder='Auto-populated by default Arabic translation'
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Middle Name in Arabic Input Field */}
                      <div className='w-full'>
                        <FormField
                          control={form.control}
                          name='middleNameArabic'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Middle Name in Arabic</FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  placeholder='Auto-populated by default Arabic translation'
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      {/* Last Name in Arabic Input Field */}
                      <div className='w-full'>
                        <FormField
                          control={form.control}
                          name='lastNameArabic'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Last Name in Arabic</FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  placeholder='Auto-populated by default Arabic translation'
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  </div>
                </>
              )}

              {/* Number of Shares Field */}
              {(form.watch('typeOfMember') === 'Individual' ||
                (form.watch('typeOfMember') === 'Corporate' &&
                  role1Shareholder)) && (
                <div className='mt-4'>
                  <FormField
                    control={form.control}
                    name='numberOfShares'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Number of Shares{' '}
                          <span className='text-red-500'>*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder='Enter Number of Shares'
                            onChange={(e) => {
                              const value =
                                e.target.value === ''
                                  ? undefined
                                  : +e.target.value
                              field.onChange(value)
                            }}
                          />
                        </FormControl>
                        <FormDescription>
                          Please mention the number of shares you want to assign
                          to this particular member.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}

              {form.watch('typeOfMember') && (
                <>
                  <div className='flex flex-wrap space-between gap-y-4'>
                    {/* Gender Dropdown */}
                    <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                      <FormField
                        control={form.control}
                        name='gender'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Gender <span className='text-red-500'>*</span>
                            </FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder='Select gender' />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value='Male'>Male</SelectItem>
                                <SelectItem value='Female'>Female</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    {/* Email Field */}
                    <div className='flex-1'>
                      <FormField
                        control={form.control}
                        name='email'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Email <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  <div className='flex flex-wrap space-between gap-y-4'>
                    {/* Mobile Phone Field */}
                    <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                      <FormField
                        control={form.control}
                        name='mobilePhone'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Mobile Phone{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <PhoneInput
                              country={'ae'}
                              // value={field.value.toString()}
                              onChange={(phone) => field.onChange(phone)}
                              containerClass='fz-country-code flex h-9 w-full rounded-tl-[6px] rounded-bl-[6px] border border-input bg-transparent text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm'
                              inputClass=' flex  !h-9 !w-full rounded-tl-[6px] rounded-bl-[6px] border !border-transparent !bg-transparent px-3 text-base !shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm transform translate-y-[-2px]'
                              buttonClass='!h-100 !border-l-transparent !border-t-transparent !bg-slate-200 dark:!bg-slate-800 dark:!border-r-transparent !border-b-transparent !border-gray-200 !bg-transparent !rounded-tl-[8px] !rounded-bl-[6px]'
                              enableSearch={true}
                              searchPlaceholder='Search country...'
                            />
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    {/* Date of Birth Field */}
                    <div className='flex-1'>
                      <FormField
                        control={form.control}
                        name='dateOfBirth'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Date of Birth{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <DateTimePicker
                              granularity='day'
                              value={field.value}
                              onChange={field.onChange}
                              displayFormat={{
                                hour24: 'dd MMMM yyyy',
                              }}
                            />
                            <FormDescription>
                              dd-MMM-yyyy
                              <br />
                              Shareholder must be 18 years or above at the time
                              of application.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {/* Passport Number Field */}

                  <div className='flex flex-wrap space-between gap-y-4'>
                    <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='passportNumber'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Passport Number{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  placeholder='Enter Passport Number'
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                    {/* Passport Issue Date Field */}
                    <div className='flex-1'>
                      <FormField
                        control={form.control}
                        name='passportIssueDate'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Passport Issue Date{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <DateTimePicker
                                granularity='day'
                                value={field.value}
                                onChange={field.onChange}
                                displayFormat={{
                                  hour24: 'dd MMMM yyyy',
                                }}
                              />
                            </FormControl>
                            <FormDescription>dd-MMM-yyyy</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  <div className='flex flex-wrap space-between gap-y-4'>
                    {/*  Passport Expiry Date Field */}

                    <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                      <FormField
                        control={form.control}
                        name='passportExpiryDate'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Passport Expiry Date{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <DateTimePicker
                                granularity='day'
                                value={field.value}
                                onChange={field.onChange}
                                displayFormat={{
                                  hour24: 'dd MMMM yyyy',
                                }}
                              />
                            </FormControl>
                            <FormDescription>
                              dd-MMM-yyyy
                              <br />
                              Passport should be valid for at least 120 days at
                              the time of application.
                            </FormDescription>

                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    {/* Passport Country of Issue Dropdown */}
                    <div className='flex-1'>
                      <FormField
                        control={form.control}
                        name='passportCountryOfIssue'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Passport Country of Issue{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <CountryDropdown
                                placeholder='Country'
                                defaultValue={field.value as string} 
                                onChange={(c) => field.onChange(c.alpha3)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {/* Passport Place of Issue Field */}

                  <div className='flex flex-wrap space-between gap-y-4'>
                    <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                      <FormField
                        control={form.control}
                        name='passportPlaceOfIssue'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Passport Place of Issue{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder='Enter Passport Place of Issue'
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Nationality Dropdown */}
                    <div className='flex-1'>
                      <FormField
                        control={form.control}
                        name='nationality'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Nationality{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <CountryDropdown
                                placeholder='Country'
                                defaultValue={field.value as string} 
                                onChange={(c) => field.onChange(c.alpha3)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  <div className='flex flex-wrap space-between gap-y-4'>
                    {/* Previous Nationality Dropdown */}
                    <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                      <FormField
                        control={form.control}
                        name='previousNationality'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Previous Nationality</FormLabel>
                            <FormControl>
                              <CountryDropdown
                                placeholder='Country'
                                defaultValue={field.value as string} 
                                onChange={(c) => field.onChange(c.alpha3)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    {/* Visited UAE Before Dropdown */}
                    <div className='flex-1'>
                      <FormField
                        control={form.control}
                        name='visitedUaeBefore'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Visited UAE Before ?{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder='Select option' />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value='Yes'>Yes</SelectItem>
                                <SelectItem value='No'>No</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {/* Are you UAE resident ?*/}
                  <FormField
                    control={form.control}
                    name='areYouResidentValue'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Are you UAE Resident{' '}
                          <span className='text-red-500'>*</span>
                        </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder='Select option' />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='Yes'>Yes</SelectItem>
                            <SelectItem value='No'>No</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  {/* Show Emirates ID and other fields when UAE resident is "Yes" */}
                  {form.watch('areYouResidentValue') === 'Yes' && (
                    <>
                      <div className='flex flex-wrap space-between gap-y-4'>
                        {/* Emirates ID (File Field) */}
                        <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                          <FormField
                            control={form.control}
                            name='emiratesID'
                            render={() => (
                              <FormItem>
                                <FormLabel>
                                  Emirates ID{' '}
                                  <span className='text-red-500'>*</span>
                                </FormLabel>
                                <FormControl>
                                  <FileUploadField
                                    accept='.pdf'
                                    onchoose={(file) => {
                                      form.setValue('emiratesID', file || '')
                                      form.clearErrors('emiratesID')
                                    }}
                                  />
                                </FormControl>
                                <FormDescription>
                                  Please upload the copy of the Emirates ID
                                  (Front and Back as one PDF).
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        {/* EID Issue Date (Date Field) */}
                        <div className='flex-1'>
                          <FormField
                            control={form.control}
                            name='eidIssueDate'
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>
                                  EID Issue Date{' '}
                                  <span className='text-red-500'>*</span>
                                </FormLabel>
                                <DateTimePicker
                                  granularity='day'
                                  value={field.value}
                                  onChange={field.onChange}
                                  displayFormat={{
                                    hour24: 'dd MMMM yyyy',
                                  }}
                                />
                                <FormDescription>dd-MMM-yyyy</FormDescription>

                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                      <div className='flex flex-wrap space-between gap-y-4'>
                        {/* EID Expiry Date (Date Field) */}
                        <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                          <FormField
                            control={form.control}
                            name='eidExpiryDate'
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>
                                  EID Expiry Date{' '}
                                  <span className='text-red-500'>*</span>
                                </FormLabel>
                                <DateTimePicker
                                  granularity='day'
                                  value={field.value}
                                  onChange={field.onChange}
                                  displayFormat={{
                                    hour24: 'dd MMMM yyyy',
                                  }}
                                />
                                <FormDescription>dd-MMM-yyyy</FormDescription>

                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        {/* EID Number (Input Field) */}

                        <div className='flex-1'>
                          <FormField
                            control={form.control}
                            name='eidNumber'
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>EID Number</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    placeholder='Enter EID Number'
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                      <div className='flex flex-wrap space-between gap-y-4'>
                        {/* Resident Visa */}{' '}
                        <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                          <FormField
                            control={form.control}
                            name='residentVisa'
                            render={() => (
                              <FormItem>
                                <FormLabel>
                                  Residence Visa{' '}
                                  <span className='text-red-500'>*</span>
                                </FormLabel>
                                <FormControl>
                                  <FileUploadField
                                    accept='.pdf'
                                    onchoose={(file) => {
                                      form.setValue('residentVisa', file || '')
                                      form.clearErrors('residentVisa')
                                    }}
                                  />
                                </FormControl>
                                <FormDescription>
                                  Please upload the copy of the resident Visa
                                  for UAE.
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        {/* Visa Number (Input Field) */}
                        <div className='flex-1'>
                          <FormField
                            control={form.control}
                            name='visaNumber'
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Visa Number</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    placeholder='Enter Visa Number'
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                      <div className='flex flex-wrap space-between gap-y-4'>
                        {/* Visa Issue Date (Date Field) */}
                        <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                          <FormField
                            control={form.control}
                            name='visaIssueDate'
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>
                                  Visa Issue Date{' '}
                                  <span className='text-red-500'>*</span>
                                </FormLabel>
                                <DateTimePicker
                                  granularity='day'
                                  value={field.value}
                                  onChange={field.onChange}
                                  displayFormat={{
                                    hour24: 'dd MMMM yyyy',
                                  }}
                                />

                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        {/* Visa Expiry Date (Date Field) */}
                        <div className='flex-1'>
                          <FormField
                            control={form.control}
                            name='visaExpiryDate'
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>
                                  Visa Expiry Date{' '}
                                  <span className='text-red-500'>*</span>
                                </FormLabel>
                                <DateTimePicker
                                  granularity='day'
                                  value={field.value}
                                  onChange={field.onChange}
                                  displayFormat={{
                                    hour24: 'dd MMMM yyyy',
                                  }}
                                />

                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                    </>
                  )}

                  {/* Full Address Section */}
                  <div className='flex flex-wrap items-end gap-2'>
                    {/* Address Line 1 Field */}
                    <div className='w-full sm:w-[32%] sm:mr-[1%] mb-2 sm:mb-0'>
                      <FormField
                        control={form.control}
                        name='addressLine'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className='text-sm font-semibold'>
                              Full Address{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder='Enter Address Line 1'
                              />
                            </FormControl>

                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* City / District Input Field */}
                    <div className='w-full sm:w-[32%] sm:mr-[1%] mb-2 sm:mb-0'>
                      <FormField
                        control={form.control}
                        name='cityDistrict'
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder='Enter City / District'
                              />
                            </FormControl>

                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Country Field */}
                    <div className='w-full sm:w-[32%] mb-2 sm:mb-0'>
                      <FormField
                        control={form.control}
                        name='country'
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <CountryDropdown
                                placeholder='Country'
                                defaultValue={field.value as string} 
                                onChange={(c) => field.onChange(c.alpha3)}
                              />
                            </FormControl>

                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {/* Passport Guidelines link */}
                  <div className='pb-4'>
                    <a
                      href='https://cdn.ifza.com/docs/04_visa/pp_eid_reference.pdf'
                      target='_blank'
                      className='text-blue-500 underline'
                    >
                      Passport Guidelines
                    </a>
                  </div>
                </>
              )}

              {/* Role */}
              {/* <FormItem>
                <FormLabel>
                  Role <span className='text-red-500'>*</span>
                </FormLabel>

                <div className='flex flex-wrap gap-x-4 mt-2'> */}
              {/* General Manager */}
              {/* <FormField
                    control={form.control}
                    name='roleGeneralManager'
                    render={({ field }) => (
                      <FormItem>
                        <div className='flex items-center space-x-2'>
                          <Checkbox
                            id='role-generalManager'
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                          <label htmlFor='role-generalManager'>
                            General Manager
                          </label>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  /> */}

              {/* Shareholder */}
              {/* <FormField
                    control={form.control}
                    name='roleShareholder'
                    render={({ field }) => (
                      <FormItem>
                        <div className='flex items-center space-x-2'>
                          <Checkbox
                            id='role-shareholder'
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                          <label htmlFor='role-shareholder'>Shareholder</label>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  /> */}

              {/* Secretary */}
              {/* <FormField
                    control={form.control}
                    name='roleSecretary'
                    render={({ field }) => (
                      <FormItem>
                        <div className='flex items-center space-x-2'>
                          <Checkbox
                            id='role-secretary'
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                          <label htmlFor='role-secretary'>Secretary</label>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  /> */}

              {/* Director */}
              {/* <FormField
                    control={form.control}
                    name='roleDirector'
                    render={({ field }) => (
                      <FormItem>
                        <div className='flex items-center space-x-2'>
                          <Checkbox
                            id='role-director'
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                          <label htmlFor='role-director'>Director</label>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                {!atLeastOneSelected && form.formState.isSubmitted && (
                  <p className='text-sm text-red-500'>Choose your option(s).</p>
                )}
              </FormItem>

              <FormDescription className='mt-2'>
                If there is only one member in the license, please select all
                roles and designations mentioned above. In case of multiple
                members, there can only be 1 General Manager and 1 Secretary.
                You can have multiple Shareholders & Directors.
              </FormDescription> */}

              <div className='absolute bottom-0 left-0 w-full border-t border-gray-100 p-3 pr-10 bg-background flex justify-end space-x-2 z-10'>
                <DialogFooter className='w-full'>
                  <Button
                    type='submit'
                    onClick={() => {
                      form.handleSubmit(handleFormSubmit)
                      console.log('Current Form Values:', form.getValues())
                    }}
                  >
                    Add Member
                  </Button>
                </DialogFooter>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default CompanyMembers