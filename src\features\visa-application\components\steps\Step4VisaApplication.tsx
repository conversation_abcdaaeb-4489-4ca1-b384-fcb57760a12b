import React from 'react'
import { useFormContext, useWatch } from 'react-hook-form'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { DateTimePicker } from '@/components/ui/datetime-picker'
import { ApplicationFormValues } from '../../types/application-form-types'

interface Step4FormProps {
  setDate: React.Dispatch<React.SetStateAction<Date | undefined>>
}

const Step4VisaApplication: React.FC<Step4FormProps> = ({ setDate }) => {
  const form = useFormContext<ApplicationFormValues>()
  const visaType = useWatch({ control: form.control, name: 'visaType' })
  const returnTicket = useWatch({ control: form.control, name: 'returnTicket' })
  const annualLeaveEntitlement = useWatch({ control: form.control, name: 'annualLeaveEntitlement' })

  return (
    <>
    <form className="space-y-4 fz-form">
      <div className='flex flex-wrap space-between gap-y-4'>
        <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
          <FormField
            control={form.control}
            name='typeOfEmployment'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Type of Employment{' '}
                  <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <Input {...field} placeholder='' disabled />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className='flex-1'>
          <FormField
            control={form.control}
            name='employmentDuration'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Employment Duration in Months{' '}
                  <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type='number'
                    placeholder='Enter Employment Duration in Months'
                    onChange={(e) => {
                      const value =
                        e.target.value === ''
                          ? undefined
                          : +e.target.value
                      field.onChange(value)
                    }}
                  />
                </FormControl>

                <FormDescription>
                  Maximum of 24 Months/2 Years
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      {visaType === 'Employment Visa Renewal' && (
        <>
          <FormField
            control={form.control}
            name='jobTitleChange'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Do you want to change the Job title of the Visa
                  holder ?
                </FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Choose' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='Yes'>Yes</SelectItem>
                      <SelectItem value='No'>No</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </>
      )}

      <div className='flex flex-wrap space-between gap-y-4'>
        <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
          <FormField
            control={form.control}
            name='jobTitle'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Job Title{' '}
                  <span style={{ color: 'red' }}>*</span>
                </FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem
                        value='no-attested-degree'
                        disabled
                        style={{
                          fontWeight: 'bold',
                          color: 'red',
                        }}
                      >
                        No Attested Degree Required
                      </SelectItem>

                      {[
                        'Administrative Assistant',
                        'Security Staff',
                        'Public Relations Clerk',
                        'Customer Service Representative',
                        'Tailor General',
                        'General Helper',
                        'Sales Agent',
                        'Site Supervisor',
                        'Customs Clearance Agent',
                        'Photographer',
                        'Sales',
                        'Senior Sales Executive',
                        'Driver',
                        'Film Developer',
                        'Curtains Installation Worker',
                        'Aircraft Field Service Mechanic',
                        'Marketing Executive',
                        'Technical Assistant',
                        'Light Vehicle Mechanic General',
                        'Messenger',
                        'Archive Clerk',
                        'Clerk Assistant',
                        'Company Clerk',
                        'Supervisor',
                        'General Manager',
                        'Investor',
                        'Marketing Assistant',
                        'Partner',
                        'Reception Officer',
                        'Receptionist',
                        'Sales Representative',
                        'Secretary',
                        'Cook',
                        'Executive Assistant',
                        'Cleaner General',
                        'Office Clerks General',
                        'Petroleum Product Sales Gen',
                        'Heavy Truck Driver',
                      ].map((title) => (
                        <SelectItem key={title} value={title}>
                          {title}
                        </SelectItem>
                      ))}

                      <SelectItem
                        value='attested-degree'
                        disabled
                        style={{
                          fontWeight: 'bold',
                          color: 'red',
                        }}
                      >
                        Attested Degree Required
                      </SelectItem>

                      {[
                        'Administrative Director',
                        'Credit Officer',
                        'Financial Officer',
                        'Administrative Officer',
                        'Medical Sales Representative',
                        'Legal Advisor',
                        'General Geologist',
                        'Financial & Administrative Director',
                        'Administration Directors',
                        'Operations Analyst',
                        'Graphic Designer',
                        'Finance Department Assistant',
                        'Senior Engineer',
                        'Trader',
                        'Director of Sales and Marketing',
                        'Information Technology officer',
                        'Marketing Specialist',
                        'Marketing Manager',
                        'Security Manager',
                        'Pilot',
                        'Chief Accountant',
                        'Head Legal Affairs Section',
                        'Investment Controller',
                        'Investment Manager',
                        'Interior Designer',
                        'Legal Affairs Manager',
                        'Technical Manager',
                        'Business Development Manager',
                        'Security Officer',
                        'Technician',
                        'Law Professor',
                        'Manager',
                        'Accountant',
                        'Managing Director',
                        'Assistant Manager',
                        'Sales Supervisor',
                        'Sales Officer',
                        'Administrative Supervisor',
                        'Assistant Managing Director',
                        'Human Resource Director',
                        'Human Resource Manager',
                        'HR Manager',
                        'Office Manager',
                        'Operations Manager',
                        'Sales Manager',
                        'Vice President',
                        'Web Developer',
                        'Software Specialist',
                        'Finance Manager',
                        'Engineering Manager',
                        'Electronics Engineer',
                        'Chairman of the board',
                        'Senior Officer of Legal Affairs',
                        'Civil Engineer/Airports',
                        'Director/Cinema And Tv Films',
                        'Cinema Director',
                        'Administrative Advisor',
                        'Consultant Engineer',
                        'Teacher',
                        'Projects Manager',
                        'Electrical Engineer',
                      ].map((title) => (
                        <SelectItem key={title} value={title}>
                          {title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormDescription>
                  The title which will appear on the residence
                  visa stamped on your passport. Please note these
                  titles are subject to change ay any point during
                  the Visa process. Please let us know if you have
                  any questions.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className='flex-1'>
          <FormField
            control={form.control}
            name='employmentStartDate'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Employment Start Date{' '}
                  <span className='text-red-500'>*</span>
                </FormLabel>
                <DateTimePicker
                  granularity='day'
                  value={field.value}
                  onChange={(date) => {
                    field.onChange(date)
                    setDate(date)
                  }}
                  displayFormat={{
                    hour24: 'dd MMMM yyyy',
                  }}
                />
                <FormDescription>dd/MM/yyyy</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
      <div className='flex flex-wrap space-between gap-y-4'>
        <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
          <FormField
            control={form.control}
            name='probationPeriod'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Probation Period{' '}
                  <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select Probation Period' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='O Months'>
                        O Months
                      </SelectItem>
                      <SelectItem value='1 Month'>
                        1 Month
                      </SelectItem>
                      <SelectItem value='3 Months'>
                        3 Months
                      </SelectItem>
                      <SelectItem value='6 Months'>
                        6 Months
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className='flex-1'>
          <FormField
            control={form.control}
            name='employmentTerminationNotice'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Employment Termination Notice{' '}
                  <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Select Employment Termination Notice' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='1 Month'>
                        1 Month
                      </SelectItem>
                      <SelectItem value='2 Months'>
                        2 Months
                      </SelectItem>
                      <SelectItem value='3 Months'>
                        3 Months
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      <div className='flex flex-wrap space-between gap-y-4'>
        <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
          <FormField
            control={form.control}
            name='educationQualification'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Educational Qualification{' '}
                  <span style={{ color: 'red' }}>*</span>
                </FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Choose' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='No formal education'>
                        No formal education
                      </SelectItem>
                      <SelectItem value='Primary'>
                        Primary
                      </SelectItem>
                      <SelectItem value='High School'>
                        High School
                      </SelectItem>
                      <SelectItem value='Vocational'>
                        Vocational
                      </SelectItem>
                      <SelectItem value="Bachelor's Degree">
                        Bachelor's Degree
                      </SelectItem>
                      <SelectItem value="Master's Degree">
                        Master's Degree
                      </SelectItem>
                      <SelectItem value='Doctorate'>
                        Doctorate
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className='flex-1'>
          <FormField
            control={form.control}
            name='returnTicket'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Return Ticket Eligibility{' '}
                  <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Choose' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='Economy'>
                        Economy
                      </SelectItem>
                      <SelectItem value='Business'>
                        Business
                      </SelectItem>
                      <SelectItem value='First Class'>
                        First Class
                      </SelectItem>
                      <SelectItem value='No Entitlement'>
                        No Entitlement
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      {(returnTicket === 'Economy' ||
        returnTicket === 'Business' ||
        returnTicket === 'First Class') && (
        <>
          <FormField
            control={form.control}
            name='ticketEntitlementPeriod'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Ticket Entitlement Period{' '}
                  <span style={{ color: 'red' }}>*</span>
                </FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Choose' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='1 Year'>
                        1 Year
                      </SelectItem>
                      <SelectItem value='2 Years'>
                        2 Years
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </>
      )}

      <FormField
        control={form.control}
        name='annualLeaveEntitlement'
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              Annual Leave Entitlement{' '}
              <span className='text-red-500'>*</span>
            </FormLabel>
            <FormControl>
              <Select
                onValueChange={field.onChange}
                value={field.value}
              >
                <SelectTrigger>
                  <SelectValue placeholder='Choose' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='Working Days'>
                    Working Days
                  </SelectItem>
                  <SelectItem value='Calendar Days'>
                    Calendar Days
                  </SelectItem>
                </SelectContent>
              </Select>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {annualLeaveEntitlement ===
        'Working Days' && (
          <>
            <FormField
              control={form.control}
              name='workingDays'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Number of Leave Days (Working Days){' '}
                    <span style={{ color: 'red' }}>*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type='number'
                      placeholder='Enter '
                      onChange={(e) => {
                        const value =
                          e.target.value === ''
                            ? undefined
                            : +e.target.value
                        field.onChange(value)
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        )}

      {annualLeaveEntitlement ===
        'Calendar Days' && (
          <>
            <FormField
              control={form.control}
              name='calendarDays'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Number of Leave Days (Calendar Days){' '}
                    <span style={{ color: 'red' }}>*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type='number'
                      placeholder='Enter '
                      onChange={(e) => {
                        const value =
                          e.target.value === ''
                            ? undefined
                            : +e.target.value
                        field.onChange(value)
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        )}
      </form>
    </>
  )
}

export default Step4VisaApplication