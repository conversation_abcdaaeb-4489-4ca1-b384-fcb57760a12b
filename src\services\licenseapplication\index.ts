import { BASE_URL } from '@/utils/network';
import axios from 'axios';
import { useAuthStore } from '@/stores/authStore';

// Helper to format Date or string to YYYY-MM-DD
function formatDate(input: Date | string): string {
  const date = typeof input === 'string' ? new Date(input) : input;
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}


export interface UBOData {
  name: string;
  phone?: string;
  email: string;
  dateOfBirth: Date | string;
  placeOfBirth: Date | string;
  nationality: string;
  passportNumber: string;
  passportIssueDate: Date | string;
  passportExpiry: Date | string;
  residentialAddress: string;
}

export interface LicenseApplicationFormValues {
  applicationDate: Date | string;
  applicationType: string;
  cem: string;
  cemCrmUserId?: string;
  emailAddress: string;
  phone?: string;
  quotationValue: string;
  grossNet: string;
  commissionPlan: string;
  paymentType: string;
  newActivity: string;
  additionalActivity: string;
  companyStatement: boolean;
  countryOfOperation: string[];
  operateAsFranchise?: string;
  tradeNameOfFranchise?: string;
  option1English: string;
  option1Arabic?: string;
  option2English: string;
  option2Arabic?: string;
  option3English: string;
  option3Arabic?: string;
  agreementTerms: boolean;
  branchName: string;
  branchNameArabic?: string;
  countryOfRegistration?: string;
  addressOfParentCompany?: string;
  financialYearStartDate: Date | string | undefined; // Allow undefined
  financialYearEndDate: Date | string;
  licenseType: string;
  tradeLicenseValidity: string;
  visaPackage: string;
  investorOrPartnerVisa: string;
  numberOfPartnerVisas: string;
  includeVisaCostInQuote: string;
  numberOfVisasInQuote: string;
  numberOfInsideCountryVisas?: string;
  numberOfOutsideCountryVisas: string;
  establishmentCard: string;
  shareholdingType: string;
  numberOfShareholders: number;
  proposedShareCapital: number;
  shareValue: number;
  totalNumberOfShares?: string;
  shareholderInIFZA: string;
  companyStatementValue: string;
  agreement?: string;
  declaration: string;
  confirmation: string;
  authorizedSignatoryName: string;
  authorizedSignatoryEmail: string;
  ubos?: UBOData[];
}

export function mainpulateLicenseApplicationData(data: LicenseApplicationFormValues) {
  const payload: any = {
    Application_Date: formatDate(data.applicationDate),
    CEM: data.cem,
    Quotation_Value: data.quotationValue,
    Gross_Net: data.grossNet,
    Commission_Plan: data.commissionPlan,
    CEM_CRM_User_ID: data.cemCrmUserId || '',
    License_Application_Type: data.applicationType,
    Payment_Method: data.paymentType,
    App_Updates_Email_Address: data.emailAddress,
    App_Update_WhatsApp_Number: data.phone || '',
    Are_you_going_to_operate_as_a_Franchise: data.operateAsFranchise || '',
    Company_Name: '', // Not captured in UI
    Country_of_Operation: data.countryOfOperation,
    Option_1_English: data.option1English,
    Option_1_Arabic: data.option1Arabic || '',
    Option_2_English: data.option2English,
    Option_2_Arabic: data.option2Arabic || '',
    Option_3_English: data.option3English,
    Option_3_Arabic: data.option3Arabic || '',
    Terms_Conditions_Agreement: data.agreementTerms ? 'Yes' : 'No',
    License_Type: data.licenseType,
    Trade_License_Validity: data.tradeLicenseValidity,
    Visa_Package: data.visaPackage,
    Investor_Or_Partner_Visa: data.investorOrPartnerVisa,
    Establishment_Card: data.establishmentCard,
    Include_Visa_Cost_in_Quote: data.includeVisaCostInQuote,
    Financial_Year_Start_Date: data.financialYearStartDate ? formatDate(data.financialYearStartDate) : '', // Handle undefined
    Financial_Year_End_Date: formatDate(data.financialYearEndDate),
    No_of_Inside_Country_Visas: data.numberOfInsideCountryVisas || '',
    No_of_Outside_Country_Visas: data.numberOfOutsideCountryVisas,
    Number_Of_Visas_In_Quote: data.numberOfVisasInQuote,
    Shareholding_Type: data.shareholdingType,
    Number_of_Shareholders: String(data.numberOfShareholders),
    Proposed_Share_Capital: String(data.proposedShareCapital),
    Share_Value: String(data.shareValue),
    Total_Number_of_Shares: data.totalNumberOfShares || '',
    Business_Activity: data.newActivity,
    Additional_Activity: data.additionalActivity,
    Branch_Name: data.branchName,
    Branch_Name_Arabic: data.branchNameArabic || '',
    Country_Of_Registration: data.countryOfRegistration || '',
    Address_Of_Parent_Company: data.addressOfParentCompany || '',
    The_company_agrees_to_the_statement: data.companyStatementValue,
    Terms_And_Company_Agreement: data.agreement || '', 
    UBO_Declaration: data.declaration,
    Confirmation: data.confirmation,
    General_Manager_Name: data.authorizedSignatoryName,
    General_Manager_Authorized_Signatory_Email: data.authorizedSignatoryEmail,
    Please_input_Trade_name_of_Franchise: data.tradeNameOfFranchise || '',
    Add_UBO: (data.ubos || []).map((ubo) => ({
      Name1: ubo.name,
      Phone: ubo.phone || '',
      Email: ubo.email,
      Date_of_Birth: ubo.dateOfBirth ? formatDate(ubo.dateOfBirth) : '',
      Place_of_Birth: ubo.placeOfBirth ? formatDate(ubo.placeOfBirth) : '',
      Nationality: ubo.nationality,
      Passport_Number: ubo.passportNumber,
      Passport_Issue_Date: ubo.passportIssueDate ? formatDate(ubo.passportIssueDate) : '',
      Expiry_Date_of_Passport: ubo.passportExpiry ? formatDate(ubo.passportExpiry) : '',
      Residential_Address: ubo.residentialAddress,
    })),
  };
  return payload;
}

export async function createLicenseApplication(payload: any) {
  const token = useAuthStore.getState().auth.accessToken;
  // Determine URL: always append external ID if present
  let url = `${BASE_URL}/api/LIC/create`;
  const externalId = typeof window !== 'undefined'
    ? new URLSearchParams(window.location.search).get('id')
    : null;
  if (externalId) {
    url += `?id=${externalId}`;
  }
  // Build headers: include Authorization only when no external ID
  const headers: Record<string, string> = {};
  if (!externalId && token) {
    headers.Authorization = `Bearer ${token}`;
  }
  const response = await axios.post(url, payload, { headers });
  return response.data;
}

// Map application members to API members schema
export interface LicenseMember {
  id: number
  roleGeneralManager: boolean
  roleShareholder: boolean
  roleSecretary: boolean
  roleDirector: boolean
  title?: string
  firstName?: string
  middleName?: string
  lastName?: string
  numberOfShares: number
  gender: string
  email: string
  mobilePhone: string
  dateOfBirth: Date
  passportNumber: string
  passportIssueDate: Date
  passportExpiryDate: Date
  passportCountryOfIssue: string
  passportPlaceOfIssue: string
  nationality: string
  previousNationality: string
  visitedUaeBefore: string
  areYouResidentValue: string
  eidNumber?: number
  eidIssueDate?: Date
  eidExpiryDate?: Date
  visaNumber?: number
  visaIssueDate?: Date
  visaExpiryDate?: Date
  // UI member includes address components
  addressLine?: string
  cityDistrict?: string
  country?: string
  passport?: File | null
}

/**
 * Prepare members payload for license application
 */
export function mainpulateLicenseMembersData(members: LicenseMember[]) {
  return {
    members: members.map((m) => ({
       Position_in_Board: [
         ...(m.roleDirector ? ['Director'] : []),
         ...(m.roleGeneralManager ? ['General Manager'] : []),
         ...(m.roleShareholder ? ['Shareholder'] : []),
         ...(m.roleSecretary ? ['Secretary'] : []),
       ],
       Salutation: m.title || '',
       Name: `${m.firstName || ''} ${m.middleName || ''} ${m.lastName || ''}`.trim(),
       Member_Name_AR: '',
       Member_First_Name: m.firstName || '',
       Member_First_Name_AR: '',
       Member_Middle_Name: m.middleName || '',
       Member_Middle_Name_AR: '',
       Member_Last_Name: m.lastName || '',
       Member_Last_Name_AR: '',
       No_of_Shares_New: m.numberOfShares || 0,
       Share_Capital_New: 0,
       Gender: m.gender || '',
       Email_Address: m.email || '',
       Mobile_Number: m.mobilePhone || '',
       Date_of_Birth: m.dateOfBirth ? formatDate(m.dateOfBirth) : '',
       Passport_No: m.passportNumber || '',
       Passport_Issue_Date: m.passportIssueDate ? formatDate(m.passportIssueDate) : '',
       Passport_Expiry: m.passportExpiryDate ? formatDate(m.passportExpiryDate) : '',
       Passport_Country_of_Issue: m.passportCountryOfIssue || '',
       Passport_Place_of_Issue: m.passportPlaceOfIssue || '',
       Nationality: m.nationality || '',
       Previous_Nationality: m.previousNationality || '',
       Visited_UAE_Before: m.visitedUaeBefore || '',
       UAE_Resident: m.areYouResidentValue === 'Yes',
       EID_Number: m.eidNumber?.toString() || '',
       EID_Issue_Date: m.eidIssueDate ? formatDate(m.eidIssueDate) : '',
       EID_Expiry_Date: m.eidExpiryDate ? formatDate(m.eidExpiryDate) : '',
       EID_PATH: '',
       Visa_Number: m.visaNumber?.toString() || '',
       Visa_Issue_Date: m.visaIssueDate ? formatDate(m.visaIssueDate) : '',
       Visa_Expiry: m.visaExpiryDate ? formatDate(m.visaExpiryDate) : '',
       Full_Address: `${m.addressLine || ''}${m.cityDistrict ? ', ' + m.cityDistrict : ''}${m.country ? ', ' + m.country : ''}`,
       CRM_ID: '',
       CRM_LIC_ID: '',
     })),
   }
}

/**
 * Send members payload to API for a given license application ID
 */
export async function createLicenseApplicationMembers(
  applicationId: string | number,
  payload: any
) {
  const token = useAuthStore.getState().auth.accessToken;
  // Build URL and always append external ID if present
  let url = `${BASE_URL}/api/LIC/${applicationId}/members`;
  const externalId = typeof window !== 'undefined'
    ? new URLSearchParams(window.location.search).get('id')
    : null;
  if (externalId) {
    url += `?id=${externalId}`;
  }
  // Build headers: include Authorization only when no external ID
  const headers: Record<string, string> = {};
  if (!externalId && token) {
    headers.Authorization = `Bearer ${token}`;
  }
  console.log(`createLicenseApplicationMembers URL: ${url}`);
  const response = await axios.post(url, payload, { headers });
  console.log('createLicenseApplicationMembers response URL:', response.config.url);
  return response.data;
}

/**
 * Uploads member files and details for a specific company member.
 * Endpoint: /api/LIC/{id}/member/{memberid}/file
 * 
 * @param id - License application ID
 * @param memberId - Member ID
 * @param files - File objects: passport (required), eid (optional)
 * @param data - Member details and file numbers:
 *   Member_First_Name, Member_Last_Name,
 *   Passport_Number (required if passport file)
 */
export async function uploadMemberFiles(
  id: string | number,
  memberId: string | number,
  files: { passport: File; eid?: File },
  data: {
    Member_First_Name: string;
    Member_Last_Name: string;
    Passport_Number: string;
  }
) {
  // Prepare FormData and append fields
  const formData = new FormData();
  // Required fields
  formData.append('Member_First_Name', data.Member_First_Name);
  formData.append('Member_Last_Name', data.Member_Last_Name);
  formData.append('Passport_Number', data.Passport_Number);
  // Attach files
  formData.append('passport', files.passport);
  if (files.eid) formData.append('eid', files.eid);
  // Determine URL and append external ID if present
  const token = useAuthStore.getState().auth.accessToken;
  const externalId = typeof window !== 'undefined'
    ? new URLSearchParams(window.location.search).get('id')
    : null;
  let url = `${BASE_URL}/api/LIC/${id}/member/${memberId}/file`;
  if (externalId) {
    url += `?id=${externalId}`;
  }
  // Build headers: only Authorization when no external ID
  const headers: Record<string, string> = {};
  if (!externalId && token) {
    headers.Authorization = `Bearer ${token}`;
  }
  console.log(`uploadMemberFiles URL: ${url}`);
  const response = await axios.post(url, formData, { headers });
  return response.data;
}
