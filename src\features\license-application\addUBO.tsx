import React, { useState } from 'react'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { PlusIcon } from 'lucide-react'
import PhoneInput from 'react-phone-input-2'
import { useToast } from '@/hooks/use-toast'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { CountryDropdown } from '@/components/ui/country-dropdown'
import { DateTimePicker } from '@/components/ui/datetime-picker'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from '@/components/ui/dialog'
import {
  Form,
  FormField,
  FormDescription,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from '@/components/ui/table'

interface Ubo {
  id: number
  name?: string
  phone?: string
  email: string
  dateOfBir?: Date
  placeOfBirth?: string
  nationallity?: string
  passportNum?: string
  passportIssue?: Date
  passportExpiry?: Date
  residentialAddress?: string
  becameUBO?: string
  ceasedUBO?: string
  isPEP?: boolean
  pepProfile?: string
  otherPepProfile?: string
  pepDetails?: string
  incomeSource?: string
  otherIncomeSource?: string
  uboIncomeCountry?: string
  bankNameUae?: string
  bankNameNonUae?: string
}

const uboFormSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters.' }),
  phone: z.string().min(5, { message: 'You must enter at least 5 digits.' }),
  email: z.string().email({ message: 'Invalid email address.' }),
  dateOfBir: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine(
      (date) => {
        const currentDate = new Date() // Get the current date
        const age = currentDate.getFullYear() - date.getFullYear() // Calculate the age based on the year
        const month = currentDate.getMonth() - date.getMonth() // Calculate the difference in months

        // Check if the age is 18 or older
        return age > 18 || (age === 18 && month >= 0)
      },
      {
        message: 'Members must be at least 18 years old', // Error message if the age is less than 18
      }
    ),

  placeOfBirth: z.string().min(1, { message: 'Select a choice.' }),
  nationallity: z.string().min(1, { message: 'Select a choice.' }),

  passportNum: z.string().min(2, { message: 'Enter a value for this field.' }),

  passportIssue: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine(
      (date) => {
        const today = new Date() // Get today's date
        return date <= today // Validate if the date is not in the future
      },
      {
        message: "Passport Issuing date can't be in the future", // Custom error message
      }
    ),

  passportExpiry: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine(
      (date) => {
        const today = new Date()
        const expiryDate = new Date(date)

        // Calculate the difference between the expiry date and today's date
        const diffInTime = expiryDate.getTime() - today.getTime()
        const diffInDays = diffInTime / (1000 * 3600 * 24) // Convert from milliseconds to days

        // Ensure the passport expiry date is at least 120 days in the future
        return diffInDays >= 120
      },
      {
        message: "Passport Expiry Date can't be less than 4 months", // Custom error message
      }
    ),

  residentialAddress: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  becameUBO: z.string().min(2, { message: 'Enter a value for this field.' }),
  ceasedUBO: z.string().min(2, { message: 'Enter a value for this field.' }),
  isPEP: z.boolean({ required_error: 'This field is required' }),

  pepProfile: z.string().min(2, { message: 'Select a choice.' }),

  otherPepProfile: z.string().optional(),

  pepDetails: z.string().min(2, { message: 'Enter a value for this field.' }),
  incomeSource: z.string().min(2, { message: 'Select a choice.' }),

  otherIncomeSource: z.string().optional(),

  uboIncomeCountry: z.string().min(2, { message: 'Select a choice.' }),
  bankNameUae: z.string().min(2, { message: 'Select a choice.' }),
  bankNameNonUae: z.string().min(2, { message: 'Select a choice.' }),
})

type UboFormData = z.infer<typeof uboFormSchema>

const AddUBO: React.FC = () => {
  const [ubo, setUbo] = useState<Ubo[]>([])
  const [dialogOpen, setDialogOpen] = useState(false)
  // const [date, setDate] = useState<Date | undefined>(undefined)
  const { toast } = useToast()

  const form = useForm<UboFormData>({
    resolver: zodResolver(uboFormSchema),
    defaultValues: {
      name: '',
      phone: '',
      email: '',
      dateOfBir: new Date(),
      placeOfBirth: '',
      nationallity: '',
      passportNum: '',
      passportIssue: new Date(),
      passportExpiry: new Date(),
      residentialAddress: '',
      becameUBO: '',
      ceasedUBO: '',
      isPEP: false,
      pepProfile: '',
      otherPepProfile: '',
      pepDetails: '',
      incomeSource: '',
      otherIncomeSource: '',
      uboIncomeCountry: '',
      bankNameUae: '',
      bankNameNonUae: '',
    },
  })

  const handleAddRow = () => {
    setDialogOpen(true)
  }

  const handleCloseDialog = () => {
    setDialogOpen(false)
  }
  const handleEdit = (id: number) => {
    const uboToEdit = ubo.find((item) => item.id === id)
    if (uboToEdit) {
      // Populate the form with the data of the UBO that you want to edit
      form.setValue('name', uboToEdit.name || '')
      form.setValue('phone', uboToEdit.phone || '')
      form.setValue('email', uboToEdit.email || '')
      form.setValue('dateOfBir', uboToEdit.dateOfBir || new Date())
      form.setValue('placeOfBirth', uboToEdit.placeOfBirth || '')
      form.setValue('nationallity', uboToEdit.nationallity || '')
      form.setValue('passportNum', uboToEdit.passportNum || '')
      form.setValue('passportIssue', uboToEdit.passportIssue || new Date())
      form.setValue('passportExpiry', uboToEdit.passportExpiry || new Date())
      form.setValue('residentialAddress', uboToEdit.residentialAddress || '')
      form.setValue('becameUBO', uboToEdit.becameUBO || '')
      form.setValue('ceasedUBO', uboToEdit.ceasedUBO || '')
      form.setValue('isPEP', uboToEdit.isPEP || false)
      form.setValue('pepProfile', uboToEdit.pepProfile || '')
      form.setValue('pepDetails', uboToEdit.pepDetails || '')
      form.setValue('incomeSource', uboToEdit.incomeSource || '')
      form.setValue('uboIncomeCountry', uboToEdit.uboIncomeCountry || '')
      form.setValue('bankNameUae', uboToEdit.bankNameUae || '')
      form.setValue('bankNameNonUae', uboToEdit.bankNameNonUae || '')

      setDialogOpen(true) // Open the dialog with the populated form
    }
  }

  const handleDelete = (id: number) => {
    setUbo((prevUbo) => prevUbo.filter((item) => item.id !== id))
    toast({ description: 'UBO deleted successfully', variant: 'destructive' })
  }

  const handleUboData = (data: UboFormData) => {
    const newUbo: Ubo = {
      id: ubo.length > 0 ? Math.max(...ubo.map((m) => m.id)) + 1 : 1,

      name: data.name,
      phone: data.phone,
      email: data.email,
      dateOfBir: data.dateOfBir,
      placeOfBirth: data.placeOfBirth,
      nationallity: data.nationallity,
      passportNum: data.passportNum,
      passportIssue: data.passportIssue,
      passportExpiry: data.passportExpiry,
      residentialAddress: data.residentialAddress,
      becameUBO: data.becameUBO,
      ceasedUBO: data.ceasedUBO,
      isPEP: data.isPEP,
      pepProfile: data.pepProfile,
      otherPepProfile: data.otherPepProfile,
      pepDetails: data.pepDetails,
      incomeSource: data.incomeSource,
      otherIncomeSource: data.otherIncomeSource,
      uboIncomeCountry: data.uboIncomeCountry,
      bankNameUae: data.bankNameUae,
      bankNameNonUae: data.bankNameNonUae,
    }

    setUbo((prevUbo) => [...prevUbo, newUbo])
    handleCloseDialog()
  }

  const handleFormSubmit = (data: UboFormData) => {
    form.reset()
    handleUboData(data)
  }
  const pepProfile = form.watch('pepProfile')
  const incomeSource = form.watch('incomeSource')
  return (
    <>
      <Table className='w-full table-auto scrollbar-thin'>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Phone</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Date of Birth</TableHead>
            <TableHead>Place of Birth</TableHead>
            <TableHead>Nationality</TableHead>
            <TableHead>Passport Number</TableHead>
            <TableHead>Passport Issue Date</TableHead>
            <TableHead>Passport Expiry Date</TableHead>

            <TableHead>Residential Address</TableHead>
            <TableHead>
              Basis and date (DD/MM/YYYY) on which the individual became an UBO
            </TableHead>
            <TableHead>
              Basis and date (DD/MM/YYYY) on which the individual ceased to be
              an UBO
            </TableHead>
            <TableHead>Is PEP?</TableHead>
            <TableHead>UBO PEP Profile</TableHead>
            <TableHead>PEP Details</TableHead>
            <TableHead>UBO Income Source</TableHead>
            <TableHead>UBO Income Country</TableHead>
            <TableHead>Bank Name (UAE)</TableHead>
            <TableHead>Bank Name (Non UAE)</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {ubo.map((ubo) => (
            <TableRow key={ubo.id}>
              <TableCell>{ubo.name}</TableCell>
              <TableCell>{ubo.phone}</TableCell>
              <TableCell>{ubo.email}</TableCell>
              <TableCell>
                {ubo.dateOfBir?.toLocaleDateString() || 'N/A'}
              </TableCell>
              <TableCell>{ubo.placeOfBirth}</TableCell>
              <TableCell>{ubo.nationallity}</TableCell>
              <TableCell>{ubo.passportNum}</TableCell>
              <TableCell>
                {ubo.passportIssue?.toLocaleDateString() || 'N/A'}
              </TableCell>{' '}
              <TableCell>
                {ubo.passportExpiry?.toLocaleDateString() || 'N/A'}
              </TableCell>
              <TableCell>{ubo.residentialAddress}</TableCell>
              <TableCell>{ubo.becameUBO}</TableCell>
              <TableCell>{ubo.ceasedUBO}</TableCell>
              <TableCell>{ubo.isPEP ? 'True' : 'False'}</TableCell>
              <TableCell>{ubo.pepProfile}</TableCell>
              {/* <TableCell>{ubo.otherPepProfile}</TableCell> */}
              <TableCell>{ubo.pepDetails}</TableCell>
              <TableCell>{ubo.incomeSource}</TableCell>
              {/* <TableCell>{ubo.otherIncomeSource}</TableCell> */}
              <TableCell>{ubo.uboIncomeCountry}</TableCell>
              <TableCell>{ubo.bankNameUae}</TableCell>
              <TableCell>{ubo.bankNameNonUae}</TableCell>
              {/* Add Edit and Delete actions */}
              <TableCell className='flex gap-2'>
                {/* Edit Icon Button */}
                <Button
                  variant='link'
                  onClick={() => handleEdit(ubo.id)}
                  className='text-blue-500'
                >
                  {/* <EditIcon /> */}
                </Button>
                {/* Delete Icon Button */}
                <Button
                  variant='link'
                  onClick={() => handleDelete(ubo.id)}
                  className='text-red-500'
                >
                  {/* <Trash2Icon /> */}
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <Button
        type='button'
        variant='link'
        className='mt-4'
        onClick={handleAddRow}
      >
        <PlusIcon className='mr-2' /> Add UBO
      </Button>
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className='h-[90vh] pb-14 pr-2' variant='default'>
          <DialogHeader className='static'>
            <DialogTitle>Add UBO(s)</DialogTitle>
            <DialogDescription>
              Please fill out the details of the UBO :
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleFormSubmit)}
              className='scrollbar-thin overflow-y-auto pr-1 pl-1 space-y-4 fz-form'
            >
              <div className='flex flex-wrap space-between gap-y-4'>
                {/* Name Field */}
                <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                  <FormField
                    control={form.control}
                    name='name'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Name</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder='Please Enter Full Name First Name and Last Name'
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Phone Field */}
                <div className='flex-1'>
                  <FormField
                    control={form.control}
                    name='phone'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone</FormLabel>
                        <PhoneInput
                          country={'ae'}
                          // value={field.value.toString()}
                          onChange={(phone) => field.onChange(phone)}
                          containerClass='fz-country-code flex h-9 w-full rounded-tl-[6px] rounded-bl-[6px] border border-input bg-transparent text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm'
                          inputClass=' flex  !h-9 !w-full rounded-tl-[6px] rounded-bl-[6px] border !border-transparent !bg-transparent px-3 text-base !shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm transform translate-y-[-2px]'
                          buttonClass='!h-100 !border-l-transparent !border-t-transparent !bg-slate-200 dark:!bg-slate-800 dark:!border-r-transparent !border-b-transparent !border-gray-200 !bg-transparent !rounded-tl-[8px] !rounded-bl-[6px]'
                          enableSearch={true}
                          searchPlaceholder='Search country...'
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              <div className='flex flex-wrap space-between gap-y-4'>
                {/* Email Field */}
                <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                  <FormField
                    control={form.control}
                    name='email'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input
                            type='email'
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Date of Birth Field */}
                <div className='flex-1'>
                  <FormField
                    control={form.control}
                    name='dateOfBir'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Date of Birth</FormLabel>
                        <DateTimePicker
                          granularity='day'
                          value={field.value}
                          onChange={field.onChange}
                          displayFormat={{
                            hour24: 'dd MMMM yyyy', // 31 January 2024
                          }}
                        />
                        <FormDescription>dd-MMM-yyyy</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className='flex flex-wrap space-between gap-y-4'>
                {/* Place of Birth Field */}
                <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                  <FormField
                    control={form.control}
                    name='placeOfBirth'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Place of Birth</FormLabel>
                        <FormControl>
                          <CountryDropdown
                            placeholder='Country'
                            defaultValue={field.value as string} 
                            onChange={(c) => field.onChange(c.alpha3)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Nationality Field */}
                <div className='flex-1'>
                  <FormField
                    control={form.control}
                    name='nationallity'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nationality</FormLabel>
                        <FormControl>
                          <CountryDropdown
                            placeholder='Country'
                            defaultValue={field.value as string} 
                            onChange={(c) => field.onChange(c.alpha3)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className='flex flex-wrap space-between gap-y-4'>
                {/* Passport Number Field */}
                <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                  <FormField
                    control={form.control}
                    name='passportNum'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Passport Number</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder='Enter Passport Number'
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Passport Issue Date Field */}
                <div className='flex-1'>
                  <FormField
                    control={form.control}
                    name='passportIssue'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Passport Issue Date</FormLabel>
                        <DateTimePicker
                          granularity='day'
                          value={field.value}
                          onChange={field.onChange}
                          displayFormat={{
                            hour24: 'dd MMMM yyyy', // 31 January 2024
                          }}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className='flex flex-wrap space-between gap-y-4'>
                {/* Passport Expiry Date Field */}
                <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                  <FormField
                    control={form.control}
                    name='passportExpiry'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Passport Expiry Date</FormLabel>
                        <DateTimePicker
                          granularity='day'
                          value={field.value}
                          onChange={field.onChange}
                          displayFormat={{
                            hour24: 'dd MMMM yyyy', // 31 January 2024
                          }}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Residential Address Field */}
                <div className='flex-1'>
                  <FormField
                    control={form.control}
                    name='residentialAddress'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Residential Address</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder='Enter Residential Address'
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className='flex flex-wrap space-between gap-y-4'>
                {/* Basis and Date (DD/MM/YYYY) on which the individual became an UBO Field */}

                <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                  <FormField
                    control={form.control}
                    name='becameUBO'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Basis and Date (DD/MM/YYYY) on which the individual
                          became an UBO :
                        </FormLabel>
                        <FormControl>
                          <Input {...field} placeholder='Enter' />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Basis and Date (DD/MM/YYYY) on which the individual ceased to be an UBO Field */}

                <div className='flex-1'>
                  <FormField
                    control={form.control}
                    name='ceasedUBO'
                    render={({ field }) => (
                      <FormItem className='mb-4'>
                        <FormLabel>
                          Basis and Date (DD/MM/YYYY) on which the individual
                          ceased to be an UBO (if applicable) :
                        </FormLabel>
                        <FormControl>
                          <Input {...field} placeholder='Enter ' />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Is PEP? Field */}
              <div className='flex items-center space-x-2'>
                <Checkbox
                  id='is-pep'
                  checked={form.watch('isPEP')} // Watch form value
                  onCheckedChange={(checked: boolean) =>
                    form.setValue('isPEP', checked)
                  } // Update form value when checked
                />

                <label className='text-sm font-normal ml-2' htmlFor='is-pep'>
                  Is PEP ?
                </label>
              </div>

              {/* UBO PEP Profile Field */}

              <div className='mb-4 mt-4'>
                <FormField
                  control={form.control}
                  name='pepProfile'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>UBO PEP Profile</FormLabel>
                      <FormControl>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder='Select UBO PEP Profile' />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='Head of State or of Government'>
                              Head of State or of Government
                            </SelectItem>
                            <SelectItem value='Senior politician'>
                              Senior politician
                            </SelectItem>
                            <SelectItem value='Senior government official'>
                              Senior government official
                            </SelectItem>
                            <SelectItem value='Judicial (supreme court/head of court of appeal/head of first instance court)'>
                              Judicial (supreme court/head of court of
                              appeal/head of first instance court)
                            </SelectItem>
                            <SelectItem value='Military official (military rank of Colonel and above)'>
                              Military official (military rank of Colonel and
                              above)
                            </SelectItem>

                            <SelectItem value='Member of the ruling family'>
                              Member of the ruling family
                            </SelectItem>
                            <SelectItem value='CEO of a government owned company'>
                              CEO of a government owned company
                            </SelectItem>
                            <SelectItem value='CEO of a company linked to the government'>
                              CEO of a company linked to the government
                            </SelectItem>

                            <SelectItem value='Senior political party official'>
                              Senior political party official
                            </SelectItem>

                            <SelectItem value='Other'>Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {pepProfile === 'Other' && (
                <>
                  <div className='mb-4 mt-4'>
                    <FormField
                      control={form.control}
                      name='otherPepProfile'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Please Specify</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder='Please Specify' />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </>
              )}

              {/* PEP Details Field */}

              <div className='mb-4 mt-4'>
                <FormField
                  control={form.control}
                  name='pepDetails'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>PEP Details</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder='Please Specify' />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* UBO Income Source Field */}

              <div className='mb-4 mt-4'>
                <FormField
                  control={form.control}
                  name='incomeSource'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>UBO Income Source</FormLabel>
                      <FormControl>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder='Choose Income Source' />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='License Shareholders'>
                              License Shareholders
                            </SelectItem>
                            <SelectItem value='Personal Investments'>
                              Personal Investments
                            </SelectItem>

                            <SelectItem value='Third Party Investment'>
                              Third Party Investment
                            </SelectItem>
                            <SelectItem value='Ultimate Beneficiary members'>
                              Ultimate Beneficiary members
                            </SelectItem>

                            <SelectItem value='Other'>Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Show the "Other" input field only when "Other" is selected */}

              {incomeSource === 'Other' && (
                <>
                  <div className='mb-4 mt-4'>
                    <FormField
                      control={form.control}
                      name='otherIncomeSource'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Please Specify</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder='Please Specify' />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </>
              )}

              {/* UBO Income Country Field */}

              <div className='mb-4 mt-4'>
                <FormField
                  control={form.control}
                  name='uboIncomeCountry'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>UBO Income Country</FormLabel>
                      <FormControl>
                        <CountryDropdown
                          placeholder='Country'
                          defaultValue={field.value}
                          onChange={(country) => {
                            field.onChange(country.alpha3)
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className='flex flex-wrap space-between gap-y-4'>
                {/* Bank Name (UAE) Field */}

                <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-4'>
                  <FormField
                    control={form.control}
                    name='bankNameUae'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bank Name (UAE)</FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder='Choose Bank Name (UAE)' />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value='ADCB'>ADCB</SelectItem>
                              <SelectItem value='Mashreq'>Mashreq</SelectItem>

                              <SelectItem value='WIO'>WIO</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Bank Name (Non UAE) Field */}

                <div className='flex-1'>
                  <FormField
                    control={form.control}
                    name='bankNameNonUae'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bank Name (Non UAE)</FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder='Choose Bank Name (Non UAE)' />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value='Choice 1'>Choice 1</SelectItem>
                              <SelectItem value='Choice 2'>Choice 2</SelectItem>
                              <SelectItem value='Choice 3'>Choice 3</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Add UBO Button */}
              <div className='absolute bottom-0 left-0 w-full border-t border-gray-100 p-3 pr-10 bg-background flex justify-end space-x-2 z-10 '>
                <DialogFooter className='w-full'>
                  <Button type='submit'>Add UBO</Button>
                </DialogFooter>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default AddUBO
