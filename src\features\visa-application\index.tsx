import React, { useState, useEffect } from 'react'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Lightbulb, FilePenLine, Loader2 } from 'lucide-react'
import PhoneInput from 'react-phone-input-2'
import { toast } from '@/hooks/use-toast'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { CountryDropdown } from '@/components/ui/country-dropdown'
import { DateTimePicker } from '@/components/ui/datetime-picker'
import FileUploadField from '@/components/ui/fileUpload'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import ApplicationProgress from '@/components/application-progress'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

const applicationFormSchema = z.object({
  appUpdatesEmail: z.string().email({ message: 'Invalid email address.' }),
  whatsAppNumber: z.string().optional(),
  applicationDate: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine((date) => !isNaN(date.getTime()), {
      message: 'Invalid date format',
    }),

  visaType: z.string().min(1, { message: 'Select a choice.' }),
  tradeLicenseValidated: z.boolean().refine((val) => val === true, {
    message: 'You must accept the terms and conditions.',
  }),
  visaApplicationType: z.string().min(1, { message: 'Select a choice.' }),
  nationality: z.string().min(1, { message: 'Select a choice.' }),
  residantVisaStamping: z.string().min(1, { message: 'Select a choice.' }),
  familyOnHoldLetter: z.string().optional(),
  renewalDisclaimer: z
    .boolean()
    .refine((val) => val === true, {
      message: 'Select this option.',
    })
    .optional(),
  visaFree: z.string().min(1, { message: 'Select a choice.' }).optional(),
  outsideCountry: z
    .boolean()
    .refine((val) => val === true, {
      message: 'Choose this option.',
    })
    .optional(),
  updatedRVCopy: z
    .any()
    .refine((val) => val instanceof File && val.size > 0, {
      message: 'Upload a file here.',
    })
    .optional(),
  idCardUpload: z
    .any()
    .refine((val) => val instanceof File && val.size > 0, {
      message: 'Upload a file here.',
    })
    .optional(),

  currentVisaStatus: z
    .string()
    .min(1, { message: 'Select a choice.' })
    .optional(),
  visaValidUntil: z.date().optional(),
  title: z.string().min(1, { message: '*' }),
  firstName: z.string().min(2, { message: 'Required.' }),
  middleName: z.string().optional(),
  lastName: z.string().min(2, { message: 'Required.' }),
  title1: z.string().optional(),
  arabicName: z.string().min(1, { message: 'Select a choice.' }),
  firstNameArabic: z.string().optional(),
  middleNameArabic: z.string().optional(),
  lastNameArabic: z.string().optional(),
  emailAddress: z.string().email({ message: 'Invalid email address.' }),
  phone: z.string().min(5, { message: 'You must enter at least 5 digits.' }),
  streetAddress: z.string().optional(),
  addressLine: z.string().optional(),
  cityAddress: z.string().optional(),
  stateProvince: z.string().optional(),
  country: z.string().optional(),
  streetAddress1: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  addressLine2: z.string().optional(),
  city: z.string().min(2, { message: 'Enter a value for this field.' }),
  province: z.string().optional(),
  country1: z.string().min(1, { message: 'Select a choice.' }),

  emiratesID: z.string().min(1, { message: 'Select a choice.' }),
  emiratesIDNumber: z.string().optional(),
  emiratesIDExpiryDate: z.date().optional(),
  emiratesIDCopy: z
    .any()
    .refine((val) => val instanceof File && val.size > 0, {
      message: 'Upload a file here.',
    })
    .optional(),

  nationality1: z.string().min(1, { message: 'Select a choice.' }),
  passportNumber: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  placeOfIssue: z.string().min(2, { message: 'Enter a value for this field.' }),
  placeOfIssueArabic: z.string().optional(),
  passportType: z.string().min(1, { message: 'Select a choice.' }),
  agreementPassportRules: z.boolean().refine((val) => val === true, {
    message: 'You must accept the terms and conditions.',
  }),

  eVisaApplicationType: z
    .any()
    // .min(1, { message: 'Select a choice.' })
    .optional(),

  Agreed: z
    .boolean()
    .refine((val) => val === true, {
      message: 'Choose this option.',
    })
    .optional(),
  countryOfIssuance: z.string().min(1, { message: 'Select a choice.' }),
  coloredPassport: z
    .any()
    .refine((val) => val instanceof File && val.size > 0, {
      message: 'Upload a file here.',
    }),
  coloredPassport2: z
    .any()
    .refine((val) => val instanceof File && val.size > 0, {
      message: 'Upload a file here.',
    }),
  passportIssueDate: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine(
      (date) => {
        const today = new Date() // Get today's date
        return date <= today // Validate if the date is not in the future
      },
      {
        message: "Passport Issuing date can't be in the future", // Custom error message
      }
    ),
  passportExpiryDate: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine(
      (date) => {
        const today = new Date()
        const expiryDate = new Date(date)

        // Calculate the difference between the expiry date and today's date
        const diffInTime = expiryDate.getTime() - today.getTime()
        const diffInDays = diffInTime / (1000 * 3600 * 24) // Convert from milliseconds to days

        // Ensure the passport expiry date is at least 210 days in the future (7 months)
        return diffInDays >= 210
      },
      {
        message: "Passport Expiry Date can't be less than 7 months", // Custom error message
      }
    ),
  cityOfBirth: z.string().min(2, { message: 'Enter a value for this field.' }),
  cityOfBirthArabic: z.string().optional(),
  countryOfBirth: z.string().min(1, { message: 'Select a choice.' }),
  dateOfBirth: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine((date) => !isNaN(date.getTime()), {
      message: 'Invalid date format',
    }),
  gender: z.string().min(1, { message: 'Select a choice.' }),
  previousNationality: z.string().optional(),
  maritalStatus: z.string().min(1, { message: 'Select a choice.' }),
  religion: z.string().min(1, { message: 'Select a choice.' }),
  religionSubCategory: z
    .string()
    .min(1, { message: 'Select a choice.' })
    .optional(),
  fatherFullName: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  motherFullName: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  motherFullNameArabic: z.string().optional(),
  photoOfApplicant: z
    .any()
    .refine((val) => val instanceof File && val.size > 0, {
      message: 'Upload a file here.',
    }),

  visaApplicantFiles: z
    .any()
    .refine((val) => val instanceof File && val.size > 0, {
      message: 'Upload a file here.',
    }),

  typeOfEmployment: z.string().optional(),
  employmentDuration: z
    .number()
    .min(0, { message: 'Enter a value greater than or equal to 0.' }) // Validate min value
    .max(24, { message: 'Enter a value less than or equal to 24.' }), // Validate max value
  jobTitleChange: z.string().optional(),
  jobTitle: z.string().min(1, { message: 'Select a choice.' }),
  educationQualification: z.string().min(1, { message: 'Select a choice.' }),
  employmentStartDate: z
    .union([z.date(), z.string().transform((str) => new Date(str))])
    .refine((date) => !isNaN(date.getTime()), {
      message: 'Invalid date format',
    }),
  probationPeriod: z.string().min(1, { message: 'Select a choice.' }),
  employmentTerminationNotive: z
    .string()
    .min(1, { message: 'Select a choice.' }),
  returnTicket: z.string().min(1, { message: 'Select a choice.' }),
  ticketEntitlementPeriod: z
    .string()
    .min(1, { message: 'Select a choice.' })
    .optional(),
  annualLeaveEntitlement: z.string().min(1, { message: 'Select a choice.' }),
  workingDays: z
    .number()
    .min(22, { message: 'Enter a value greater than or equal to 22.' }) // Validate min value
    .max(99, { message: 'Enter a value less than or equal to 99.' }) // Validate max value
    .optional(),
  calendarDays: z
    .number()
    .min(30, { message: 'Enter a value greater than or equal to 30.' }) // Validate min value
    .max(99, { message: 'Enter a value less than or equal to 99.' }) // Validate max value
    .optional(),
  salaryChange: z.string().optional(),
  basicSalary: z
    .number()
    .min(10, { message: 'You must enter at least 2 digits.' }) // Minimum 2 digits
    .max(9999999, { message: 'Maximum limit : 7 digits.' }), // Maximum 7 digits

  transportationAllowance: z.number().optional(),
  accommodationAllowance: z.number().optional(),
  otherAllowance: z.number().optional(),
  totalMonthlySalary: z.number().optional(),
  preferredPaymentMethod: z.string().min(1, { message: 'Select a choice.' }),
  visaFee: z.number().refine((val) => val === 3750, {
    message: 'Visa Fee must match the required amount : 3750',
  }), // Visa Fee must match the required amount: 3750.
  statusChange: z
    .number()
    .min(160, { message: 'Enter a value greater than or equal to 160.' }) // Validate min value
    .max(1600, { message: 'Enter a value less than or equal to 1600.' }), // Validate max value

  vipStampingFee: z.number().refine((val) => val === 1500, {
    message: 'VIP Stamping Fee must match the required amount : 1500',
  }),
  partnerInvestorVisa: z.number().refine((val) => val === 1000, {
    message: 'Partner/Investor Visa must match the required amount : 1000',
  }),
  totalAmountToBePaid: z.number().optional(),
  proofOfPayment: z.any().optional(),
  companyName: z.string().min(2, { message: 'Enter a value for this field.' }),
  tradeLicenseNumber: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  establishmentCardNumber: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  authorizedSignatory: z
    .string()
    .min(2, { message: 'Enter a value for this field.' }),
  emailAddressOfGeneralManager: z
    .string()
    .email({ message: 'Invalid email address.' }),
  termsAndConditions: z.boolean().refine((val) => val === true, {
    message: 'You must agree to the Terms & Conditions Agreement.',
  }),

  affirmInformation: z.boolean().refine((val) => val === true, {
    message: 'You must accept the terms and conditions.',
  }),
})

type ApplicationFormValues = z.infer<typeof applicationFormSchema>
export default function VisaApplication() {
  const [isFormSubmitted, setIsFormSubmitted] = useState(false)
  console.log('isFormSubmitted', isFormSubmitted)
  const [isLoading, setIsLoading] = useState(false)
  useState<boolean>(false)
  const [date, setDate] = useState<Date | undefined>(undefined)
  const form = useForm<ApplicationFormValues>({
    resolver: zodResolver(applicationFormSchema),
    defaultValues: {
      appUpdatesEmail: '',
      whatsAppNumber: '',
      applicationDate: new Date(),
      visaType: '',
      tradeLicenseValidated: false, // Trade license must be valid for 90 days ...
      visaApplicationType: '',
      nationality: '',
      residantVisaStamping: '', // Resident Visa Stamping Type
      familyOnHoldLetter: '', // Do you require a family on hold letter ?
      renewalDisclaimer: false,
      visaFree: '', // Do you want to apply VFL for this VISA ?
      eVisaApplicationType: '',
      outsideCountry: false, // Outside Country Visa Declaration
      updatedRVCopy: '',
      idCardUpload: '', // National ID Card upload
      currentVisaStatus: '',
      visaValidUntil: new Date(),
      title: '',
      firstName: '',
      middleName: '',
      lastName: '',
      title1: '',
      arabicName: '', // Do you have Arabic Name in your Passport?
      firstNameArabic: '',
      middleNameArabic: '',
      lastNameArabic: '',
      emailAddress: '',
      phone: '',
      streetAddress: '', // UAE Address
      addressLine: '', // UAE Address
      cityAddress: '', // UAE Address
      stateProvince: '', // UAE Address
      country: '', // UAE Address
      streetAddress1: '', // Home Country Address
      addressLine2: '', // Home Country Address
      city: '', // Home Country Address
      province: '', // Home Country Address
      country1: '', // Home Country Address
      emiratesID: '',
      emiratesIDNumber: '',
      emiratesIDExpiryDate: new Date(),
      emiratesIDCopy: null,
      nationality1: '', // Nationality : Step 3
      passportNumber: '',
      placeOfIssue: '',
      placeOfIssueArabic: '',
      passportType: '',
      agreementPassportRules: false, // Agreement to passport rules
      Agreed: false, // I agree to the above status change statement and the rules associated
      countryOfIssuance: '',
      coloredPassport: null, // Colored Passport Copy Page 1
      coloredPassport2: null, // Colored Passport Copy Page 2
      passportIssueDate: undefined,
      passportExpiryDate: undefined,
      cityOfBirth: '',
      cityOfBirthArabic: '',
      countryOfBirth: '',
      dateOfBirth: undefined,
      gender: '',
      previousNationality: '',
      maritalStatus: '',
      religion: '',
      religionSubCategory: '',
      fatherFullName: '',
      motherFullName: '',
      motherFullNameArabic: '',
      photoOfApplicant: null,
      visaApplicantFiles: null,
      typeOfEmployment: 'Limited',
      employmentDuration: 1, // Employment Duration in Months
      jobTitleChange: '', // Do you want to change the Job title of the Visa holder ?
      jobTitle: '',
      educationQualification: '',
      employmentStartDate: undefined,
      probationPeriod: '',
      employmentTerminationNotive: '',
      returnTicket: '', // Return Ticket Eligibility
      ticketEntitlementPeriod: '',
      annualLeaveEntitlement: '',
      workingDays: 2,
      calendarDays: 3,
      salaryChange: '',
      basicSalary: 1,
      transportationAllowance: 0,
      accommodationAllowance: 0,
      otherAllowance: 0,
      totalMonthlySalary: 0,
      preferredPaymentMethod: '',
      visaFee: 3750,
      statusChange: 160,
      vipStampingFee: 1500,
      partnerInvestorVisa: 1000, // Partner/Investor Visa
      totalAmountToBePaid: 0,
      proofOfPayment: null,
      companyName: '',
      tradeLicenseNumber: '',
      establishmentCardNumber: '',
      authorizedSignatory: '',
      emailAddressOfGeneralManager: '',
      termsAndConditions: false, // Terms & Conditions Agreement : Step 6
      affirmInformation: false, // I affirm that all the information provided .. : Step 7
    },
    // shouldUnregister: true,
    mode: 'all',
    criteriaMode: 'all',
  })

  const [showEVisaType, setShowEVisaType] = useState(false)
  const [basicSalary, setBasicSalary] = useState(0)
  const [transportationAllowance, setTransportationAllowance] = useState(0)
  const [accommodationAllowance, setAccommodationAllowance] = useState(0)
  const [otherAllowance, setOtherAllowance] = useState(0)

  // calculateTotalSalary
  const calculateTotalSalary = () => {
    return (
      basicSalary +
      transportationAllowance +
      accommodationAllowance +
      otherAllowance
    )
  }

  useEffect(() => {
    // Update the total salary whenever any of the fields change
    const totalSalary = calculateTotalSalary()
    form.setValue('totalMonthlySalary', totalSalary)
  }, [
    basicSalary,
    transportationAllowance,
    accommodationAllowance,
    otherAllowance,
  ])

  useEffect(() => {
    const nationality = form.watch('nationality')
    const visaType = form.watch('visaType')

    // Ensure that nationality is not undefined or empty, and compare it to the list of nationalities that require an eVisa
    // 'Palestine State' is Missing in the list of nationalities
    const shouldShowEVisa = Boolean(
      nationality &&
        visaType &&
        visaType !== 'New Employment Visa' &&
        visaType !== 'New Investor Visa' &&
        visaType !== 'New Partner Visa' &&
        ![
          'AFG',
          'BGD',
          'DZA',
          'CHN',
          'EGY',
          'IDN',
          'IRN',
          'IRQ',
          'ISR',
          'LBN',
          'MAR',
          'NPL',
          'NGA',
          'LBY',
          'PAK',
          'SOM',
          'LKA',
          'SYR',
          'TUN',
          'YEM',
        ].includes(nationality)
    )
    // Set the state to a boolean value (true or false)
    setShowEVisaType(shouldShowEVisa)
  }, [form.watch('nationality'), form.watch('visaType')])

  const [_, setNationality] = useState<string>('')

  useEffect(() => {
    // Watch the nationality field value from the form and set it to state
    const nationalityValue = form.watch('nationality') ?? ''
    setNationality(nationalityValue)
  }, [form.watch('nationality')])

  const [currentStep, setCurrentStep] = React.useState(1) // Track current step

  const { watch, setValue, getValues } = form

  useEffect(() => {
    // Watch for changes in the fields
    const subscription = watch((values) => {
      const {
        visaFee = 0,
        statusChange = 0,
        vipStampingFee = 0,
        partnerInvestorVisa = 0,
        visaApplicationType,
        visaType,
        residantVisaStamping,
        jobTitle,
      } = values

      let total = 0

      // Always add visaFee if available
      total += Number(visaFee) || 0

      // Add statusChange only when Visa App Type is 'In-Country'
      // and Visa Type is NOT 'Employment Visa Renewal' or 'Work Permit Renewal'
      if (
        visaApplicationType === 'In-Country' &&
        visaType !== 'Employment Visa Renewal' &&
        visaType !== 'Work Permit Renewal'
      ) {
        total += Number(statusChange) || 0
      }

      // Add VIP Stamping Fee only when stamping type is 'VIP'
      if (residantVisaStamping === 'VIP') {
        total += Number(vipStampingFee) || 0
      }

      // Add Partner/Investor Visa fee only when job title is 'Partner' or 'Investor'
      if (jobTitle === 'Partner' || jobTitle === 'Investor') {
        total += Number(partnerInvestorVisa) || 0
      }

      // Check if the total has changed before calling setValue
      const currentTotal = getValues('totalAmountToBePaid')
      if (currentTotal !== total) {
        // Update the totalAmountToBePaid field with the computed sum if it's different
        setValue('totalAmountToBePaid', total)
      }
    })

    // Cleanup the subscription on unmount
    return () => subscription.unsubscribe()
  }, [watch, setValue, getValues])
  const nationality = form.watch('nationality')

  const fieldsPerStep: Record<number, (keyof ApplicationFormValues)[]> = {
    1: [
      'appUpdatesEmail',
      'whatsAppNumber',
      'applicationDate',
      'visaType',
      'familyOnHoldLetter',
      'tradeLicenseValidated',
      'renewalDisclaimer',
      'visaApplicationType',
      'visaFree',
      'outsideCountry',
      'currentVisaStatus',
      'nationality',
      'Agreed',
      'updatedRVCopy',
      'idCardUpload',
      'eVisaApplicationType',
      'residantVisaStamping',
      'visaValidUntil',
    ], // Fields for step 1

    2: [
      'title',
      'firstName',
      'middleName',
      'lastName',
      'title1',
      'firstNameArabic',
      'middleNameArabic',
      'lastNameArabic',
      'emailAddress',
      'phone',
      'streetAddress',
      'addressLine',
      'cityAddress',
      'stateProvince',
      'country',
      'streetAddress1',
      'addressLine2',
      'city',
      'province',
      'country1',
      'emiratesID',
      'emiratesIDNumber',
      'emiratesIDExpiryDate',
      'emiratesIDCopy',
    ], // Fields for step 2

    3: [
      'nationality1',
      'arabicName',
      'passportNumber',
      'placeOfIssue',
      'placeOfIssueArabic',
      'passportType',
      'agreementPassportRules',
      'countryOfIssuance',
      'coloredPassport',
      'coloredPassport2',
      'passportIssueDate',
      'passportExpiryDate',
      'cityOfBirth',
      'cityOfBirthArabic',
      'countryOfBirth',
      'dateOfBirth',
      'gender',
      'previousNationality',
      'maritalStatus',
      'religion',
      'religionSubCategory',
      'fatherFullName',
      'motherFullName',
      'motherFullNameArabic',
    ], // Fields for step 3

    4: [
      'typeOfEmployment',
      'employmentDuration',
      'jobTitleChange',
      'jobTitle',
      'employmentStartDate',
      'probationPeriod',
      'employmentTerminationNotive',
      'educationQualification',
      'returnTicket',
      'ticketEntitlementPeriod',
      'annualLeaveEntitlement',
      'workingDays',
      'calendarDays',
    ], // Fields for step 4

    5: [
      'salaryChange',
      'basicSalary',
      'transportationAllowance',
      'accommodationAllowance',
      'otherAllowance',
      'totalMonthlySalary',
    ], // Fields for step 5

    6: [
      'companyName',
      'tradeLicenseNumber',
      'establishmentCardNumber',
      'authorizedSignatory',
      'emailAddressOfGeneralManager',
      'termsAndConditions',
    ], // Fields for step 6

    7: [
      'photoOfApplicant',
      'visaApplicantFiles',
      'proofOfPayment',
      'preferredPaymentMethod',
      'visaFee',
      'statusChange',
      'vipStampingFee',
      'partnerInvestorVisa',
      'totalAmountToBePaid',
      'firstName',
      'lastName',
      'affirmInformation',
    ], // Fields for step 7
  }

  const [stepValues, setStepValues] = useState<
    Record<number, ApplicationFormValues>
  >({})

  const getStepSchema = (step: number, values: ApplicationFormValues) => {
    const ineligibleNationalities = [
      'AFG',
      'BGD',
      'DZA',
      'CHN',
      'EGY',
      'IDN',
      'IRN',
      'IRQ',
      'ISR',
      'LBN',
      'MAR',
      'NPL',
      'NGA',
      'LBY',
      'PAK',
      'SOM',
      'LKA',
      'SYR',
      'TUN',
      'YEM',
    ]
    switch (step) {
      case 1:
        return z.object({
          appUpdatesEmail: applicationFormSchema.shape.appUpdatesEmail,
          visaType: applicationFormSchema.shape.visaType,
          tradeLicenseValidated:
            applicationFormSchema.shape.tradeLicenseValidated,
          ...(values.visaType === 'Employment Visa Renewal' && {
            renewalDisclaimer: applicationFormSchema.shape.renewalDisclaimer,
          }),
          visaApplicationType: applicationFormSchema.shape.visaApplicationType,
          ...((form.watch('visaType') === 'New Employment Visa' ||
            form.watch('visaType') === 'New Investor Visa' ||
            form.watch('visaType') === 'New Partner Visa') && {
            visaFree: applicationFormSchema.shape.visaFree,
          }),
          ...(values.visaApplicationType === 'Out of Country' && {
            outsideCountry: applicationFormSchema.shape.outsideCountry,
          }),
          ...(values.visaApplicationType === 'In-Country' && {
            currentVisaStatus: applicationFormSchema.shape.currentVisaStatus,
          }),
          nationality: applicationFormSchema.shape.nationality,
          ...(form.watch('visaApplicationType') === 'In-Country' &&
            ['AFG', 'BGD', 'PAK', 'NGA'].includes(
              form.watch('nationality')
            ) && {
              Agreed: applicationFormSchema.shape.Agreed,
            }),

          ...(values.visaType === 'Work Permit Renewal' && {
            updatedRVCopy: applicationFormSchema.shape.updatedRVCopy,
          }),
          ...((form.watch('nationality') === 'AFG' ||
            form.watch('nationality') === 'IRN' ||
            form.watch('nationality') === 'IRQ' ||
            form.watch('nationality') === 'PAK') && {
            idCardUpload: applicationFormSchema.shape.idCardUpload,
          }),

          ...(!ineligibleNationalities.includes(values.nationality ?? '') &&
            values.visaType !== 'New Employment Visa' &&
            values.visaType !== 'New Investor Visa' &&
            values.visaType !== 'New Partner Visa' && {
              eVisaApplicationType: z
                .string()
                .min(1, { message: 'Select a choice.' }),
            }),

          residantVisaStamping:
            applicationFormSchema.shape.residantVisaStamping,
        })

      case 2:
        return z.object({
          title: applicationFormSchema.shape.title,
          firstName: applicationFormSchema.shape.firstName,
          lastName: applicationFormSchema.shape.lastName,
          emailAddress: applicationFormSchema.shape.emailAddress,
          phone: applicationFormSchema.shape.phone,
          streetAddress1: applicationFormSchema.shape.streetAddress1,
          city: applicationFormSchema.shape.city,
          country1: applicationFormSchema.shape.country1,
          emiratesID: applicationFormSchema.shape.emiratesID,
          ...(values.emiratesID === 'Yes' && {
            emiratesIDCopy: applicationFormSchema.shape.emiratesIDCopy,
          }),
        })

      case 3:
        return z.object({
          nationality1: applicationFormSchema.shape.nationality1,
          arabicName: applicationFormSchema.shape.arabicName,
          passportNumber: applicationFormSchema.shape.passportNumber,
          placeOfIssue: applicationFormSchema.shape.placeOfIssue,
          passportType: applicationFormSchema.shape.passportType,
          agreementPassportRules:
            applicationFormSchema.shape.agreementPassportRules,
          countryOfIssuance: applicationFormSchema.shape.countryOfIssuance,
          coloredPassport: applicationFormSchema.shape.coloredPassport,

          ...(['SYR', 'IND', 'TUR'].includes(values.countryOfIssuance) && {
            coloredPassport2: applicationFormSchema.shape.coloredPassport2,
          }),

          passportIssueDate: applicationFormSchema.shape.passportIssueDate,
          passportExpiryDate: applicationFormSchema.shape.passportExpiryDate,
          cityOfBirth: applicationFormSchema.shape.cityOfBirth,
          countryOfBirth: applicationFormSchema.shape.countryOfBirth,
          dateOfBirth: applicationFormSchema.shape.dateOfBirth,
          gender: applicationFormSchema.shape.gender,
          maritalStatus: applicationFormSchema.shape.maritalStatus,
          religion: applicationFormSchema.shape.religion,
          ...(values.religion === 'Islam' && {
            religionSubCategory:
              applicationFormSchema.shape.religionSubCategory,
          }),
          fatherFullName: applicationFormSchema.shape.fatherFullName,
          motherFullName: applicationFormSchema.shape.motherFullName,
        })

      case 4:
        return z.object({
          employmentDuration: applicationFormSchema.shape.employmentDuration,
          jobTitle: applicationFormSchema.shape.jobTitle,
          employmentStartDate: applicationFormSchema.shape.employmentStartDate,
          probationPeriod: applicationFormSchema.shape.probationPeriod,
          employmentTerminationNotive:
            applicationFormSchema.shape.employmentTerminationNotive,
          educationQualification:
            applicationFormSchema.shape.educationQualification,
          returnTicket: applicationFormSchema.shape.returnTicket,
          ...((form.watch('returnTicket') === 'Economy' ||
            form.watch('returnTicket') === 'Business' ||
            form.watch('returnTicket') === 'First Class') && {
            ticketEntitlementPeriod:
              applicationFormSchema.shape.ticketEntitlementPeriod,
          }),
          annualLeaveEntitlement:
            applicationFormSchema.shape.annualLeaveEntitlement,
          ...(form.watch('annualLeaveEntitlement') === 'Working Days' && {
            workingDays: applicationFormSchema.shape.workingDays,
          }),
          ...(form.watch('annualLeaveEntitlement') === 'Calendar Days' && {
            calendarDays: applicationFormSchema.shape.calendarDays,
          }),
        })

      case 5:
        return z.object({
          basicSalary: applicationFormSchema.shape.basicSalary,
        })

      case 6:
        return z.object({
          companyName: applicationFormSchema.shape.companyName,
          tradeLicenseNumber: applicationFormSchema.shape.tradeLicenseNumber,
          establishmentCardNumber:
            applicationFormSchema.shape.establishmentCardNumber,
          authorizedSignatory: applicationFormSchema.shape.authorizedSignatory,
          emailAddressOfGeneralManager:
            applicationFormSchema.shape.emailAddressOfGeneralManager,
          termsAndConditions: applicationFormSchema.shape.termsAndConditions,
        })

      case 7:
        return z.object({
          photoOfApplicant: applicationFormSchema.shape.photoOfApplicant,
          visaApplicantFiles: applicationFormSchema.shape.visaApplicantFiles,
          preferredPaymentMethod:
            applicationFormSchema.shape.preferredPaymentMethod,
          visaFee: applicationFormSchema.shape.visaFee,
          statusChange: applicationFormSchema.shape.statusChange,
          vipStampingFee: applicationFormSchema.shape.vipStampingFee,
          partnerInvestorVisa: applicationFormSchema.shape.partnerInvestorVisa,
          affirmInformation: applicationFormSchema.shape.affirmInformation,
        })

      default:
        return z.object({})
    }
  }

  // Function to move between steps
  const nextStep = async () => {
    const values = form.getValues()

    // Get the schema for the current step
    const schema = getStepSchema(currentStep, values)

    // Extract the fields required for validation from the schema
    const requiredFields = Object.keys(
      schema.shape
    ) as (keyof ApplicationFormValues)[]

    // Trigger validation for only the required fields
    await form.trigger(requiredFields)

    // Use Zod to validate the values manually and catch detailed errors
    const result = schema.safeParse(values)

    const stepFields = fieldsPerStep[currentStep]
    const currentStepValues = stepFields.reduce((acc, key) => {
      acc[key] = form.getValues(key)
      return acc
    }, {} as Partial<ApplicationFormValues>)

    console.log('Form Data For Step', currentStep, ':', currentStepValues)

    if (!result.success) {
      const issues = result.error.issues

      // Set manual errors for the invalid fields
      issues.forEach((issue) => {
        const field = issue.path[0] as keyof ApplicationFormValues

        if (requiredFields.includes(field)) {
          form.setError(field, {
            type: 'manual',
            message: issue.message,
          })
        }
      })

      console.log(
        'Validation Errors For Step',
        currentStep,
        ':',
        requiredFields.reduce(
          (acc, key) => {
            const error = form.formState.errors[key]
            if (error && typeof error === 'object' && 'message' in error) {
              acc[key] = error
            }
            return acc
          },
          {} as Record<string, unknown>
        )
      )

      toast({
        title: 'Error',
        description: 'Please fill in all the required fields.',
        variant: 'destructive',
      })

      return // Stop if there are validation errors
    }

    // Clear errors and proceed to the next step
    form.clearErrors()
    setStepValues((prev) => ({ ...prev, [currentStep]: values }))

    if (currentStep < 7) {
      setCurrentStep(currentStep + 1)
    }

    console.log(
      'Validation Errors For Step',
      currentStep,
      ': All fields have been filled correctly.'
    )
  }

  const prevStep = () => {
    if (currentStep > 1) {
      const previousStep = currentStep - 1
      const previousValues = stepValues[previousStep] || {}

      form.clearErrors() // clear all errors

      Object.entries(previousValues).forEach(([key, value]) => {
        const currentValue = form.getValues(key as keyof ApplicationFormValues)
        if (currentValue !== value) {
          form.setValue(key as keyof ApplicationFormValues, value, {
            shouldValidate: false,
            shouldTouch: false,
            shouldDirty: false,
          })
        }
      })

      setCurrentStep(previousStep)
    }
  }

  const onSubmit = async (data: ApplicationFormValues) => {
    console.log('Form Data:', data)
    // Get the fields for the current step
    const fieldsToValidate = fieldsPerStep[currentStep]

    // Trigger validation for the fields in the current step
    const isValid = await form.trigger(fieldsToValidate)

    // Log form data for the current step
    if (currentStep === 7) {
      const step7Values = fieldsPerStep[7].reduce((acc, field) => {
        acc[field] = form.getValues(field)
        return acc
      }, {} as Partial<ApplicationFormValues>)

      console.log('Form Data for Step 7 :', step7Values)
    }

    // Check if there are validation errors or if everything is filled correctly
    if (Object.keys(form.formState.errors).length === 0) {
      console.log(
        'Validation Errors For Step',
        currentStep,
        ':',
        'All fields have been filled correctly.'
      )
    } else {
      console.log(
        'Validation Errors For Step',
        currentStep,
        ':',
        form.formState.errors
      )
    }

    if (isValid) {
      // Set loading state to true when submission starts
      setIsLoading(true)
      setIsFormSubmitted(true)

      // Simulate API call with timeout
      setTimeout(() => {
        setIsLoading(false)
        toast({
          title: 'Success!',
          description: 'Your Visa Application has been submitted successfully.',
          variant: 'success',
        })
      }, 1500)
    } else {
      // Handle validation errors
      toast({
        title: 'Error',
        description: 'Please fill in all the required fields.',
        variant: 'destructive',
      })
    }
  }

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        {/* <TopNav links={topNav} /> */}
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-2 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight'>
            Visa Application
          </h1>
          <div className='flex items-center space-x-2'>
            <Button>Download</Button>
          </div>
        </div>
        <ApplicationProgress currentStep={currentStep} type='visa' />

        {/* === Step 1 Content === */}
        {currentStep === 1 && (
          <>
            <Card className='mt-6'>
              <CardHeader>
                <CardTitle>Basic Information ( Page 1 of 7 )</CardTitle>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form
                    // onSubmit={form.handleSubmit(onSubmit)}
                    className='space-y-4 mb-4'
                  >
                    {/* App Updates Email Address field */}
                    <div className='flex flex-wrap space-between gap-y-4'>
                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        <FormField
                          control={form.control}
                          name='appUpdatesEmail'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                App Updates Email Address{' '}
                                <span style={{ color: 'red' }}>*</span>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type='Enter App Updates Email'
                                  {...field}
                                />
                              </FormControl>
                              <FormDescription>
                                Email address where notification updates and
                                issued Visas/communication with regards to the
                                Visa will be sent.
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      {/* App Updates WhatsApp Number */}
                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='whatsAppNumber'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>App Updates WhatsApp Number</FormLabel>
                              <PhoneInput
                                country={'ae'}
                                value={field.value || ''}
                                onChange={(phone) => field.onChange(phone)}
                                containerClass='fz-country-code flex h-9 w-full rounded-tl-[6px] rounded-bl-[6px] border border-input bg-transparent text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm'
                                inputClass=' flex  !h-9 !w-full rounded-tl-[6px] rounded-bl-[6px] border !border-transparent !bg-transparent px-3 text-base !shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm transform translate-y-[-2px]'
                                buttonClass='!h-100 !border-l-transparent !border-t-transparent !bg-slate-200 dark:!bg-slate-800 dark:!border-r-transparent !border-b-transparent !border-gray-200 !bg-transparent !rounded-tl-[8px] !rounded-bl-[6px]'
                                enableSearch={true}
                                searchPlaceholder='Search country...'
                              />
                              <FormDescription>
                                Mobile Number where notification updates and
                                issued Visas/communication with regards to the
                                Visa will be sent.
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                    {/* Application Date */}
                    <div className='flex flex-wrap space-between gap-y-4'>
                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        <FormField
                          control={form.control}
                          name='applicationDate'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Application Date{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <DateTimePicker
                                granularity='day'
                                value={field.value || new Date()}
                                onChange={field.onChange}
                                displayFormat={{
                                  hour24: 'dd MMMM yyyy',
                                }}
                              />
                              <FormDescription>dd-MMM-yyyy</FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      {/* Visa Type field */}
                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='visaType'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Visa Type{' '}
                                <span style={{ color: 'red' }}>*</span>
                              </FormLabel>
                              <FormControl>
                                <Select
                                  {...field}
                                  onValueChange={(value) =>
                                    field.onChange(value)
                                  }
                                  value={field.value}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder='Please Select' />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value='New Employment Visa'>
                                      New Employment Visa
                                    </SelectItem>
                                    <SelectItem value='New Investor Visa'>
                                      New Investor Visa
                                    </SelectItem>
                                    <SelectItem value='New Partner Visa'>
                                      New Partner Visa
                                    </SelectItem>
                                    <SelectItem value='Employment Visa Renewal'>
                                      Employment Visa Renewal
                                    </SelectItem>
                                    <SelectItem value='Work Permit Only'>
                                      Work Permit Only
                                    </SelectItem>
                                    <SelectItem value='Work Permit Renewal'>
                                      Work Permit Renewal
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                              </FormControl>
                              <FormDescription>
                                If you are applying for the first time, please
                                select "New Employment Visa". If you currently
                                have a valid UAE Residence Visa and you wish to
                                apply for the work permit only, please select
                                "Work Permit"
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    {/* Instructions Text */}
                    {form.watch('visaType') === 'Employment Visa Renewal' && (
                      <>
                        <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
                          <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                            <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
                          </span>
                          <div className='flex space-y-2 flex-col ml-3'>
                            <AlertDescription>
                              <p className='text-sm text-slate-800 dark:text-slate-400'>
                                To proceed with a visa renewal application, we
                                required for the License and establishment card
                                of the company to be valid for 90 days and more
                              </p>
                            </AlertDescription>
                          </div>
                        </Alert>
                      </>
                    )}

                    {/* "Do you require a family on hold letter ?" field */}
                    {(form.watch('visaType') === 'New Employment Visa' ||
                      form.watch('visaType') === 'New Investor Visa' ||
                      form.watch('visaType') === 'New Partner Visa') && (
                      <>
                        <div className='mb-4'>
                          <FormField
                            control={form.control}
                            name='familyOnHoldLetter'
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>
                                  Do you require a family on hold letter ?
                                </FormLabel>
                                <FormControl>
                                  <Select
                                    {...field}
                                    onValueChange={(value) =>
                                      field.onChange(value)
                                    }
                                    value={field.value}
                                  >
                                    <SelectTrigger>
                                      <SelectValue placeholder='Select' />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value='Yes'>Yes</SelectItem>
                                      <SelectItem value='No'>No</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                                <FormDescription>
                                  Family on Hold Letter is required when the
                                  applicant has dependents on their current visa
                                  which they want to cancel and apply for a new
                                  visa with IFZA.
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </>
                    )}

                    {/* Trade license must be valid for 90 days or more to proceed with visa application : Checkbox field */}
                    <FormField
                      control={form.control}
                      name='tradeLicenseValidated'
                      render={({ field }) => (
                        <FormItem>
                          <div className='flex items-center'>
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={(checked: boolean) =>
                                  field.onChange(checked)
                                }
                              />
                            </FormControl>
                            <FormLabel className='ml-2'>
                              Trade license must be valid for 60 days or more to
                              proceed with visa application.
                            </FormLabel>
                          </div>

                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Renewal Disclaimer */}
                    {form.watch('visaType') === 'Employment Visa Renewal' && (
                      <>
                        <FormField
                          control={form.control}
                          name='renewalDisclaimer'
                          render={({ field }) => (
                            <FormItem>
                              <div className='flex items-center'>
                                <FormControl>
                                  <Checkbox
                                    checked={field.value}
                                    onCheckedChange={(checked: boolean) =>
                                      field.onChange(checked)
                                    }
                                  />
                                </FormControl>
                                <FormLabel className='ml-2'>
                                  Renewal Disclaimer{' '}
                                  <span style={{ color: 'red' }}>*</span>
                                </FormLabel>
                              </div>
                              <FormDescription>
                                Applicant must be inside country (UAE) to
                                proceed and company license must be valid for
                                more than 90 days.
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        {/* Instructions Text */}
                        <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
                          <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                            <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
                          </span>
                          <div className='flex space-y-2 flex-col ml-3'>
                            <AlertDescription>
                              <p className='text-sm text-slate-800 dark:text-slate-400'>
                                Nationality amendments cannot be processed
                                during visa renewal.
                              </p>
                              <p className='text-sm text-slate-800 dark:text-slate-400'>
                                If a nationality amendment is required, please
                                request once the visa renewal is complete.
                              </p>
                            </AlertDescription>
                          </div>
                        </Alert>
                      </>
                    )}

                    {/* Updated RV copy */}
                    {form.watch('visaType') === 'Work Permit Renewal' && (
                      <>
                        <FormField
                          control={form.control}
                          name='updatedRVCopy'
                          render={() => (
                            <FormItem>
                              <FormLabel>
                                Updated RV copy{' '}
                                <span style={{ color: 'red' }}>*</span>
                              </FormLabel>
                              <FormControl>
                                <FileUploadField
                                  accept='.jpg,.jpeg'
                                  onchoose={(file) => {
                                    form.setValue('updatedRVCopy', file || '')
                                    form.clearErrors('updatedRVCopy')
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </>
                    )}

                    {/* Visa Application Type field */}
                    <div className='mb-4'>
                      <FormField
                        control={form.control}
                        name='visaApplicationType'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Visa Application Type{' '}
                              <span style={{ color: 'red' }}>*</span>
                            </FormLabel>
                            <FormControl>
                              <Select
                                {...field}
                                onValueChange={(value) => field.onChange(value)}
                                value={field.value}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder='Select' />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value='Out of Country'>
                                    Out of Country
                                  </SelectItem>
                                  <SelectItem value='In-Country'>
                                    In-Country
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormDescription>
                              Select the correct application type. Visa is
                              processed based on the information provided. Any
                              wrong/incorrect information may result in delays
                              or may even lead to application rejection.
                              Additional charges may incur.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* "Do you want to apply VFL for this VISA ?" field */}

                    {(form.watch('visaType') === 'New Employment Visa' ||
                      form.watch('visaType') === 'New Investor Visa' ||
                      form.watch('visaType') === 'New Partner Visa') && (
                      <>
                        <div className='mb-4'>
                          <FormField
                            control={form.control}
                            name='visaFree'
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>
                                  Do you want to apply VFL for this VISA ?{' '}
                                  <span style={{ color: 'red' }}>*</span>
                                </FormLabel>
                                <FormControl>
                                  <Select
                                    {...field}
                                    onValueChange={(value) =>
                                      field.onChange(value)
                                    }
                                    value={field.value}
                                  >
                                    <SelectTrigger>
                                      <SelectValue placeholder='Select' />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value='Yes'>Yes</SelectItem>
                                      <SelectItem value='No'>No</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                                <FormDescription>
                                  If you want to apply Free Visa for Life
                                  promotion for this Visa select Yes, select No
                                  if you already applied for Free Visa for Life
                                  with other Visa application.
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </>
                    )}

                    {/*Checkbox : Outside Country Visa Declaration */}
                    {form.watch('visaApplicationType') === 'Out of Country' && (
                      <>
                        <div className='mb-4'>
                          <FormField
                            control={form.control}
                            name='outsideCountry'
                            render={({ field }) => (
                              <FormItem>
                                <div className='flex w-full items-center justify-between'>
                                  <FormLabel className='mr-4 mt-4'>
                                    Outside Country Visa Declaration{' '}
                                    <span style={{ color: 'red' }}>*</span>
                                  </FormLabel>

                                  <div className='flex items-center justify-center flex-1 space-x-2'>
                                    <FormControl>
                                      <Checkbox
                                        checked={field.value}
                                        onCheckedChange={(checked: boolean) =>
                                          field.onChange(checked)
                                        }
                                      />
                                    </FormControl>
                                    <span className='text-sm text-slate-700'>
                                      No Active Visa in UAE
                                    </span>
                                  </div>
                                </div>
                                <FormDescription>
                                  Please confirm that you do not have any active
                                  Tourist or Resident Visas in UAE. Having an
                                  active visa may result in delays or even
                                  rejection of the application.
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </>
                    )}

                    {/* Current Visa Status */}
                    {form.watch('visaApplicationType') === 'In-Country' && (
                      <>
                        <div className='mb-4'>
                          <FormField
                            control={form.control}
                            name='currentVisaStatus'
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>
                                  Current Visa Status{' '}
                                  <span style={{ color: 'red' }}>*</span>
                                </FormLabel>
                                <FormControl>
                                  <Select
                                    {...field}
                                    onValueChange={(value) =>
                                      field.onChange(value)
                                    }
                                    value={field.value}
                                  >
                                    <SelectTrigger>
                                      <SelectValue placeholder='Select' />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value='Tourist'>
                                        Tourist
                                      </SelectItem>
                                      <SelectItem value='Visa on Arrival'>
                                        Visa on Arrival
                                      </SelectItem>
                                      <SelectItem value='Cancelled'>
                                        Cancelled
                                      </SelectItem>
                                      <SelectItem value='Active Residence Visa'>
                                        Active Residence Visa
                                      </SelectItem>
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                                <FormDescription>
                                  Please make sure you do not have any active
                                  Tourist or Residence Visas in UAE. In case
                                  there is an active visa, the application might
                                  be delayed or rejected by the immigration.
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </>
                    )}

                    {/* Nationality of the Applicant */}
                    <div className='mb-4'>
                      <FormField
                        control={form.control}
                        name='nationality'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Nationality of the Applicant{' '}
                              <span style={{ color: 'red' }}>*</span>
                            </FormLabel>
                            <FormControl>
                              <CountryDropdown
                                placeholder='Country'
                                defaultValue={field.value as string} 
                                onChange={(c) => field.onChange(c.alpha3)}
                              />
                            </FormControl>
                            <FormDescription>
                              Please mention the Nationality of the Applicant.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    {/* Instructions Text */}

                    {form.watch('visaType') === 'New Employment Visa' &&
                      form.watch('visaApplicationType') === 'Out of Country' &&
                      (form.watch('nationality') === 'BGD' ||
                        form.watch('nationality') === 'EGY' ||
                        form.watch('nationality') === 'IDN' ||
                        form.watch('nationality') === 'KEN' ||
                        form.watch('nationality') === 'MYS' ||
                        form.watch('nationality') === 'LKA' ||
                        form.watch('nationality') === 'TUN') && (
                        <>
                          <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                            <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                              <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                            </span>
                            <div className='flex flex-col ml-2'>
                              <AlertTitle className=' text-red-500'>
                                Please note :
                              </AlertTitle>

                              <AlertDescription>
                                <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                  For the selected nationality, the visa
                                  applicant may be required to visit the UAE
                                  Embassy/Consulate in their home country, for
                                  the issuance of the e-visa.
                                </p>
                                <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                  If however, the applicant no longer resides in
                                  their home country, they will still be
                                  required to travel to their home country and
                                  visit the UAE Embassy/Consulate.
                                </p>
                                <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                  If they are unable to do so, the application
                                  will need to be withdrawn and the money will
                                  not be refunded.
                                </p>
                              </AlertDescription>
                            </div>
                          </Alert>
                        </>
                      )}

                    {/* Instructions Text */}
                    {form.watch('visaType') === 'Employment Visa Renewal' && (
                      <>
                        <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                          <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                            <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                          </span>
                          <div className='flex flex-col ml-2'>
                            <AlertTitle>Important Note :</AlertTitle>
                            <AlertDescription>
                              <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                Kindly note, e-visa & status change will not be
                                issued during this process.
                              </p>
                              <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                We will only require updated passport sized
                                picture, emirates ID application form and
                                medical fitness test to proceed.
                              </p>
                            </AlertDescription>
                          </div>
                        </Alert>
                      </>
                    )}
                    {/* Show thses when nationality is "Afghanistan" or "Iran" or "Iraq" or "Pakistan"  */}
                    {(form.watch('nationality') === 'AFG' ||
                      form.watch('nationality') === 'IRN' ||
                      form.watch('nationality') === 'IRQ' ||
                      form.watch('nationality') === 'PAK') && (
                      <>
                        <Alert className='border-dashed border-primary mt-4 mb-4 alert-bg-warning dark:bg-primary/40'>
                          <AlertDescription className='text-red-500'>
                            Please note for the selected nationality, the
                            National ID card (Front & Back is required for Visa
                            Processing) Please upload a valid National ID card.
                          </AlertDescription>
                        </Alert>

                        {/* National ID Card upload */}

                        <FormField
                          control={form.control}
                          name='idCardUpload'
                          render={() => (
                            <FormItem>
                              <FormLabel>
                                National ID Card upload{' '}
                                <span style={{ color: 'red' }}>*</span>
                              </FormLabel>
                              <FormControl>
                                <FileUploadField
                                  accept='.pdf,.jpg,.png'
                                  onchoose={(file) => {
                                    form.setValue('idCardUpload', file || '')
                                    form.clearErrors('idCardUpload')
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </>
                    )}
                    {/* Show theses when 'visaApplicationType' is "In-Country" and nationality is "AFG" or "BGD" or "PAK" or "NGA" */}
                    {form.watch('visaApplicationType') === 'In-Country' &&
                      (form.watch('nationality') === 'AFG' ||
                        form.watch('nationality') === 'BGD' ||
                        form.watch('nationality') === 'PAK' ||
                        form.watch('nationality') === 'NGA') && (
                        <>
                          {/* Instructions Text */}
                          <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                            <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                              <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                            </span>
                            <div className='flex flex-col ml-2'>
                              <AlertTitle className='underline text-red-500'>
                                IMPORTANT NOTES :
                              </AlertTitle>

                              <AlertDescription>
                                <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                  Please note for this Nationality, the status
                                  change after the issuance of the Visa may not
                                  be possible. The applicant might have to leave
                                  the country and enter again using the issued
                                  Visa. This is solely at the discretion of the
                                  relevant Govt. Authorities. All charges are
                                  still applicable including the status change
                                  fees.
                                </p>
                                <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                  Alternatively, you can apply for an Outside
                                  the country Visa and the applicant can enter
                                  UAE using the issued Visa.
                                </p>
                              </AlertDescription>
                            </div>
                          </Alert>

                          {/* I agree to the above status change statement and the rules associated : Checkbox */}

                          <div className='mb-4'>
                            <FormField
                              control={form.control}
                              name='Agreed'
                              render={({ field }) => (
                                <FormItem>
                                  <div className='flex w-full items-center justify-between'>
                                    <FormLabel className='mr-4 mt-4'>
                                      I agree to the above status change
                                      statement and the rules associated.{' '}
                                      <span style={{ color: 'red' }}>*</span>
                                    </FormLabel>

                                    <div className='flex items-center justify-center flex-1 space-x-2'>
                                      <FormControl>
                                        <Checkbox
                                          checked={field.value}
                                          onCheckedChange={(checked: boolean) =>
                                            field.onChange(checked)
                                          }
                                        />
                                      </FormControl>
                                      <span className='text-sm text-slate-700'>
                                        Agreed
                                      </span>
                                    </div>
                                  </div>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </>
                      )}

                    {/* E-Visa Application Type */}
                    {showEVisaType && (
                      <>
                        <FormField
                          control={form.control}
                          name='eVisaApplicationType'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                E-Visa Application Type{' '}
                                <span style={{ color: 'red' }}>*</span>
                              </FormLabel>
                              <FormControl>
                                <Select
                                  {...field}
                                  onValueChange={(value) =>
                                    field.onChange(value)
                                  }
                                  value={field.value}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder='Select E-Visa Application Type' />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value='Standard'>
                                      Standard
                                    </SelectItem>
                                    <SelectItem value='VIP'>VIP</SelectItem>
                                  </SelectContent>
                                </Select>
                              </FormControl>
                              <FormDescription>
                                Additional charges apply for the VIP E-Visa
                                issuance. From the time of receiving the
                                completed application and payment confirmation,
                                the E-Visa will be issued within 24 hours. T&Cs
                                Apply.{' '}
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </>
                    )}

                    {/* Resident Visa Stamping Type */}

                    <div className='mb-4'>
                      <FormField
                        control={form.control}
                        name='residantVisaStamping'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Resident Visa Stamping Type{' '}
                              <span style={{ color: 'red' }}>*</span>
                            </FormLabel>
                            <FormControl>
                              <Select
                                {...field}
                                onValueChange={(value) => field.onChange(value)}
                                value={field.value}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder='Select' />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value='Standard'>
                                    Standard
                                  </SelectItem>
                                  <SelectItem value='VIP'>VIP</SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormDescription>
                              After your Visa is approved and you have completed
                              the formalities, Residence Visa is then
                              issued/stamped on your passport. Please ensure
                              that you have one full page on the passport
                              without any stamps.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {(form.watch('residantVisaStamping') === 'VIP' ||
                      form.watch('eVisaApplicationType') === 'VIP') && (
                      <>
                        <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                          <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                            <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                          </span>
                          <div className='flex flex-col ml-2 '>
                            <AlertTitle className='flex flex-col ml-2 font-semibold'>
                              VIP Processing Timeline
                            </AlertTitle>
                            <AlertDescription>
                              <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                The 24-hour processing timeframe begins only
                                upon receipt of all required documents,
                                including proof of payment and a signed
                                employment contract :
                              </p>

                              <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                Received before 11:00 AM: Processed within 24
                                hours.
                              </p>
                              <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                Received after 11:00 AM: 24h timeline to start
                                the next business day at 8:30 AM.
                              </p>
                              <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                Timelines are subject to immigration approval.
                              </p>
                            </AlertDescription>
                          </div>
                        </Alert>

                        {/* Instructions Text */}
                        {form.watch('residantVisaStamping') === 'VIP' && (
                          <>
                            <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
                              <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                                <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
                              </span>
                              <div className='flex space-y-2 flex-col ml-3'>
                                <AlertDescription>
                                  <p className='text-sm text-slate-800 dark:text-slate-400'>
                                    Charges will apply for VIP Stamping. By
                                    selecting the VIP Stamping service you agree
                                    to the charges, these will be shared in the
                                    estimate.
                                  </p>
                                </AlertDescription>
                              </div>
                            </Alert>
                          </>
                        )}
                      </>
                    )}

                    {/* Visa Valid Until : Show this field when 'Visa App Type' is 'In-Country' AND 'Visa Type' IS NOT 'Employment Visa Renewal' OR 'Work Permit Renewal'*/}
                    {form.watch('visaApplicationType') === 'In-Country' &&
                      (form.watch('visaType') === '' ||
                        (form.watch('visaType') !== 'Employment Visa Renewal' &&
                          form.watch('visaType') !==
                            'Work Permit Renewal')) && (
                        <>
                          <div className='mb-4 mt-4'>
                            <FormField
                              control={form.control}
                              name='visaValidUntil'
                              render={() => (
                                <FormItem>
                                  <FormLabel>Visa Valid Until</FormLabel>
                                  <DateTimePicker
                                    granularity='day'
                                    value={date}
                                    onChange={setDate}
                                    displayFormat={{
                                      hour24: 'dd MMMM yyyy',
                                    }}
                                  />
                                  <FormDescription>dd/MM/yyyy</FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </>
                      )}
                  </form>
                </Form>
              </CardContent>
            </Card>
          </>
        )}

        {/* === Step 2 Content === */}
        {currentStep === 2 && (
          <>
            <Card className='mt-6'>
              <CardHeader>
                <CardTitle>Applicant Information ( Page 2 of 7 )</CardTitle>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form className='space-y-4 fz-form'>
                    {/* Title Dropdown */}
                    <FormField
                      control={form.control}
                      name='title'
                      render={() => (
                        <FormItem className='w-full'>
                          <FormLabel>
                            Name in English{' '}
                            <span style={{ color: 'red' }}>*</span>
                          </FormLabel>
                          <div className='flex items-start space-x-2'>
                            {/* Title */}
                            <div className='w-[120px]'>
                              <FormField
                                control={form.control}
                                name='title'
                                render={({ field }) => (
                                  <FormItem className='w-full'>
                                    <FormControl>
                                      <Select
                                        onValueChange={field.onChange}
                                        defaultValue={field.value}
                                      >
                                        <SelectTrigger className='w-[120px]'>
                                          <SelectValue placeholder='Select Title' />
                                        </SelectTrigger>
                                        <SelectContent>
                                          <SelectItem value='Mr.'>
                                            Mr.
                                          </SelectItem>
                                          <SelectItem value='Mrs.'>
                                            Mrs.
                                          </SelectItem>
                                          <SelectItem value='Ms.'>
                                            Ms.
                                          </SelectItem>
                                        </SelectContent>
                                      </Select>
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>

                            {/* First Name */}
                            <div className='w-full'>
                              <FormField
                                control={form.control}
                                name='firstName'
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Input
                                        {...field}
                                        placeholder='First Name in English'
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>

                            {/* Middle Name */}
                            <div className='w-full'>
                              <FormField
                                control={form.control}
                                name='middleName'
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Input
                                        {...field}
                                        placeholder='Middle Name in English'
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>

                            {/* Last Name */}
                            <div className='w-full'>
                              <FormField
                                control={form.control}
                                name='lastName'
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Input
                                        {...field}
                                        placeholder='Last Name in English'
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>
                        </FormItem>
                      )}
                    />

                    <FormDescription>
                      The name should be exactly as per the passport. Other
                      version of the name which are not matching the passport
                      not be accepted and may result in application rejection
                      and/or delays.
                    </FormDescription>

                    {/* Instructions Text : Show this when 'Nationality of the Applicant' is 'Bangladesh' */}

                    {form.watch('nationality') === 'BGD' && (
                      <>
                        <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                          <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                            <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                          </span>
                          <div className='flex flex-col ml-2'>
                            <AlertTitle className=' text-red-500'>
                              Please follow the below guideline for filling in
                              the applicant's name :
                            </AlertTitle>

                            <AlertDescription>
                              <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                First name, will be the given name, as per the
                                passport
                              </p>
                              <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                Middle name, will be the surname, as per the
                                passport
                              </p>
                              <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                Last name, will be the name of the father (first
                                page), as per the passport
                              </p>
                            </AlertDescription>
                          </div>
                        </Alert>
                      </>
                    )}

                    {/* Instructions Text : Show this when 'Nationality of the Applicant' is 'Pakistan' */}

                    {form.watch('nationality') === 'PAK' && (
                      <>
                        <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                          <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                            <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                          </span>
                          <div className='flex flex-col ml-2'>
                            <AlertTitle className=' text-red-500'>
                              Please follow the below guideline for filling in
                              the applicant's name :
                            </AlertTitle>

                            <AlertDescription>
                              <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                First name, will be the given name, as per the
                                passport
                              </p>
                              <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                Middle name, will be the surname, as per the
                                passport
                              </p>
                              <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                Last name, will be the name of the
                                father/husband, as per the passport
                              </p>
                            </AlertDescription>
                          </div>
                        </Alert>
                      </>
                    )}

                    {/* Instructions Text : Show this when 'Nationality of the Applicant' is 'India' */}

                    {form.watch('nationality') === 'IND' && (
                      <>
                        <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                          <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                            <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                          </span>
                          <div className='flex flex-col ml-2'>
                            <AlertTitle className=' text-red-500'>
                              Please follow the below guideline for filling in
                              the applicant's name :
                            </AlertTitle>

                            <AlertDescription>
                              <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                First name, will be the given name, as per the
                                passport
                              </p>
                              <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                Middle name, will be the surname, as per the
                                passport
                              </p>
                              <p className="text-sm dark:text-red-500 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                                Last name, will be the name of the father (last
                                page), as per the passport
                              </p>
                            </AlertDescription>
                          </div>
                        </Alert>
                      </>
                    )}
                    {/* Name In Arabic Section */}

                    <FormField
                      control={form.control}
                      name='title1'
                      render={() => (
                        <FormItem className='w-full'>
                          <FormLabel>Name in Arabic</FormLabel>
                          <div className='flex items-start space-x-2'>
                            {/* Title in Arabic */}
                            <div className='w-[120px]'>
                              <FormField
                                control={form.control}
                                name='title1'
                                render={({ field }) => (
                                  <FormItem className='w-full'>
                                    <FormControl>
                                      <Select
                                        onValueChange={field.onChange}
                                        defaultValue={field.value}
                                      >
                                        <SelectTrigger className='w-[120px]'>
                                          <SelectValue placeholder='Select Title' />
                                        </SelectTrigger>
                                        <SelectContent>
                                          <SelectItem value='Mr.'>
                                            Mr.
                                          </SelectItem>
                                          <SelectItem value='Mrs.'>
                                            Mrs.
                                          </SelectItem>
                                          <SelectItem value='Ms.'>
                                            Ms.
                                          </SelectItem>
                                        </SelectContent>
                                      </Select>
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>

                            {/* First Name Arabic */}
                            <div className='w-full'>
                              <FormField
                                control={form.control}
                                name='firstNameArabic'
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Input
                                        {...field}
                                        placeholder='First Name in Arabic'
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>

                            {/* Middle Name Arabic */}
                            <div className='w-full'>
                              <FormField
                                control={form.control}
                                name='middleNameArabic'
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Input
                                        {...field}
                                        placeholder='Middle Name in Arabic'
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>

                            {/* Last Name Arabic */}
                            <div className='w-full'>
                              <FormField
                                control={form.control}
                                name='lastNameArabic'
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Input
                                        {...field}
                                        placeholder='Last Name in Arabic'
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>
                        </FormItem>
                      )}
                    />

                    <FormDescription>
                      The name should be exactly as per the passport. Other
                      version of the name which are not matching the passport
                      not be accepted and may result in application rejection
                      and/or delays.
                    </FormDescription>

                    <div className='flex flex-wrap space-between gap-y-4'>
                      {/* Email Address */}
                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        <FormField
                          control={form.control}
                          name='emailAddress'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Email Address{' '}
                                <span style={{ color: 'red' }}>*</span>
                              </FormLabel>
                              <FormControl>
                                <Input type='Enter Email Address' {...field} />
                              </FormControl>
                              <FormDescription>
                                Email address of the visa applicant
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Phone */}

                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='phone'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Phone <span className='text-red-500'>*</span>
                              </FormLabel>
                              <PhoneInput
                                country={'ae'}
                                value={field.value.toString()}
                                onChange={(phone) => field.onChange(phone)}
                                containerClass='fz-country-code flex h-9 w-full rounded-tl-[6px] rounded-bl-[6px] border border-input bg-transparent text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm'
                                inputClass=' flex  !h-9 !w-full rounded-tl-[6px] rounded-bl-[6px] border !border-transparent !bg-transparent px-3 text-base !shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm transform translate-y-[-2px]'
                                buttonClass='!h-100 !border-l-transparent !border-t-transparent !bg-slate-200 dark:!bg-slate-800 dark:!border-r-transparent !border-b-transparent !border-gray-200 !bg-transparent !rounded-tl-[8px] !rounded-bl-[6px]'
                                enableSearch={true}
                                searchPlaceholder='Search country...'
                              />
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    {/* UAE Address */}

                    <FormField
                      control={form.control}
                      name='streetAddress'
                      render={() => (
                        <FormItem className='w-full mt-4'>
                          <FormLabel>UAE Address (if Applicable)</FormLabel>

                          <div className='flex items-start space-x-2'>
                            {/* Street Address Field */}
                            <div className='w-full'>
                              <FormField
                                control={form.control}
                                name='streetAddress'
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Input
                                        {...field}
                                        placeholder='Enter Street Address'
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>

                            {/* Address Line 2 Input Field */}
                            <div className='w-full'>
                              <FormField
                                control={form.control}
                                name='addressLine'
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Input
                                        {...field}
                                        placeholder='Enter Address Line 2'
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>

                            {/* City Input Field */}
                            <div className='w-full'>
                              <FormField
                                control={form.control}
                                name='cityAddress'
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Input
                                        {...field}
                                        placeholder='Enter City '
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>

                            {/* State/Region/Province Input Field */}
                            <div className='w-full'>
                              <FormField
                                control={form.control}
                                name='stateProvince'
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Input
                                        {...field}
                                        placeholder='Enter State/Region/Province'
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>

                            {/* Country Field */}
                            <div className='w-full'>
                              <FormField
                                control={form.control}
                                name='country'
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <CountryDropdown
                                        placeholder='Country'
                                        defaultValue={field.value as string} 
                                        onChange={(c) => field.onChange(c.alpha3)}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>
                        </FormItem>
                      )}
                    />

                    {/* Home Country Address */}

                    <FormField
                      control={form.control}
                      name='streetAddress1'
                      render={() => (
                        <FormItem className='w-full mt-4'>
                          <FormLabel>
                            Home Country Address{' '}
                            <span style={{ color: 'red' }}>*</span>
                          </FormLabel>

                          <div className='flex items-start space-x-2'>
                            {/* Street Address Field */}
                            <div className='w-full'>
                              <FormField
                                control={form.control}
                                name='streetAddress1'
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Input
                                        {...field}
                                        placeholder='Enter Street Address'
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                            {/* Address Line 2 Input Field */}
                            <div className='w-full'>
                              <FormField
                                control={form.control}
                                name='addressLine2'
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Input
                                        {...field}
                                        placeholder='Enter Address Line 2'
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>

                            {/* City Input Field */}
                            <div className='w-full'>
                              <FormField
                                control={form.control}
                                name='city'
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Input
                                        {...field}
                                        placeholder='Enter City'
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>

                            {/* State/Region/Province Input Field */}
                            <div className='w-full'>
                              <FormField
                                control={form.control}
                                name='province'
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Input
                                        {...field}
                                        placeholder='Enter State/Region/Province'
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>

                            {/* Country Field */}
                            <div className='w-full'>
                              <FormField
                                control={form.control}
                                name='country1'
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <CountryDropdown
                                        placeholder='Country'
                                        defaultValue={field.value as string} 
                                        onChange={(c) => field.onChange(c.alpha3)}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>
                        </FormItem>
                      )}
                    />

                    {/* Do you have a previous Emirates ID */}

                    <FormField
                      control={form.control}
                      name='emiratesID'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Do you have a previous Emirates ID{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Select
                              {...field}
                              onValueChange={(value) => field.onChange(value)}
                              value={field.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder='Select' />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value='Yes'>Yes</SelectItem>
                                <SelectItem value='No'>No</SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Do you have a previous Emirates ID : IF 'YES' */}

                    {form.watch('emiratesID') === 'Yes' && (
                      <>
                        <div className='flex flex-wrap space-between gap-y-4'>
                          {/* Emirates ID Number */}
                          <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                            <FormField
                              control={form.control}
                              name='emiratesIDNumber'
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Emirates ID Number</FormLabel>
                                  <FormControl>
                                    <Input {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          {/* Emirates ID Expiry Date */}
                          <div className='flex-1'>
                            <FormField
                              control={form.control}
                              name='emiratesIDExpiryDate'
                              render={() => (
                                <FormItem>
                                  <FormLabel>Emirates ID Expiry Date</FormLabel>
                                  <DateTimePicker
                                    granularity='day'
                                    value={date}
                                    onChange={setDate}
                                    displayFormat={{
                                      hour24: 'dd MMMM yyyy',
                                    }}
                                  />
                                  <FormDescription>dd/MM/yyyy</FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>

                        {/* Emirates ID Copy */}

                        <FormField
                          control={form.control}
                          name='emiratesIDCopy'
                          render={() => (
                            <FormItem>
                              <FormLabel>
                                Emirates ID Copy{' '}
                                <span style={{ color: 'red' }}>*</span>
                              </FormLabel>
                              <FormControl>
                                <FileUploadField
                                  accept='.pdf,.jpg,.png'
                                  onchoose={(file) => {
                                    form.setValue('emiratesIDCopy', file || '')
                                    form.clearErrors('emiratesIDCopy')
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </>
                    )}
                  </form>
                </Form>
              </CardContent>
            </Card>
          </>
        )}

        {/* === Step 3 Content === */}
        {currentStep === 3 && (
          <>
            <Card className='mt-6'>
              <CardHeader>
                <CardTitle>Passport Information ( Page 3 of 7 )</CardTitle>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form className='space-y-4 fz-form'>
                    <div className='flex flex-wrap space-between gap-y-4'>
                      {/* Nationality */}
                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        <FormField
                          control={form.control}
                          name='nationality1'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Nationality{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <CountryDropdown
                                  placeholder='Country'
                                  defaultValue={field.value as string} 
                                  onChange={(c) => field.onChange(c.alpha3)}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      {/* Do you have Arabic Name in your Passport? : Dropdown */}
                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='arabicName'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Do you have Arabic Name in your Passport ?{' '}
                                <span style={{ color: 'red' }}>*</span>
                              </FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder='Select ' />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value='Yes'>Yes</SelectItem>
                                  <SelectItem value='No'>No</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                    {/* Passport Number */}

                    <FormField
                      control={form.control}
                      name='passportNumber'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Passport Number{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder='Enter Passport Number'
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Place of Issue */}

                    <div className='flex flex-wrap space-between gap-y-4'>
                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        <FormField
                          control={form.control}
                          name='placeOfIssue'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Place of Issue{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  placeholder='Enter Place of Issue'
                                />
                              </FormControl>
                              <FormDescription>
                                Issuing place of the passport
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Place of Issue (Arabic)*/}
                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='placeOfIssueArabic'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Place of Issue (Arabic)</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder='Enter' />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    {/* Passport Type */}

                    <FormField
                      control={form.control}
                      name='passportType'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Passport Type{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Select
                              {...field}
                              onValueChange={(value) => field.onChange(value)}
                              value={field.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder='Select' />
                              </SelectTrigger>

                              <SelectContent>
                                <SelectItem value='Ordinary'>
                                  Ordinary
                                </SelectItem>
                                {(nationality === 'SYR' ||
                                  nationality === 'LBN') && (
                                  <SelectItem value='Travel Document'>
                                    Travel Document
                                  </SelectItem>
                                )}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormDescription>
                            Please note only Ordinary Passports are accepted for
                            Visas, it is not allowed to apply for Visas on
                            Diplomatic passports.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle className=' text-red-500'>
                          You agree to the statement below by checking the box :
                        </AlertTitle>

                        <AlertDescription>
                          <p className="text-sm dark:text-red-500 pl-4 relative before:content-['1.'] before:absolute before:left-0 before:top-0">
                            The passport provided is a normal/ordinary passport.
                          </p>
                          <p className="text-sm dark:text-red-500 pl-4 relative before:content-['2.'] before:absolute before:left-0 before:top-0">
                            It is not a travel document, provisional, and/or
                            diplomatic passport.
                          </p>
                          <p className="text-sm dark:text-red-500 pl-4 relative before:content-['3.'] before:absolute before:left-0 before:top-0">
                            It is the responsibility of the partner/client to
                            ensure that the correct passport is provided at the
                            time of application.
                          </p>
                          <p className="text-sm dark:text-red-500 pl-4 relative before:content-['4.'] before:absolute before:left-0 before:top-0">
                            Any Visa(s) delayed or rejected because of not
                            complying to the statements above will be the sole
                            responsibility of the partner/client.{' '}
                          </p>
                        </AlertDescription>
                        <p className="text-sm dark:text-red-500 pl-4 relative before:content-['5.'] before:absolute before:left-0 before:top-0">
                          No refunds/provisions will be possible in case of
                          non-compliance.{' '}
                        </p>
                      </div>
                    </Alert>

                    {/* Agreement to passport rules */}

                    <FormField
                      control={form.control}
                      name='agreementPassportRules'
                      render={({ field }) => (
                        <FormItem>
                          <div className='flex w-full items-center justify-between'>
                            <FormLabel className='mr-4'>
                              Agreement to passport rules{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>

                            <div className='flex items-center justify-center flex-1 space-x-2'>
                              <FormControl>
                                <Checkbox
                                  checked={field.value}
                                  onCheckedChange={(checked: boolean) =>
                                    field.onChange(checked)
                                  }
                                />
                              </FormControl>
                              <span className='text-sm text-slate-700'>
                                Agreed
                              </span>
                            </div>
                          </div>

                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className='flex flex-wrap space-between gap-y-4'>
                      {/* Country of Issuance */}
                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        <FormField
                          control={form.control}
                          name='countryOfIssuance'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Country of Issuance{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <CountryDropdown
                                  placeholder='Country'
                                  defaultValue={field.value as string} 
                                  onChange={(c) => field.onChange(c.alpha3)}
                                />
                              </FormControl>
                              <FormDescription>
                                Issuing country of the passport
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Colored Passport Copy Page 1 */}
                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='coloredPassport'
                          render={() => (
                            <FormItem>
                              <FormLabel>
                                Colored Passport Copy Page 1{' '}
                                <span style={{ color: 'red' }}>*</span>
                              </FormLabel>
                              <FormControl>
                                <FileUploadField
                                  accept='.jpg,.jpeg'
                                  onchoose={(file) => {
                                    form.setValue('coloredPassport', file || '')
                                    form.clearErrors('coloredPassport')
                                  }}
                                />
                              </FormControl>
                              <FormDescription>
                                Please upload page 1 of your passport. We accept
                                only image/photo in the format of .jpg OR .jpeg
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                    {/* Colored Passport Copy Page 2 : Show this field when 'Country of Issuance' is 'Syria OR India OR Turkey' */}
                    {(form.watch('countryOfIssuance') === 'SYR' ||
                      form.watch('countryOfIssuance') === 'IND' ||
                      form.watch('countryOfIssuance') === 'TUR') && (
                      <>
                        <FormField
                          control={form.control}
                          name='coloredPassport2'
                          render={() => (
                            <FormItem>
                              <FormLabel>
                                Colored Passport Copy Page 2{' '}
                                <span style={{ color: 'red' }}>*</span>
                              </FormLabel>
                              <FormControl>
                                <FileUploadField
                                  accept='.jpg,.jpeg'
                                  onchoose={(file) => {
                                    form.setValue(
                                      'coloredPassport2',
                                      file || ''
                                    )
                                    form.clearErrors('coloredPassport2')
                                  }}
                                />
                              </FormControl>
                              <FormDescription>
                                Please upload page 2 of your passport. We accept
                                only image/photo in the format of .jpg OR .jpeg
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </>
                    )}

                    <div className='flex flex-wrap space-between gap-y-4'>
                      {/* Passport Issue Date */}
                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        <FormField
                          control={form.control}
                          name='passportIssueDate'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Passport Issue Date{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <DateTimePicker
                                granularity='day'
                                value={field.value}
                                onChange={(date) => {
                                  field.onChange(date)
                                  setDate(date)
                                }}
                                displayFormat={{
                                  hour24: 'dd MMMM yyyy',
                                }}
                              />
                              <FormDescription>dd/MM/yyyy</FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Passport Expiry Date */}

                      <div className='flex-1'>
                        {' '}
                        <FormField
                          control={form.control}
                          name='passportExpiryDate'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                {' '}
                                Passport Expiry Date{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <DateTimePicker
                                granularity='day'
                                value={field.value}
                                onChange={(date) => {
                                  field.onChange(date)
                                  setDate(date)
                                }}
                                displayFormat={{
                                  hour24: 'dd MMMM yyyy',
                                }}
                              />
                              <FormMessage />
                              <FormDescription>
                                dd/MM/yyyy <br />
                                Please note that your passport should be valid
                                for 7 months plus at the time of application.
                                The Visa stamping process may not be completed
                                otherwise.
                              </FormDescription>
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                    <div className='flex flex-wrap space-between gap-y-4'>
                      {/* City of Birth */}

                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        {' '}
                        <FormField
                          control={form.control}
                          name='cityOfBirth'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                City of Birth{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  placeholder='Enter City of Birth'
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* City of Birth (Arabic) */}

                      <div className='flex-1'>
                        {' '}
                        <FormField
                          control={form.control}
                          name='cityOfBirthArabic'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>City of Birth (Arabic)</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder='Enter' />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                    <div className='flex flex-wrap space-between gap-y-4'>
                      {/* Country of Birth */}
                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        {' '}
                        <FormField
                          control={form.control}
                          name='countryOfBirth'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Country of Birth{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <CountryDropdown
                                  placeholder='Country'
                                  defaultValue={field.value as string} 
                                  onChange={(c) => field.onChange(c.alpha3)}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Date of Birth */}

                      <div className='flex-1'>
                        {' '}
                        <FormField
                          control={form.control}
                          name='dateOfBirth'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                {' '}
                                Date of Birth{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <DateTimePicker
                                granularity='day'
                                value={field.value}
                                onChange={(date) => {
                                  field.onChange(date)
                                  setDate(date)
                                }}
                                displayFormat={{
                                  hour24: 'dd MMMM yyyy',
                                }}
                              />
                              <FormDescription>dd/MM/yyyy</FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                    <div className='flex flex-wrap space-between gap-y-4'>
                      {/* Gender */}

                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        {' '}
                        <FormField
                          control={form.control}
                          name='gender'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Gender <span className='text-red-500'>*</span>
                              </FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder='Select gender' />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value='Male'>Male</SelectItem>
                                  <SelectItem value='Female'>Female</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Previous Nationality */}

                      <div className='flex-1'>
                        {' '}
                        <FormField
                          control={form.control}
                          name='previousNationality'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                {' '}
                                Previous Nationality (if any)
                              </FormLabel>
                              <FormControl>
                                <Input {...field} placeholder='Enter' />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                    <div className='flex flex-wrap space-between gap-y-4'>
                      {/* Marital Status */}

                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        {' '}
                        <FormField
                          control={form.control}
                          name='maritalStatus'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Marital Status{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder='Select ' />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value='Married'>
                                    Married
                                  </SelectItem>
                                  <SelectItem value='Single'>Single</SelectItem>
                                  <SelectItem value='Divorced'>
                                    Divorced
                                  </SelectItem>
                                  <SelectItem value='Widowed'>
                                    Widowed
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Religion */}
                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='religion'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Religion <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder='Select Religion' />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value='Islam'>Islam</SelectItem>
                                    <SelectItem value='Christianity'>
                                      Christianity
                                    </SelectItem>
                                    <SelectItem value='Hinduism'>
                                      Hinduism
                                    </SelectItem>
                                    <SelectItem value='Buddhism'>
                                      Buddhism
                                    </SelectItem>
                                    <SelectItem value='Sikhism'>
                                      Sikhism
                                    </SelectItem>
                                    <SelectItem value='Judaism'>
                                      Judaism
                                    </SelectItem>
                                    <SelectItem value='Bahaei'>
                                      Bahaei
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                    {form.watch('religion') === 'Islam' && (
                      <>
                        <FormField
                          control={form.control}
                          name='religionSubCategory'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Religion Sub-Category{' '}
                                <span style={{ color: 'red' }}>*</span>
                              </FormLabel>
                              <FormControl>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder='Select ' />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value='Sunni'>Sunni</SelectItem>
                                    <SelectItem value='Shia'>Shia</SelectItem>
                                  </SelectContent>
                                </Select>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </>
                    )}

                    {/* Father's Full Name */}

                    <FormField
                      control={form.control}
                      name='fatherFullName'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Father's Full Name{' '}
                            <span style={{ color: 'red' }}>*</span>
                          </FormLabel>
                          <FormControl>
                            <Input {...field} placeholder='Enter' />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className='flex flex-wrap space-between gap-y-4'>
                      {/* Mother's Full Name */}

                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        <FormField
                          control={form.control}
                          name='motherFullName'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Mother's Full Name{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Input {...field} placeholder='Enter' />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Mother's Full Name (Arabic) */}

                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='motherFullNameArabic'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Mother's Full Name (Arabic)</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder='Enter' />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </>
        )}

        {/* === Step 4 Content === */}
        {currentStep === 4 && (
          <>
            <Card className='mt-6'>
              <CardHeader>
                <CardTitle>Employment Information ( Page 4 of 7 )</CardTitle>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form className='space-y-4 fz-form'>
                    <div className='flex flex-wrap space-between gap-y-4'>
                      {/* Type of Employment */}
                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        <FormField
                          control={form.control}
                          name='typeOfEmployment'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Type of Employment{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Input {...field} placeholder='' disabled />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      {/* Employment Duration in Months */}
                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='employmentDuration'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Employment Duration in Months{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  type='number'
                                  placeholder='Enter Employment Duration in Months'
                                  onChange={(e) => {
                                    const value =
                                      e.target.value === ''
                                        ? undefined
                                        : +e.target.value
                                    field.onChange(value)
                                  }}
                                />
                              </FormControl>

                              <FormDescription>
                                Maximum of 24 Months/2 Years
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    {/* Do you want to change the Job title of the Visa holder? */}
                    {form.watch('visaType') === 'Employment Visa Renewal' && (
                      <>
                        <FormField
                          control={form.control}
                          name='jobTitleChange'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Do you want to change the Job title of the Visa
                                holder ?
                              </FormLabel>
                              <FormControl>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder='Choose' />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value='Yes'>Yes</SelectItem>
                                    <SelectItem value='No'>No</SelectItem>
                                  </SelectContent>
                                </Select>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </>
                    )}

                    <div className='flex flex-wrap space-between gap-y-4'>
                      {/* Job Title */}
                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        <FormField
                          control={form.control}
                          name='jobTitle'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Job Title{' '}
                                <span style={{ color: 'red' }}>*</span>
                              </FormLabel>
                              <FormControl>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder='Select' />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem
                                      value='no-attested-degree'
                                      disabled
                                      style={{
                                        fontWeight: 'bold',
                                        color: 'red',
                                      }}
                                    >
                                      No Attested Degree Required
                                    </SelectItem>

                                    {[
                                      'Administrative Assistant',
                                      'Security Staff',
                                      'Public Relations Clerk',
                                      'Customer Service Representative',
                                      'Tailor General',
                                      'General Helper',
                                      'Sales Agent',
                                      'Site Supervisor',
                                      'Customs Clearance Agent',
                                      'Photographer',
                                      'Sales',
                                      'Senior Sales Executive',
                                      'Driver',
                                      'Film Developer',
                                      'Curtains Installation Worker',
                                      'Aircraft Field Service Mechanic',
                                      'Marketing Executive',
                                      'Technical Assistant',
                                      'Light Vehicle Mechanic General',
                                      'Messenger',
                                      'Archive Clerk',
                                      'Clerk Assistant',
                                      'Company Clerk',
                                      'Supervisor',
                                      'General Manager',
                                      'Investor',
                                      'Marketing Assistant',
                                      'Partner',
                                      'Reception Officer',
                                      'Receptionist',
                                      'Sales Representative',
                                      'Secretary',
                                      'Cook',
                                      'Executive Assistant',
                                      'Cleaner General',
                                      'Office Clerks General',
                                      'Petroleum Product Sales Gen',
                                      'Heavy Truck Driver',
                                    ].map((title) => (
                                      <SelectItem key={title} value={title}>
                                        {title}
                                      </SelectItem>
                                    ))}

                                    <SelectItem
                                      value='attested-degree'
                                      disabled
                                      style={{
                                        fontWeight: 'bold',
                                        color: 'red',
                                      }}
                                    >
                                      Attested Degree Required
                                    </SelectItem>

                                    {[
                                      'Administrative Director',
                                      'Credit Officer',
                                      'Financial Officer',
                                      'Administrative Officer',
                                      'Medical Sales Representative',
                                      'Legal Advisor',
                                      'General Geologist',
                                      'Financial & Administrative Director',
                                      'Administration Directors',
                                      'Operations Analyst',
                                      'Graphic Designer',
                                      'Finance Department Assistant',
                                      'Senior Engineer',
                                      'Trader',
                                      'Director of Sales and Marketing',
                                      'Information Technology officer',
                                      'Marketing Specialist',
                                      'Marketing Manager',
                                      'Security Manager',
                                      'Pilot',
                                      'Chief Accountant',
                                      'Head Legal Affairs Section',
                                      'Investment Controller',
                                      'Investment Manager',
                                      'Interior Designer',
                                      'Legal Affairs Manager',
                                      'Technical Manager',
                                      'Business Development Manager',
                                      'Security Officer',
                                      'Technician',
                                      'Law Professor',
                                      'Manager',
                                      'Accountant',
                                      'Managing Director',
                                      'Assistant Manager',
                                      'Sales Supervisor',
                                      'Sales Officer',
                                      'Administrative Supervisor',
                                      'Assistant Managing Director',
                                      'Human Resource Director',
                                      'Human Resource Manager',
                                      'HR Manager',
                                      'Office Manager',
                                      'Operations Manager',
                                      'Sales Manager',
                                      'Vice President',
                                      'Web Developer',
                                      'Software Specialist',
                                      'Finance Manager',
                                      'Engineering Manager',
                                      'Electronics Engineer',
                                      'Chairman of the board',
                                      'Senior Officer of Legal Affairs',
                                      'Civil Engineer/Airports',
                                      'Director/Cinema And Tv Films',
                                      'Cinema Director',
                                      'Administrative Advisor',
                                      'Consultant Engineer',
                                      'Teacher',
                                      'Projects Manager',
                                      'Electrical Engineer',
                                    ].map((title) => (
                                      <SelectItem key={title} value={title}>
                                        {title}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </FormControl>
                              <FormDescription>
                                The title which will appear on the residence
                                visa stamped on your passport. Please note these
                                titles are subject to change ay any point during
                                the Visa process. Please let us know if you have
                                any questions.
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Employment Start Date */}
                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='employmentStartDate'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Employment Start Date{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <DateTimePicker
                                granularity='day'
                                value={field.value}
                                onChange={(date) => {
                                  field.onChange(date)
                                  setDate(date)
                                }}
                                displayFormat={{
                                  hour24: 'dd MMMM yyyy',
                                }}
                              />
                              <FormDescription>dd/MM/yyyy</FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                    <div className='flex flex-wrap space-between gap-y-4'>
                      {/* Probation Period : Drop down*/}
                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        <FormField
                          control={form.control}
                          name='probationPeriod'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Probation Period{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder='Select Probation Period' />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value='O Months'>
                                      O Months
                                    </SelectItem>
                                    <SelectItem value='1 Month'>
                                      1 Month
                                    </SelectItem>
                                    <SelectItem value='3 Months'>
                                      3 Months
                                    </SelectItem>
                                    <SelectItem value='6 Months'>
                                      6 Months
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      {/* Employment Termination Notice */}
                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='employmentTerminationNotive'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Employment Termination Notice{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder='Select Employment Termination Notice' />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value='1 Month'>
                                      1 Month
                                    </SelectItem>
                                    <SelectItem value='2 Months'>
                                      2 Months
                                    </SelectItem>
                                    <SelectItem value='3 Months'>
                                      3 Months
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    <div className='flex flex-wrap space-between gap-y-4'>
                      {/* Educational Qualification */}
                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        <FormField
                          control={form.control}
                          name='educationQualification'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Educational Qualification{' '}
                                <span style={{ color: 'red' }}>*</span>
                              </FormLabel>
                              <FormControl>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder='Choose' />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value='No formal education'>
                                      No formal education
                                    </SelectItem>
                                    <SelectItem value='Primary'>
                                      Primary
                                    </SelectItem>
                                    <SelectItem value='High School'>
                                      High School
                                    </SelectItem>
                                    <SelectItem value='Vocational'>
                                      Vocational
                                    </SelectItem>
                                    <SelectItem value="Bachelor's Degree">
                                      Bachelor's Degree
                                    </SelectItem>
                                    <SelectItem value="Master's Degree">
                                      Master's Degree
                                    </SelectItem>
                                    <SelectItem value='Doctorate'>
                                      Doctorate
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Return Ticket Eligibility */}
                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='returnTicket'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Return Ticket Eligibility{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder='Choose' />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value='Economy'>
                                      Economy
                                    </SelectItem>
                                    <SelectItem value='Business'>
                                      Business
                                    </SelectItem>
                                    <SelectItem value='First Class'>
                                      First Class
                                    </SelectItem>
                                    <SelectItem value='No Entitlement'>
                                      No Entitlement
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                    {/* Ticket Entitlement Period */}

                    {(form.watch('returnTicket') === 'Economy' ||
                      form.watch('returnTicket') === 'Business' ||
                      form.watch('returnTicket') === 'First Class') && (
                      <>
                        <FormField
                          control={form.control}
                          name='ticketEntitlementPeriod'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Ticket Entitlement Period{' '}
                                <span style={{ color: 'red' }}>*</span>
                              </FormLabel>
                              <FormControl>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder='Choose' />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value='1 Year'>
                                      1 Year
                                    </SelectItem>
                                    <SelectItem value='2 Years'>
                                      2 Years
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </>
                    )}

                    {/* Annual Leave Entitlement */}

                    <FormField
                      control={form.control}
                      name='annualLeaveEntitlement'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Annual Leave Entitlement{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder='Choose' />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value='Working Days'>
                                  Working Days
                                </SelectItem>
                                <SelectItem value='Calendar Days'>
                                  Calendar Days
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Number of Leave Days (Working Days) */}
                    {form.watch('annualLeaveEntitlement') ===
                      'Working Days' && (
                      <>
                        <FormField
                          control={form.control}
                          name='workingDays'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Number of Leave Days (Working Days){' '}
                                <span style={{ color: 'red' }}>*</span>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  type='number'
                                  placeholder='Enter '
                                  onChange={(e) => {
                                    const value =
                                      e.target.value === ''
                                        ? undefined
                                        : +e.target.value
                                    field.onChange(value)
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </>
                    )}

                    {/* Number of Leave Days (Calendar Days) */}
                    {form.watch('annualLeaveEntitlement') ===
                      'Calendar Days' && (
                      <>
                        <FormField
                          control={form.control}
                          name='calendarDays'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Number of Leave Days (Calendar Days){' '}
                                <span style={{ color: 'red' }}>*</span>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  type='number'
                                  placeholder='Enter '
                                  onChange={(e) => {
                                    const value =
                                      e.target.value === ''
                                        ? undefined
                                        : +e.target.value
                                    field.onChange(value)
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </>
                    )}
                  </form>
                </Form>
              </CardContent>
            </Card>
          </>
        )}

        {/* === Step 5 Content === */}
        {currentStep === 5 && (
          <>
            <Card className='mt-6'>
              <CardHeader>
                <CardTitle>
                  Monthly Salary Breakdown (AED) ( Page 5 of 7 )
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form className='space-y-4 fz-form'>
                    {/* Do you want to change the salary details of the Visa holder? */}
                    {form.watch('visaType') === 'Employment Visa Renewal' && (
                      <>
                        <FormField
                          control={form.control}
                          name='salaryChange'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Do you want to change the salary details of the
                                Visa holder ?
                              </FormLabel>
                              <FormControl>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder='Choose' />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value='Yes'>Yes</SelectItem>
                                    <SelectItem value='No'>No</SelectItem>
                                  </SelectContent>
                                </Select>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </>
                    )}
                    {/* Basic Salary (AED) */}
                    <FormField
                      control={form.control}
                      name='basicSalary'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Basic Salary (AED){' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <div className='flex items-center border rounded-md'>
                              <Input
                                {...field}
                                placeholder='Enter'
                                value={field.value || basicSalary} // Use field.value from the form state
                                onChange={(e) => {
                                  const value = Number(e.target.value) // Ensure the value is a number
                                  setBasicSalary(value) // Update local state
                                  field.onChange(value) // Update form field state with number value
                                }}
                                min={10} // minimum 2 digits
                                max={9999999} // maximum 7 digits
                              />
                              <span className='px-3'>AED</span>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Transportation Allowance (AED) */}
                    <FormField
                      control={form.control}
                      name='transportationAllowance'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Transportation Allowance (AED)</FormLabel>
                          <FormControl>
                            <div className='flex items-center border rounded-md'>
                              <Input
                                {...field}
                                placeholder='Enter'
                                value={field.value || transportationAllowance}
                                onChange={(e) => {
                                  const value = Number(e.target.value)
                                  setTransportationAllowance(value)
                                  field.onChange(value)
                                }}
                              />
                              <span className='px-3'>AED</span>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Accommodation Allowance (AED) */}
                    <FormField
                      control={form.control}
                      name='accommodationAllowance'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Accommodation Allowance (AED)</FormLabel>
                          <FormControl>
                            <div className='flex items-center border rounded-md'>
                              <Input
                                {...field}
                                placeholder='Enter'
                                value={field.value || accommodationAllowance}
                                onChange={(e) => {
                                  const value = Number(e.target.value)
                                  setAccommodationAllowance(value)
                                  field.onChange(value)
                                }}
                              />
                              <span className='px-3'>AED</span>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {/* Other Allowance (AED) */}
                    <FormField
                      control={form.control}
                      name='otherAllowance'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Other Allowance (AED)</FormLabel>
                          <FormControl>
                            <div className='flex items-center border rounded-md'>
                              <Input
                                {...field}
                                placeholder='Enter'
                                value={field.value || otherAllowance}
                                onChange={(e) => {
                                  const value = Number(e.target.value)
                                  setOtherAllowance(value)
                                  field.onChange(value)
                                }}
                              />
                              <span className='px-3'>AED</span>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Total Monthly Salary (AED) */}

                    <FormField
                      control={form.control}
                      name='totalMonthlySalary'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Total Monthly Salary (AED)</FormLabel>
                          <FormControl>
                            <div className='flex items-center border rounded-md'>
                              <Input
                                {...field}
                                value={calculateTotalSalary()}
                                disabled
                              />
                              <span className='px-3'>AED</span>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </form>
                </Form>
              </CardContent>
            </Card>
          </>
        )}

        {/* === Step 6 Content === */}

        {currentStep === 6 && (
          <>
            <Card className='mt-6'>
              <CardHeader>
                <CardTitle>Company Details ( Page 6 of 7 )</CardTitle>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form className='space-y-4 fz-form'>
                    {/* Company Name */}

                    <FormField
                      control={form.control}
                      name='companyName'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Company Name <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder='Enter Company Name'
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Please type the company exactly as per your Trade
                            LIcense.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Trade License Number */}
                    <FormField
                      control={form.control}
                      name='tradeLicenseNumber'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Trade License Number{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder='Enter Trade License Number'
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Establishment Card Number  */}

                    <FormField
                      control={form.control}
                      name='establishmentCardNumber'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Establishment Card Number{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder='Enter Establishment Card Number'
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Please enter the number mentioned above the Bar
                            Code, e.g. 1/1/1234567
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Authorized Signatory/General Manager */}

                    <FormField
                      control={form.control}
                      name='authorizedSignatory'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Authorized Signatory/General Manager{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              value={field.value || ''}
                              // disabled
                            />
                          </FormControl>
                          <FormDescription>
                            Please mention the complete name of the Authorized
                            signatory or General Manager on the License.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {/*Email address of General Manager/Authorized Signatory */}
                    <FormField
                      control={form.control}
                      name='emailAddressOfGeneralManager'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Email address of General Manager/Authorized
                            Signatory <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              type='email'
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormDescription>
                            Please mention the complete/correct email address of
                            the General Manager/Authorized signatory.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Terms & Conditions Agreement */}

                    <FormField
                      control={form.control}
                      name='termsAndConditions'
                      render={({ field }) => (
                        <FormItem>
                          <div className='flex w-full items-center justify-between'>
                            <FormLabel className='mr-4'>
                              Terms & Conditions Agreement{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>

                            <div className='flex items-center justify-center flex-1 space-x-2'>
                              <FormControl>
                                <Checkbox
                                  checked={field.value}
                                  onCheckedChange={(checked: boolean) =>
                                    field.onChange(checked)
                                  }
                                />
                              </FormControl>
                              <span className='text-sm text-slate-700'>
                                I accept the specific conditions for the issue
                                of Visas in clauses 15-18 of IFZA's Terms and
                                Conditions.
                              </span>
                            </div>
                          </div>
                          <FormDescription>
                            By clicking the agreement box, you agree to IFZA
                            Terms & Conditions.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Alert className='border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <AlertDescription>
                        ​Please click here for{' '}
                        <a
                          href='https://www2.ifza.com/wp-content/uploads/2021/02/IFZA_TNCs-v4.pdf.pdf'
                          target='_blank'
                          rel='noopener noreferrer'
                          className='text-blue-500 underline'
                        >
                          Terms and Conditions.
                        </a>
                      </AlertDescription>
                    </Alert>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </>
        )}

        {/* === Step 7 Content === */}
        {currentStep === 7 && (
          <>
            <Card className='mt-6'>
              <CardHeader>
                <CardTitle>( Page 7 of 7 )</CardTitle>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form className='space-y-4 fz-form'>
                    {/* Photo of the applicant (passport size) */}
                    <div className='flex flex-wrap space-between gap-y-4'>
                      <div className='w-full sm:w-[49%] sm:mr-[1%] mb-2 sm:mb-0'>
                        <FormField
                          control={form.control}
                          name='photoOfApplicant'
                          render={() => (
                            <FormItem>
                              <FormLabel>
                                Photo of the applicant (passport size){' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <FileUploadField
                                  accept='.jpg,.jpeg,.png'
                                  onchoose={(file) => {
                                    form.setValue(
                                      'photoOfApplicant',
                                      file || ''
                                    )
                                    form.clearErrors('photoOfApplicant')
                                  }}
                                />
                              </FormControl>
                              <FormDescription>
                                Passport size photo.
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      {/* Visa Applicant Files */}
                      <div className='flex-1'>
                        <FormField
                          control={form.control}
                          name='visaApplicantFiles'
                          render={() => (
                            <FormItem>
                              <FormLabel>
                                Visa Applicant Files{' '}
                                <span style={{ color: 'red' }}>*</span>
                              </FormLabel>
                              <FormControl>
                                <FileUploadField
                                  accept='.pdf,.jpg,.jpeg'
                                  onchoose={(file) => {
                                    form.setValue(
                                      'visaApplicantFiles',
                                      file || ''
                                    )
                                    form.clearErrors('visaApplicantFiles')
                                  }}
                                />
                              </FormControl>
                              <FormDescription>
                                Please upload the attachments like Passport
                                Copy, Passport Size Picture, Cancellation
                                Document, Entry Stamp Copy or Tourist Visa copy.
                                You can find the guide for accepted document
                                types at the following link.
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                    {/* Proof of Payment */}
                    <FormField
                      control={form.control}
                      name='proofOfPayment'
                      render={() => (
                        <FormItem>
                          <FormLabel>Proof of Payment</FormLabel>
                          <FormControl>
                            <FileUploadField
                              accept='.pdf,.jpg,.jpeg,.png'
                              onchoose={(file) => {
                                form.setValue('proofOfPayment', file || '')
                                form.clearErrors('proofOfPayment')
                              }}
                            />
                          </FormControl>
                          <FormDescription>
                            Please upload the proof of payment for the Visa
                            Application. If payment was made earlier with the
                            License application, you can also attach it. Without
                            the proof of payment we are unable to proceed with
                            your application.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <CardHeader className='px-0 pt-0 pb-0'>
                      <CardTitle>Payments </CardTitle>
                    </CardHeader>

                    {/* Preferred Payment Method */}

                    <FormField
                      control={form.control}
                      name='preferredPaymentMethod'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Preferred Payment Method{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder='Choose' />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value='Cash Payment (IFZA Offices only)'>
                                  Cash Payment (IFZA Offices only)
                                </SelectItem>
                                <SelectItem value='Cheque'>Cheque</SelectItem>
                                <SelectItem value='Bank Transfer'>
                                  Bank Transfer
                                </SelectItem>
                                <SelectItem value='ATM Cash Deposit'>
                                  ATM Cash Deposit
                                </SelectItem>
                                <SelectItem value='Credit Card (Visa/Master Card)'>
                                  Credit Card (Visa/Master Card)
                                </SelectItem>
                                <SelectItem value='Already Paid with the License Application'>
                                  Already Paid with the License Application
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormDescription>
                            Please select the preferred payment method from the
                            list below For more details, please visit,
                            ifza.com/payments
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <CardHeader className='px-0 pt-0 pb-0'>
                      <CardTitle>Payment Details</CardTitle>
                    </CardHeader>

                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle>DISCLAIMER :</AlertTitle>
                        <AlertDescription>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            International transfers may take up to 7-10 working
                            days. Till the payment is reflected, your
                            application will be kept on hold.
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>

                    {/* Visa Fee */}

                    <FormField
                      control={form.control}
                      name='visaFee'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Visa Fee</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder=' '
                              onChange={(e) => {
                                const value =
                                  e.target.value === ''
                                    ? undefined
                                    : +e.target.value
                                field.onChange(value)
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {/* Status Change : Show this field when 'Visa App Type' is 'In-Country' AND 'Visa Type' IS NOT 'Employment Visa Renewal' OR 'Work Permit Renewal'*/}
                    {form.watch('visaApplicationType') === 'In-Country' &&
                      (form.watch('visaType') === '' ||
                        (form.watch('visaType') !== 'Employment Visa Renewal' &&
                          form.watch('visaType') !==
                            'Work Permit Renewal')) && (
                        <>
                          <FormField
                            control={form.control}
                            name='statusChange'
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Status Change</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    placeholder=' '
                                    onChange={(e) => {
                                      const value =
                                        e.target.value === ''
                                          ? undefined
                                          : +e.target.value
                                      field.onChange(value)
                                    }}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </>
                      )}
                    {/* Visa Stamping Fee : show this field when 'Resident Visa Stamping Type' is 'VIP' */}
                    {form.watch('residantVisaStamping') === 'VIP' && (
                      <>
                        <FormField
                          control={form.control}
                          name='vipStampingFee'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>VIP Stamping Fee</FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  type='number'
                                  placeholder='Enter '
                                  onChange={(e) => {
                                    const value =
                                      e.target.value === ''
                                        ? undefined
                                        : +e.target.value
                                    field.onChange(value)
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </>
                    )}
                    {/* Partner/Investor Visa */}

                    {(form.watch('jobTitle') === 'Partner' ||
                      form.watch('jobTitle') === 'Investor') && (
                      <>
                        <FormField
                          control={form.control}
                          name='partnerInvestorVisa'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Partner/Investor Visa</FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  placeholder=' '
                                  onChange={(e) => {
                                    const value =
                                      e.target.value === ''
                                        ? undefined
                                        : +e.target.value
                                    field.onChange(value)
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </>
                    )}

                    {/* Total Amount to be Paid */}

                    <FormField
                      control={form.control}
                      name='totalAmountToBePaid'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Total Amount to be Paid</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder=' ' disabled />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Alert className='border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <AlertDescription>
                        This is the amount due for the Visa Application as per
                        the selection of the required services. Please proceed
                        to{' '}
                        <a
                          href='https://ifza.com/en/payments/'
                          target='_blank'
                          rel='noopener noreferrer'
                          className='text-blue-500 underline'
                        >
                          https://ifza.com/en/payments/{' '}
                        </a>
                        for information on different payment methods available.
                      </AlertDescription>
                    </Alert>

                    <CardHeader className='px-0 pt-0 pb-0'>
                      <CardTitle>Important Information</CardTitle>
                    </CardHeader>

                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertDescription>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            The General Directorate of Residency and Foreign
                            Affairs – Dubai have updated the guidelines on
                            Personal Photo Specifications for all GDRFA
                            applications effective immediately.
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Detailed Guides can be found at{' '}
                            <a
                              href='https://beta.smartservices.ica.gov.ae/echannels/web/client/manual/icao/icao_english.pdf'
                              target='_blank'
                              rel='noopener noreferrer'
                              className='text-blue-500 underline'
                            >
                              this link
                            </a>
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>

                    {/* Name of the person completing the application form */}

                    <div className='flex-1'>
                      <div className='flex items-center space-x-2'>
                        <div className='flex flex-col w-full'>
                          <FormLabel>
                            Name of the person completing the application form
                          </FormLabel>
                          <div className='flex space-x-2 mt-4'>
                            <div className='w-full'>
                              <FormField
                                control={form.control}
                                name='firstName'
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Input
                                        {...field}
                                        placeholder='First Name'
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                            <div className='w-full'>
                              <FormField
                                control={form.control}
                                name='lastName'
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Input
                                        {...field}
                                        placeholder='Last Name'
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* I affirm that all the information provided ..... : Checkbox */}

                    <FormField
                      control={form.control}
                      name='affirmInformation'
                      render={({ field }) => (
                        <FormItem>
                          <div className='flex items-center'>
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={(checked: boolean) =>
                                  field.onChange(checked)
                                }
                              />
                            </FormControl>
                            <FormLabel className='ml-2'>
                              I affirm that all the information provided above
                              is accurate and true. I acknowledge that any
                              delays or rejections resulting from inaccuracies
                              in the information are my sole responsibility.
                            </FormLabel>
                          </div>

                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </form>
                </Form>
              </CardContent>
            </Card>
          </>
        )}

        {/* Buttons Section */}

        <div className='fixed bottom-0 right-0 p-3 pt-4 bg-background flex justify-end space-x-2 z-10 fz-form-btns'>
          <div className='flex-1'>
            <Button variant={'btn_outline'}>Save as Draft</Button>
          </div>
          <div className='flex gap-2'>
            {/* The 'Previous' button will not appear on the first step */}
            {currentStep !== 1 && (
              <Button variant='btn_outline' onClick={prevStep}>
                Previous
              </Button>
            )}

            {/* The 'Next' button will not appear on the third step */}
            {currentStep !== 7 && (
              <Button variant='default' onClick={nextStep}>
                Next
              </Button>
            )}
            {/* The Submit button will appear only in step 7 */}
            {currentStep === 7 && (
              <Button
                variant={'default'}
                onClick={() => {
                  onSubmit(form.getValues())
                }}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                    Processing...
                  </>
                ) : (
                  'Submit'
                )}
              </Button>
            )}
          </div>
        </div>
      </Main>
    </>
  )
}
