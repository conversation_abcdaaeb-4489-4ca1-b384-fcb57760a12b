import React, { useState, useEffect } from 'react'
import { useForm, FormProvider } from 'react-hook-form'
import { useWatch } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Loader2 } from 'lucide-react'
import { toast } from '@/hooks/use-toast'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import ApplicationProgress from '@/components/application-progress'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

import { ApplicationFormValues } from './types/application-form-types'
import { applicationFormSchema } from './schemas/application-form-schema'
import { z } from 'zod'

import Step1Form from './components/steps/Step1VisaApplication'
import Step2Form from './components/steps/Step2VisaApplication'
import Step3Form from './components/steps/Step3VisaApplication'
import Step4Form from './components/steps/Step4VisaApplication'
import Step5Form from './components/steps/Step5VisaApplication'
import Step6Form from './components/steps/Step6VisaApplication'
import Step7Form from './components/steps/Step7VisaApplication'

export default function VisaApplication() {
  const [isFormSubmitted, setIsFormSubmitted] = useState(false)
  console.log('isFormSubmitted', isFormSubmitted)
  const [isLoading, setIsLoading] = useState(false)
  useState<boolean>(false)
  const [date, setDate] = useState<Date | undefined>(undefined)
  const form = useForm<ApplicationFormValues>({
    resolver: zodResolver(applicationFormSchema),
    defaultValues: {
      appUpdatesEmail: '',
      whatsAppNumber: '',
      applicationDate: new Date(),
      visaType: '',
      tradeLicenseValidated: false, // Trade license must be valid for 90 days ...
      visaApplicationType: '',
      nationality: '',
      residentVisaStamping: '', // Resident Visa Stamping Type
      familyOnHoldLetter: '', // Do you require a family on hold letter ?
      renewalDisclaimer: false,
      visaFree: '', // Do you want to apply VFL for this VISA ?
      eVisaApplicationType: '',
      outsideCountryInstructions: false, // Applicant should not enter the UAE until...
      outsideCountry: false, // Outside Country Visa Declaration
      updatedRVCopy: '',
      idCardUpload: '', // National ID Card upload
      currentVisaStatus: '',
      visaValidUntil: new Date(),
      title: '',
      firstName: '',
      middleName: '',
      lastName: '',
      title1: '',
      arabicName: '', // Do you have Arabic Name in your Passport?
      firstNameArabic: '',
      middleNameArabic: '',
      lastNameArabic: '',
      emailAddress: '',
      phone: '',
      streetAddress: '', // UAE Address
      addressLine: '', // UAE Address
      cityAddress: '', // UAE Address
      stateProvince: '', // UAE Address
      country: '', // UAE Address
      streetAddress1: '', // Home Country Address
      addressLine2: '', // Home Country Address
      city: '', // Home Country Address
      province: '', // Home Country Address
      country1: '', // Home Country Address
      emiratesID: '',
      emiratesIDNumber: '',
      emiratesIDExpiryDate: new Date(),
      emiratesIDCopy: null,
      nationality1: '', // Nationality : Step 3
      passportNumber: '',
      placeOfIssue: '',
      placeOfIssueArabic: '',
      passportType: '',
      agreementPassportRules: false, // Agreement to passport rules
      Agreed: false, // I agree to the above status change statement and the rules associated
      countryOfIssuance: '',
      coloredPassport: null, // Colored Passport Copy Page 1
      coloredPassport2: null, // Colored Passport Copy Page 2
      passportIssueDate: undefined,
      passportExpiryDate: undefined,
      cityOfBirth: '',
      cityOfBirthArabic: '',
      countryOfBirth: '',
      dateOfBirth: undefined,
      gender: '',
      previousNationality: '',
      maritalStatus: '',
      religion: '',
      religionSubCategory: '',
      fatherFullName: '',
      motherFullName: '',
      motherFullNameArabic: '',
      photoOfApplicant: null,
      visaApplicantFiles: null,
      typeOfEmployment: 'Limited',
      employmentDuration: 1, // Employment Duration in Months
      jobTitleChange: '', // Do you want to change the Job title of the Visa holder ?
      jobTitle: '',
      educationQualification: '',
      employmentStartDate: undefined,
      probationPeriod: '',
      employmentTerminationNotice: '',
      returnTicket: '', // Return Ticket Eligibility
      ticketEntitlementPeriod: '',
      annualLeaveEntitlement: '',
      workingDays: 2,
      calendarDays: 3,
      salaryChange: '',
      basicSalary: 1,
      transportationAllowance: 0,
      accommodationAllowance: 0,
      otherAllowance: 0,
      totalMonthlySalary: 0,
      preferredPaymentMethod: '',
      visaFee: 3750,
      statusChange: 160,
      vipStampingFee: 1500,
      partnerInvestorVisa: 1000, // Partner/Investor Visa
      totalAmountToBePaid: 0,
      proofOfPayment: null,
      companyName: '',
      tradeLicenseNumber: '',
      establishmentCardNumber: '',
      authorizedSignatory: '',
      emailAddressOfGeneralManager: '',
      termsAndConditions: false, // Terms & Conditions Agreement : Step 6
      affirmInformation: false, // I affirm that all the information provided .. : Step 7
    },
    // shouldUnregister: true,
    mode: 'all',
    criteriaMode: 'all',
  })

  const [showEVisaType, setShowEVisaType] = useState(false)
  // suppress unused vars warnings for local allowances
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [basicSalary, _setBasicSalary] = useState(0)
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [transportationAllowance, _setTransportationAllowance] = useState(0)
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [accommodationAllowance, _setAccommodationAllowance] = useState(0)
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [otherAllowance, _setOtherAllowance] = useState(0)

  // calculateTotalSalary
  const calculateTotalSalary = () => {
    return (
      basicSalary +
      transportationAllowance +
      accommodationAllowance +
      otherAllowance
    )
  }

  useEffect(() => {
    // Update the total salary whenever any of the fields change
    const totalSalary = calculateTotalSalary()
    form.setValue('totalMonthlySalary', totalSalary)
  }, [
    basicSalary,
    transportationAllowance,
    accommodationAllowance,
    otherAllowance,
  ])

  const nationality = useWatch({ control: form.control, name: 'nationality' })
  const visaType = useWatch({ control: form.control, name: 'visaType' })

  useEffect(() => {
    // Ensure that nationality is not undefined or empty, and compare it to the list of nationalities that require an eVisa
    // 'Palestine State' is Missing in the list of nationalities
    const shouldShowEVisa = Boolean(
      nationality &&
        visaType &&
        visaType !== 'New Employment Visa' &&
        visaType !== 'New Investor Visa' &&
        visaType !== 'New Partner Visa' &&
        ![
          'AFG',
          'BGD',
          'DZA',
          'CHN',
          'EGY',
          'IDN',
          'IRN',
          'IRQ',
          'ISR',
          'LBN',
          'MAR',
          'NPL',
          'NGA',
          'LBY',
          'PAK',
          'SOM',
          'LKA',
          'SYR',
          'TUN',
          'YEM',
        ].includes(nationality)
    )
    // Set the state to a boolean value (true or false)
    setShowEVisaType(shouldShowEVisa)
  }, [nationality, visaType])

  useEffect(() => {
    if (nationality && nationality !== 'SYR' && nationality !== 'LBN') {
      form.setValue('passportType', 'Ordinary')
    }
  }, [nationality, form])

  const [currentStep, setCurrentStep] = React.useState(1) // Track current step

  const { watch, setValue, getValues } = form

  useEffect(() => {
    // Watch for changes in the fields
    const subscription = watch((values) => {
      const {
        visaFee = 0,
        statusChange = 0,
        vipStampingFee = 0,
        partnerInvestorVisa = 0,
        visaApplicationType,
        visaType,
        residentVisaStamping,
        jobTitle,
      } = values

      let total = 0

      // Always add visaFee if available
      total += Number(visaFee) || 0

      // Add statusChange only when Visa App Type is 'In-Country'
      // and Visa Type is NOT 'Employment Visa Renewal' or 'Work Permit Renewal'
      if (
        visaApplicationType === 'In-Country' &&
        visaType !== 'Employment Visa Renewal' &&
        visaType !== 'Work Permit Renewal'
      ) {
        total += Number(statusChange) || 0
      }

      // Add VIP Stamping Fee only when stamping type is 'VIP'
      if (residentVisaStamping === 'VIP') {
        total += Number(vipStampingFee) || 0
      }

      // Add Partner/Investor Visa fee only when job title is 'Partner' or 'Investor'
      if (jobTitle === 'Partner' || jobTitle === 'Investor') {
        total += Number(partnerInvestorVisa) || 0
      }

      // Check if the total has changed before calling setValue
      const currentTotal = getValues('totalAmountToBePaid')
      if (currentTotal !== total) {
        // Update the totalAmountToBePaid field with the computed sum if it's different
        setValue('totalAmountToBePaid', total)
      }
    })

    // Cleanup the subscription on unmount
    return () => subscription.unsubscribe()
  }, [watch, setValue, getValues])
  // const nationality = form.watch('nationality') //  ??

  const fieldsPerStep: Record<number, (keyof ApplicationFormValues)[]> = {
    1: [
      'appUpdatesEmail',
      'whatsAppNumber',
      'applicationDate',
      'visaType',
      'familyOnHoldLetter',
      'tradeLicenseValidated',
      'renewalDisclaimer',
      'visaApplicationType',
      'visaFree',
      'outsideCountryInstructions',
      'outsideCountry',
      'currentVisaStatus',
      'nationality',
      'Agreed',
      'updatedRVCopy',
      'idCardUpload',
      'eVisaApplicationType',
      'residentVisaStamping',
      'visaValidUntil',
    ], // Fields for step 1

    2: [
      'title',
      'firstName',
      'middleName',
      'lastName',
      'title1',
      'firstNameArabic',
      'middleNameArabic',
      'lastNameArabic',
      'emailAddress',
      'phone',
      'streetAddress',
      'addressLine',
      'cityAddress',
      'stateProvince',
      'country',
      'streetAddress1',
      'addressLine2',
      'city',
      'province',
      'country1',
      'emiratesID',
      'emiratesIDNumber',
      'emiratesIDExpiryDate',
      'emiratesIDCopy',
    ], // Fields for step 2

    3: [
      'nationality1',
      'arabicName',
      'passportNumber',
      'placeOfIssue',
      'placeOfIssueArabic',
      'passportType',
      'agreementPassportRules',
      'countryOfIssuance',
      'coloredPassport',
      'coloredPassport2',
      'passportIssueDate',
      'passportExpiryDate',
      'cityOfBirth',
      'cityOfBirthArabic',
      'countryOfBirth',
      'dateOfBirth',
      'gender',
      'previousNationality',
      'maritalStatus',
      'religion',
      'religionSubCategory',
      'fatherFullName',
      'motherFullName',
      'motherFullNameArabic',
    ], // Fields for step 3

    4: [
      'typeOfEmployment',
      'employmentDuration',
      'jobTitleChange',
      'jobTitle',
      'employmentStartDate',
      'probationPeriod',
      'employmentTerminationNotice',
      'educationQualification',
      'returnTicket',
      'ticketEntitlementPeriod',
      'annualLeaveEntitlement',
      'workingDays',
      'calendarDays',
    ], // Fields for step 4

    5: [
      'salaryChange',
      'basicSalary',
      'transportationAllowance',
      'accommodationAllowance',
      'otherAllowance',
      'totalMonthlySalary',
    ], // Fields for step 5

    6: [
      'companyName',
      'tradeLicenseNumber',
      'establishmentCardNumber',
      'authorizedSignatory',
      'emailAddressOfGeneralManager',
      'termsAndConditions',
    ], // Fields for step 6

    7: [
      'photoOfApplicant',
      'visaApplicantFiles',
      'proofOfPayment',
      'preferredPaymentMethod',
      'visaFee',
      'statusChange',
      'vipStampingFee',
      'partnerInvestorVisa',
      'totalAmountToBePaid',
      'firstName',
      'lastName',
      'affirmInformation',
    ], // Fields for step 7
  }

  const [stepValues, setStepValues] = useState<
    Record<number, ApplicationFormValues>
  >({})

  const getStepSchema = (step: number, values: ApplicationFormValues) => {
    const ineligibleNationalities = [
      'AFG',
      'BGD',
      'DZA',
      'CHN',
      'EGY',
      'IDN',
      'IRN',
      'IRQ',
      'ISR',
      'LBN',
      'MAR',
      'NPL',
      'NGA',
      'LBY',
      'PAK',
      'SOM',
      'LKA',
      'SYR',
      'TUN',
      'YEM',
    ]
    switch (step) {
      case 1:
        return z.object({
          appUpdatesEmail: applicationFormSchema.shape.appUpdatesEmail,
          visaType: applicationFormSchema.shape.visaType,
          ...((form.watch('visaType') === 'New Employment Visa' ||
            form.watch('visaType') === 'New Investor Visa' ||
            form.watch('visaType') === 'New Partner Visa') && {
            tradeLicenseValidated:
              applicationFormSchema.shape.tradeLicenseValidated,
          }),
          ...(values.visaType === 'Employment Visa Renewal' && {
            renewalDisclaimer: applicationFormSchema.shape.renewalDisclaimer,
          }),
          visaApplicationType: applicationFormSchema.shape.visaApplicationType,
          ...((form.watch('visaType') === 'New Employment Visa' ||
            form.watch('visaType') === 'New Investor Visa' ||
            form.watch('visaType') === 'New Partner Visa') && {
            visaFree: applicationFormSchema.shape.visaFree,
          }),
          ...(values.visaApplicationType === 'Out of Country' && {
            outsideCountryInstructions:
              applicationFormSchema.shape.outsideCountryInstructions,
          }),
          ...(values.visaApplicationType === 'Out of Country' && {
            outsideCountry: applicationFormSchema.shape.outsideCountry,
          }),
          ...(values.visaApplicationType === 'In-Country' && {
            currentVisaStatus: applicationFormSchema.shape.currentVisaStatus,
          }),
          nationality: applicationFormSchema.shape.nationality,
          ...(form.watch('visaApplicationType') === 'In-Country' &&
            ['AFG', 'BGD', 'PAK', 'NGA'].includes(
              form.watch('nationality')
            ) && {
              Agreed: applicationFormSchema.shape.Agreed,
            }),

          ...(values.visaType === 'Work Permit Renewal' && {
            updatedRVCopy: applicationFormSchema.shape.updatedRVCopy,
          }),
          ...((form.watch('nationality') === 'AFG' ||
            form.watch('nationality') === 'IRN' ||
            form.watch('nationality') === 'IRQ' ||
            form.watch('nationality') === 'PAK') && {
            idCardUpload: applicationFormSchema.shape.idCardUpload,
          }),

          ...(!ineligibleNationalities.includes(values.nationality ?? '') &&
            values.visaType !== 'New Employment Visa' &&
            values.visaType !== 'New Investor Visa' &&
            values.visaType !== 'New Partner Visa' && {
              eVisaApplicationType: z
                .string()
                .min(1, { message: 'Select a choice.' }),
            }),

          residentVisaStamping:
            applicationFormSchema.shape.residentVisaStamping,
        })

      case 2:
        return z.object({
          title: applicationFormSchema.shape.title,
          firstName: applicationFormSchema.shape.firstName,
          lastName: applicationFormSchema.shape.lastName,
          emailAddress: applicationFormSchema.shape.emailAddress,
          phone: applicationFormSchema.shape.phone,
          streetAddress1: applicationFormSchema.shape.streetAddress1,
          city: applicationFormSchema.shape.city,
          country1: applicationFormSchema.shape.country1,
          emiratesID: applicationFormSchema.shape.emiratesID,
          ...(values.emiratesID === 'Yes' && {
            emiratesIDNumber: applicationFormSchema.shape.emiratesIDNumber,
          }),
          ...(values.emiratesID === 'Yes' && {
            emiratesIDCopy: applicationFormSchema.shape.emiratesIDCopy,
          }),
        })

      case 3:
        return z.object({
          nationality1: applicationFormSchema.shape.nationality1,
          arabicName: applicationFormSchema.shape.arabicName,
          passportNumber: applicationFormSchema.shape.passportNumber,
          placeOfIssue: applicationFormSchema.shape.placeOfIssue,
          passportType: applicationFormSchema.shape.passportType,
          agreementPassportRules:
            applicationFormSchema.shape.agreementPassportRules,
          countryOfIssuance: applicationFormSchema.shape.countryOfIssuance,
          coloredPassport: applicationFormSchema.shape.coloredPassport,

          ...(['SYR', 'IND', 'TUR'].includes(values.countryOfIssuance) && {
            coloredPassport2: applicationFormSchema.shape.coloredPassport2,
          }),

          passportIssueDate: applicationFormSchema.shape.passportIssueDate,
          passportExpiryDate: applicationFormSchema.shape.passportExpiryDate,
          cityOfBirth: applicationFormSchema.shape.cityOfBirth,
          countryOfBirth: applicationFormSchema.shape.countryOfBirth,
          dateOfBirth: applicationFormSchema.shape.dateOfBirth,
          gender: applicationFormSchema.shape.gender,
          maritalStatus: applicationFormSchema.shape.maritalStatus,
          religion: applicationFormSchema.shape.religion,
          ...(values.religion === 'Islam' && {
            religionSubCategory:
              applicationFormSchema.shape.religionSubCategory,
          }),
          fatherFullName: applicationFormSchema.shape.fatherFullName,
          motherFullName: applicationFormSchema.shape.motherFullName,
        })

      case 4:
        return z.object({
          employmentDuration: applicationFormSchema.shape.employmentDuration,
          jobTitle: applicationFormSchema.shape.jobTitle,
          employmentStartDate: applicationFormSchema.shape.employmentStartDate,
          probationPeriod: applicationFormSchema.shape.probationPeriod,
          employmentTerminationNotice:
            applicationFormSchema.shape.employmentTerminationNotice,
          educationQualification:
            applicationFormSchema.shape.educationQualification,
          returnTicket: applicationFormSchema.shape.returnTicket,
          ...((form.watch('returnTicket') === 'Economy' ||
            form.watch('returnTicket') === 'Business' ||
            form.watch('returnTicket') === 'First Class') && {
            ticketEntitlementPeriod:
              applicationFormSchema.shape.ticketEntitlementPeriod,
          }),
          annualLeaveEntitlement:
            applicationFormSchema.shape.annualLeaveEntitlement,
          ...(form.watch('annualLeaveEntitlement') === 'Working Days' && {
            workingDays: applicationFormSchema.shape.workingDays,
          }),
          ...(form.watch('annualLeaveEntitlement') === 'Calendar Days' && {
            calendarDays: applicationFormSchema.shape.calendarDays,
          }),
        })

      case 5:
        return z.object({
          basicSalary: applicationFormSchema.shape.basicSalary,
        })

      case 6:
        return z.object({
          companyName: applicationFormSchema.shape.companyName,
          tradeLicenseNumber: applicationFormSchema.shape.tradeLicenseNumber,
          establishmentCardNumber:
            applicationFormSchema.shape.establishmentCardNumber,
          authorizedSignatory: applicationFormSchema.shape.authorizedSignatory,
          emailAddressOfGeneralManager:
            applicationFormSchema.shape.emailAddressOfGeneralManager,
          termsAndConditions: applicationFormSchema.shape.termsAndConditions,
        })

      case 7:
        return z.object({
          photoOfApplicant: applicationFormSchema.shape.photoOfApplicant,
          visaApplicantFiles: applicationFormSchema.shape.visaApplicantFiles,
          preferredPaymentMethod:
            applicationFormSchema.shape.preferredPaymentMethod,
          visaFee: applicationFormSchema.shape.visaFee,
          statusChange: applicationFormSchema.shape.statusChange,
          vipStampingFee: applicationFormSchema.shape.vipStampingFee,
          partnerInvestorVisa: applicationFormSchema.shape.partnerInvestorVisa,
          affirmInformation: applicationFormSchema.shape.affirmInformation,
        })

      default:
        return z.object({})
    }
  }

  // Function to move between steps
  const nextStep = async () => {
    const values = form.getValues()

    // Get the schema for the current step
    const schema = getStepSchema(currentStep, values)

    // Extract the fields required for validation from the schema
    const requiredFields = Object.keys(
      schema.shape
    ) as (keyof ApplicationFormValues)[]

    // Trigger validation for only the required fields
    await form.trigger(requiredFields)

    // Use Zod to validate the values manually and catch detailed errors
    const result = schema.safeParse(values)

    const stepFields = fieldsPerStep[currentStep]
    const currentStepValues = stepFields.reduce((acc, key) => {
      acc[key] = form.getValues(key)
      return acc
    }, {} as Partial<ApplicationFormValues>)

    console.log('Form Data For Step', currentStep, ':', currentStepValues)

    if (!result.success) {
      const issues = result.error.issues

      // Set manual errors for the invalid fields
      issues.forEach((issue) => {
        const field = issue.path[0] as keyof ApplicationFormValues

        if (requiredFields.includes(field)) {
          form.setError(field, {
            type: 'manual',
            message: issue.message,
          })
        }
      })

      console.log(
        'Validation Errors For Step',
        currentStep,
        ':',
        requiredFields.reduce(
          (acc, key) => {
            const error = form.formState.errors[key]
            if (error && typeof error === 'object' && 'message' in error) {
              acc[key] = error
            }
            return acc
          },
          {} as Record<string, unknown>
        )
      )

      toast({
        title: 'Error',
        description: 'Please fill in all the required fields.',
        variant: 'destructive',
      })

      return // Stop if there are validation errors
    }

    // Clear errors and proceed to the next step
    form.clearErrors()
    setStepValues((prev) => ({ ...prev, [currentStep]: values }))

    if (currentStep < 7) {
      setCurrentStep(currentStep + 1)
    }

    console.log(
      'Validation Errors For Step',
      currentStep,
      ': All fields have been filled correctly.'
    )
  }

  const prevStep = () => {
    if (currentStep > 1) {
      const previousStep = currentStep - 1
      const previousValues = stepValues[previousStep] || {}

      form.clearErrors() // clear all errors

      Object.entries(previousValues).forEach(([key, value]) => {
        const currentValue = form.getValues(key as keyof ApplicationFormValues)
        if (currentValue !== value) {
          form.setValue(key as keyof ApplicationFormValues, value, {
            shouldValidate: false,
            shouldTouch: false,
            shouldDirty: false,
          })
        }
      })

      setCurrentStep(previousStep)
    }
  }

  const onSubmit = async (data: ApplicationFormValues) => {
    console.log('Form Data:', data)
    // Get the fields for the current step
    const fieldsToValidate = fieldsPerStep[currentStep]

    // Trigger validation for the fields in the current step
    const isValid = await form.trigger(fieldsToValidate)

    // Log form data for the current step
    if (currentStep === 7) {
      const step7Values = fieldsPerStep[7].reduce((acc, field) => {
        acc[field] = form.getValues(field)
        return acc
      }, {} as Partial<ApplicationFormValues>)

      console.log('Form Data for Step 7 :', step7Values)
    }

    // Check if there are validation errors or if everything is filled correctly
    if (Object.keys(form.formState.errors).length === 0) {
      console.log(
        'Validation Errors For Step',
        currentStep,
        ':',
        'All fields have been filled correctly.'
      )
    } else {
      console.log(
        'Validation Errors For Step',
        currentStep,
        ':',
        form.formState.errors
      )
    }

    if (isValid) {
      // Set loading state to true when submission starts
      setIsLoading(true)
      setIsFormSubmitted(true)

      // Simulate API call with timeout
      setTimeout(() => {
        setIsLoading(false)
        toast({
          title: 'Success!',
          description: 'Your Visa Application has been submitted successfully.',
          variant: 'success',
        })
      }, 1500)
    } else {
      // Handle validation errors
      toast({
        title: 'Error',
        description: 'Please fill in all the required fields.',
        variant: 'destructive',
      })
    }
  }

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        {/* <TopNav links={topNav} /> */}
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-2 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight'>
            Visa Application
          </h1>
          {/* <div className='flex items-center space-x-2'>
            <Button>Download</Button>
          </div> */}
        </div>
        <ApplicationProgress currentStep={currentStep} type='visa' />

        {/* === Step 1 Content === */}
        {currentStep === 1 && (
          <Card className='mt-6'>
            <CardHeader className='pb-0'>
              <CardTitle>Basic Information ( Page 1 of 7 )</CardTitle>
            </CardHeader>
            <CardContent>
              <FormProvider {...form}>
                <Step1Form showEVisaType={showEVisaType} />
              </FormProvider>
            </CardContent>
          </Card>
        )}

        {/* === Step 2 Content === */}
        {currentStep === 2 && (
          <Card className='mt-6'>
            <CardHeader className='pb-0'>
              <CardTitle>Personal Details ( Page 2 of 7 )</CardTitle>
            </CardHeader>
            <CardContent>
              <FormProvider {...form}>
                <Step2Form date={date} setDate={setDate} />
              </FormProvider>
            </CardContent>
          </Card>
        )}

        {/* === Step 3 Content === */}
        {currentStep === 3 && (
          <>
            <Card className='mt-6'>
              <CardHeader className='pb-0'>
                <CardTitle>Passport Information ( Page 3 of 7 )</CardTitle>
              </CardHeader>
              <CardContent>
                <FormProvider {...form}>
                  <Step3Form setDate={setDate} />
                </FormProvider>
              </CardContent>
            </Card>
          </>
        )}

        {/* === Step 4 Content === */}
        {currentStep === 4 && (
          <>
            <Card className='mt-6'>
              <CardHeader className='pb-0'>
                <CardTitle>Employment Information ( Page 4 of 7 )</CardTitle>
              </CardHeader>
              <CardContent>
                <FormProvider {...form}>
                  <Step4Form setDate={setDate} />
                </FormProvider>
              </CardContent>
            </Card>
          </>
        )}

        {/* === Step 5 Content === */}
        {currentStep === 5 && (
          <Card className='mt-6'>
            <CardHeader className='pb-0'>
              <CardTitle>
                Monthly Salary Breakdown (AED) ( Page 5 of 7 )
              </CardTitle>
            </CardHeader>
            <CardContent>
              <FormProvider {...form}>
                <Step5Form
                  basicSalary={basicSalary}
                  setBasicSalary={_setBasicSalary}
                  transportationAllowance={transportationAllowance}
                  setTransportationAllowance={_setTransportationAllowance}
                  accommodationAllowance={accommodationAllowance}
                  setAccommodationAllowance={_setAccommodationAllowance}
                  otherAllowance={otherAllowance}
                  setOtherAllowance={_setOtherAllowance}
                  calculateTotalSalary={calculateTotalSalary}
                />
              </FormProvider>
            </CardContent>
          </Card>
        )}

        {/* === Step 6 Content === */}

        {currentStep === 6 && (
          <Card className='mt-6'>
            <CardHeader className='pb-0'>
              <CardTitle>Company Details ( Page 6 of 7 )</CardTitle>
            </CardHeader>
            <CardContent>
              <FormProvider {...form}>
                <Step6Form />
              </FormProvider>
            </CardContent>
          </Card>
        )}

        {/* === Step 7 Content === */}
        {currentStep === 7 && (
          <Card className='mt-6'>
            <CardHeader className='pb-0'>
              <CardTitle>( Page 7 of 7 )</CardTitle>
            </CardHeader>
            <CardContent>
              <FormProvider {...form}>
                <Step7Form />
              </FormProvider>
            </CardContent>
          </Card>
        )}

        {/* Buttons Section */}

        <div className='fixed bottom-0 right-0 p-3 pt-4 bg-background flex justify-end space-x-2 z-10 fz-form fz-form-btns'>
          <div className='flex-1'>
            {/* <Button variant={'btn_outline'}>Save as Draft</Button> */}
          </div>
          <div className='flex gap-2'>
            {/* The 'Previous' button will not appear on the first step */}
            {currentStep !== 1 && (
              <Button variant='btn_outline' onClick={prevStep}>
                Previous
              </Button>
            )}

            {/* The 'Next' button will not appear on the third step */}
            {currentStep !== 7 && (
              <Button variant='default' onClick={nextStep}>
                Next
              </Button>
            )}
            {/* The Submit button will appear only in step 7 */}
            {currentStep === 7 && (
              <Button
                variant={'default'}
                onClick={() => {
                  onSubmit(form.getValues())
                }}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                    Processing...
                  </>
                ) : (
                  'Submit'
                )}
              </Button>
            )}
          </div>
        </div>
      </Main>
    </>
  )
}
