import * as React from 'react'
import * as ToastPrimitives from '@radix-ui/react-toast'
import { cva, type VariantProps } from 'class-variance-authority'
import { X, BadgeCheck, BadgeAlert, BadgeInfo, BadgeX } from 'lucide-react'
import { cn } from '@/lib/utils'

const ToastProvider = ToastPrimitives.Provider

const ToastViewport = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Viewport>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Viewport
    ref={ref}
    className={cn(
      'fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]',
      className
    )}
    {...props}
  />
))
ToastViewport.displayName = ToastPrimitives.Viewport.displayName

const toastVariants = cva(
  'group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full',
  {
    variants: {
      variant: {
        default: 'border bg-background text-foreground',
        destructive:
          'destructive group border-destructive bg-red-500 text-destructive-foreground',
        success:
          'success group border-l-4 border-green-500 bg-green-50 text-green-500 dark:border-green-500 dark:bg-green-950 dark:text-green-500',
        warning:
          'warning group border-l-4 border-yellow-500 bg-yellow-50 text-yellow-500 dark:border-yellow-500 dark:bg-yellow-950 dark:text-yellow-300',
        info:
          'info group border-l-4 border-blue-500 bg-blue-50 text-blue-500 dark:border-blue-500 dark:bg-blue-950 dark:text-blue-300',
        error:
          'error group border-l-4 border-red-500 bg-red-50 text-red-500 dark:border-red-500 dark:bg-red-950 dark:text-red-300',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
)

const ToastIcon = ({ variant }: { variant?: string }) => {
  if (!variant || variant === 'default') return null
  
  const iconClasses = "h-7 w-7 mr-2"
  
  if (variant === 'success') return <BadgeCheck className={cn(iconClasses, "text-green-600 dark:text-green-400")} />
  if (variant === 'warning') return <BadgeAlert className={cn(iconClasses, "text-yellow-600 dark:text-yellow-400")} />
  if (variant === 'info') return <BadgeInfo className={cn(iconClasses, "text-blue-600 dark:text-blue-400")} />
  if (variant === 'error' || variant === 'destructive') return <BadgeX className={cn(iconClasses, "text-white-600 dark:text-white-400")} />
  
  return null
}

const Toast = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &
    VariantProps<typeof toastVariants>
>(({ className, variant, ...props }, ref) => {
  return (
    <ToastPrimitives.Root
      ref={ref}
      className={cn(toastVariants({ variant }), className)}
      {...props}
    >
      <div className="flex items-start">
        <ToastIcon variant={variant as string} />
        <div className="flex flex-col gap-1 mt-1">
          {props.children}
        </div>
      </div>
    </ToastPrimitives.Root>
  )
})
Toast.displayName = ToastPrimitives.Root.displayName

const ToastAction = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Action>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Action
    ref={ref}
    className={cn(
      'inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium transition-colors hover:bg-secondary focus:outline-none focus:ring-1 focus:ring-ring disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive group-[.success]:border-green-300/40 group-[.success]:hover:border-green-400 group-[.success]:hover:bg-green-100 group-[.success]:focus:ring-green-400 group-[.success]:dark:hover:bg-green-800 group-[.warning]:border-yellow-300/40 group-[.warning]:hover:border-yellow-400 group-[.warning]:hover:bg-yellow-100 group-[.warning]:focus:ring-yellow-400 group-[.warning]:dark:hover:bg-yellow-800 group-[.info]:border-blue-300/40 group-[.info]:hover:border-blue-400 group-[.info]:hover:bg-blue-100 group-[.info]:focus:ring-blue-400 group-[.info]:dark:hover:bg-blue-800 group-[.error]:border-red-300/40 group-[.error]:hover:border-red-400 group-[.error]:hover:bg-red-100 group-[.error]:focus:ring-red-400 group-[.error]:dark:hover:bg-red-800',
      className
    )}
    {...props}
  />
))
ToastAction.displayName = ToastPrimitives.Action.displayName

const ToastClose = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Close>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Close
    ref={ref}
    className={cn(
      'absolute right-1 top-1 rounded-md p-1 text-foreground ' +
      'group-[.destructive]:text-red-300 ' +
      'group-[.success]:text-green-300 ' +
      'group-[.warning]:text-yellow-300 ' +
      'group-[.info]:text-blue-300 ' +
      'group-[.error]:text-red-300',
      className
    )}
    {...props}
  >
    <X className="h-4 w-4" />
  </ToastPrimitives.Close>
))
ToastClose.displayName = ToastPrimitives.Close.displayName

const ToastTitle = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Title>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Title
    ref={ref}
    className={cn('text-sm font-semibold', className)}
    {...props}
  />
))
ToastTitle.displayName = ToastPrimitives.Title.displayName

const ToastDescription = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Description>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Description
    ref={ref}
    className={cn('text-sm opacity-90', className)}
    {...props}
  />
))
ToastDescription.displayName = ToastPrimitives.Description.displayName

type ToastProps = React.ComponentPropsWithoutRef<typeof Toast>

type ToastActionElement = React.ReactElement<typeof ToastAction>

export {
  type ToastProps,
  type ToastActionElement,
  ToastProvider,
  ToastViewport,
  Toast,
  ToastTitle,
  ToastDescription,
  ToastClose,
  ToastAction,
}
