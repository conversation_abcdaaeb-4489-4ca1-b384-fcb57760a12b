import { z } from 'zod'
import { applicationFormSchema } from '../schemas/application-form-schema'
import { ApplicationFormValues } from '../types/application-form-types'
import { UseFormWatch } from 'react-hook-form';

export const getStepSchema = (step: number, values: ApplicationFormValues, watch: UseFormWatch<ApplicationFormValues>) => {
  const ineligibleNationalities = [
    'AFG',
    'BGD',
    'DZA',
    'CHN',
    'EGY',
    'IDN',
    'IRN',
    'IRQ',
    'ISR',
    'LBN',
    'MAR',
    'NPL',
    'NGA',
    'LBY',
    'PAK',
    'SOM',
    'LKA',
    'SYR',
    'TUN',
    'YEM',
  ]
  switch (step) {
    case 1:
      return z.object({
        appUpdatesEmail: applicationFormSchema.shape.appUpdatesEmail,
        visaType: applicationFormSchema.shape.visaType,
        ...((watch('visaType') === 'New Employment Visa' ||
          watch('visaType') === 'New Investor Visa' ||
          watch('visaType') === 'New Partner Visa') && {
          tradeLicenseValidated:
            applicationFormSchema.shape.tradeLicenseValidated,
        }),
        ...(values.visaType === 'Employment Visa Renewal' && {
          renewalDisclaimer: applicationFormSchema.shape.renewalDisclaimer,
        }),
        visaApplicationType: applicationFormSchema.shape.visaApplicationType,
        ...((watch('visaType') === 'New Employment Visa' ||
          watch('visaType') === 'New Investor Visa' ||
          watch('visaType') === 'New Partner Visa') && {
          visaFree: applicationFormSchema.shape.visaFree,
        }),
        ...(values.visaApplicationType === 'Out of Country' && {
          outsideCountryInstructions:
            applicationFormSchema.shape.outsideCountryInstructions,
        }),
        ...(values.visaApplicationType === 'Out of Country' && {
          outsideCountry: applicationFormSchema.shape.outsideCountry,
        }),
        ...(values.visaApplicationType === 'In-Country' && {
          currentVisaStatus: applicationFormSchema.shape.currentVisaStatus,
        }),
        nationality: applicationFormSchema.shape.nationality,
        ...(watch('visaApplicationType') === 'In-Country' &&
          ['AFG', 'BGD', 'PAK', 'NGA'].includes(
            watch('nationality')
          ) && {
            Agreed: applicationFormSchema.shape.Agreed,
          }),

        ...(values.visaType === 'Work Permit Renewal' && {
          updatedRVCopy: applicationFormSchema.shape.updatedRVCopy,
        }),
        ...((watch('nationality') === 'AFG' ||
          watch('nationality') === 'IRN' ||
          watch('nationality') === 'IRQ' ||
          watch('nationality') === 'PAK') && {
          idCardUpload: applicationFormSchema.shape.idCardUpload,
        }),

        ...(!ineligibleNationalities.includes(values.nationality ?? '') &&
          values.visaType !== 'New Employment Visa' &&
          values.visaType !== 'New Investor Visa' &&
          values.visaType !== 'New Partner Visa' && {
            eVisaApplicationType: z
              .string()
              .min(1, { message: 'Select a choice.' }),
          }),

        residentVisaStamping:
          applicationFormSchema.shape.residentVisaStamping,
      })

    case 2:
      return z.object({
        title: applicationFormSchema.shape.title,
        firstName: applicationFormSchema.shape.firstName,
        lastName: applicationFormSchema.shape.lastName,
        emailAddress: applicationFormSchema.shape.emailAddress,
        phone: applicationFormSchema.shape.phone,
        streetAddress1: applicationFormSchema.shape.streetAddress1,
        city: applicationFormSchema.shape.city,
        country1: applicationFormSchema.shape.country1,
        emiratesID: applicationFormSchema.shape.emiratesID,
        ...(values.emiratesID === 'Yes' && {
          emiratesIDNumber: applicationFormSchema.shape.emiratesIDNumber,
        }),
        ...(values.emiratesID === 'Yes' && {
          emiratesIDCopy: applicationFormSchema.shape.emiratesIDCopy,
        }),
      })

    case 3:
      return z.object({
        nationality1: applicationFormSchema.shape.nationality1,
        arabicName: applicationFormSchema.shape.arabicName,
        passportNumber: applicationFormSchema.shape.passportNumber,
        placeOfIssue: applicationFormSchema.shape.placeOfIssue,
        passportType: applicationFormSchema.shape.passportType,
        agreementPassportRules:
          applicationFormSchema.shape.agreementPassportRules,
        countryOfIssuance: applicationFormSchema.shape.countryOfIssuance,
        coloredPassport: applicationFormSchema.shape.coloredPassport,

        ...(['SYR', 'IND', 'TUR'].includes(values.countryOfIssuance) && {
          coloredPassport2: applicationFormSchema.shape.coloredPassport2,
        }),

        passportIssueDate: applicationFormSchema.shape.passportIssueDate,
        passportExpiryDate: applicationFormSchema.shape.passportExpiryDate,
        cityOfBirth: applicationFormSchema.shape.cityOfBirth,
        countryOfBirth: applicationFormSchema.shape.countryOfBirth,
        dateOfBirth: applicationFormSchema.shape.dateOfBirth,
        gender: applicationFormSchema.shape.gender,
        maritalStatus: applicationFormSchema.shape.maritalStatus,
        religion: applicationFormSchema.shape.religion,
        ...(values.religion === 'Islam' && {
          religionSubCategory:
            applicationFormSchema.shape.religionSubCategory,
        }),
        fatherFullName: applicationFormSchema.shape.fatherFullName,
        motherFullName: applicationFormSchema.shape.motherFullName,
      })

    case 4:
      return z.object({
        employmentDuration: applicationFormSchema.shape.employmentDuration,
        jobTitle: applicationFormSchema.shape.jobTitle,
        employmentStartDate: applicationFormSchema.shape.employmentStartDate,
        probationPeriod: applicationFormSchema.shape.probationPeriod,
        employmentTerminationNotice:
          applicationFormSchema.shape.employmentTerminationNotice,
        educationQualification:
          applicationFormSchema.shape.educationQualification,
        returnTicket: applicationFormSchema.shape.returnTicket,
        ...((watch('returnTicket') === 'Economy' ||
          watch('returnTicket') === 'Business' ||
          watch('returnTicket') === 'First Class') && {
          ticketEntitlementPeriod:
            applicationFormSchema.shape.ticketEntitlementPeriod,
        }),
        annualLeaveEntitlement:
          applicationFormSchema.shape.annualLeaveEntitlement,
        ...(watch('annualLeaveEntitlement') === 'Working Days' && {
          workingDays: applicationFormSchema.shape.workingDays,
        }),
        ...(watch('annualLeaveEntitlement') === 'Calendar Days' && {
          calendarDays: applicationFormSchema.shape.calendarDays,
        }),
      })

    case 5:
      return z.object({
        basicSalary: applicationFormSchema.shape.basicSalary,
      })

    case 6:
      return z.object({
        companyName: applicationFormSchema.shape.companyName,
        tradeLicenseNumber: applicationFormSchema.shape.tradeLicenseNumber,
        establishmentCardNumber:
          applicationFormSchema.shape.establishmentCardNumber,
        authorizedSignatory: applicationFormSchema.shape.authorizedSignatory,
        emailAddressOfGeneralManager:
          applicationFormSchema.shape.emailAddressOfGeneralManager,
        termsAndConditions: applicationFormSchema.shape.termsAndConditions,
      })

    case 7:
      return z.object({
        photoOfApplicant: applicationFormSchema.shape.photoOfApplicant,
        visaApplicantFiles: applicationFormSchema.shape.visaApplicantFiles,
        preferredPaymentMethod:
          applicationFormSchema.shape.preferredPaymentMethod,
        visaFee: applicationFormSchema.shape.visaFee,
        statusChange: applicationFormSchema.shape.statusChange,
        vipStampingFee: applicationFormSchema.shape.vipStampingFee,
        partnerInvestorVisa: applicationFormSchema.shape.partnerInvestorVisa,
        affirmInformation: applicationFormSchema.shape.affirmInformation,
      })

    default:
      return z.object({})
  }
}