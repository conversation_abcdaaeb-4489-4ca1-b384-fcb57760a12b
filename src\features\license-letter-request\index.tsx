import { useEffect, useState } from 'react'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
// import { createLicenseLetterRequest, mainpulateLicenseLetterRequestData } from '@/services/licenseletterrequest'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  createLicenseLetterRequest,
  createLicenseLetterRequestFiles,
  mainpulateLicenseLetterRequestData,
} from '@/services/licenseletterrequest'
import {
  FilePenLine,
  Lightbulb,
  Loader2,
  PlusIcon,
  TrashIcon,
} from 'lucide-react'
import PhoneInput from 'react-phone-input-2'
import 'react-phone-input-2/lib/style.css'
import { toast } from '@/hooks/use-toast'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { CountryDropdown } from '@/components/ui/country-dropdown'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog'
import FileUploadField from '@/components/ui/fileUpload'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
// import { ProfileDropdown } from '@/components/profile-dropdown'
// import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

interface RowData {
  id: number
  addressLine1: string
  addressLine2: string
  city: string
  country: string
}

const applicationFormSchema = z
  .object({
    email: z.string().email({ message: 'Invalid email address.' }),
    title: z.string().min(2, { message: '*' }),
    firstName: z
      .string()
      .min(2, { message: 'First Name must be at least 2 characters.' }),
    lastName: z
      .string()
      .min(2, { message: 'Last Name must be at least 2 characters.' }),
    phone: z.string().optional(),
    mobileNumber: z.string().optional(),
    registeredCompanyName: z
      .string()
      .min(2, { message: 'Registered Company Name is required' }),
    tradeLicenseNumber: z
      .string()
      .min(1, { message: 'Trade License Number is required.' }),
    preferredPaymentOption: z.string().min(1, { message: 'Select a choice.' }),

    numberOfShareholders: z
      .number()
      .min(1, { message: 'Number of Shareholders must be at least 1.' })
      .optional(),

    lettersDocumentsRequired: z
      .string()
      .min(1, { message: 'Select a choice.' }),

    registryExtractIssued: z
      .string()
      .min(1, { message: 'Registry Extract Issued is required.' })
      .optional(),

    letterAddressedTo: z
      .string()
      .min(1, { message: 'Letter Addressed To is required.' })
      .optional(), // By default, it's not mandatory

    purposeOfNOC: z
      .string()
      .min(1, { message: 'Purpose of NOC is required.' })
      .optional(),

    languageOfLetter: z
      .string()
      .min(1, { message: 'Language of Letter is required.' })
      .optional(),

    servicetype: z
      .string()
      .min(1, { message: 'Service Type is required.' })
      .optional(),

    //'Certificate of Incumbency' &&'Is the Registry Extract document .. ?' is 'no'||'Certified True Copy - Registry Extract' && 'Is the Registry Extract document .. ?' is 'no'||'Letter of Good Standing' && 'Is the Registry Extract .. ?' is 'no' || 'Registry Extract'||'IFZA Letters & NOCs' ||'Certified True Copy - MOA'

    serviceType: z
      .string()
      .min(1, { message: 'Service Type is required.' })
      .optional(), // Show Service Type field when the user selects "Company Stamp"

    companyStampFile: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),

    attestedDocument: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),

    developerName: z
      .string()
      .min(1, { message: 'Developer Name is required.' })
      .optional(),
    propertyUnitNumber: z
      .string()
      .min(1, { message: 'Property Unit Number is required.' })
      .optional(),
    carMake: z.string().min(1, { message: 'Car Make is required.' }).optional(),
    carModel: z
      .string()
      .min(1, { message: 'Car Model is required.' })
      .optional(),
    yearOfManufacture: z
      .string()
      .min(1, { message: 'Year of Manufacture is required.' })
      .optional(),
    vinNumber: z
      .string()
      .min(1, { message: 'VIN Number is required.' })
      .optional(),
    carColor: z
      .string()
      .min(1, { message: 'Car Color is required.' })
      .optional(),
    carEngineNumber: z
      .string()
      .min(1, { message: 'Car Engine Number is required.' })
      .optional(),
    countryOfOrigin: z
      .string()
      .min(1, { message: 'Country of Origin is required.' })
      .optional(),
    details: z.string().min(1, { message: 'Details are required.' }).optional(),

    shareCapitalLetter: z

      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),

    shareholdersCount: z
      .string()
      .min(1, { message: 'Select a choice.' })
      .optional(),

    moaIssuedPreviously: z
      .string()
      .min(1, { message: 'Select a choice.' })
      .optional(),
    registaryAddressedTo: z
      .string()
      .min(1, { message: 'Registry Addressed To is required.' })
      .optional(),

    shareholderAddresses: z
      .array(
        z.object({
          id: z.number(),
          addressLine1: z.string().min(1, { message: 'Required.' }),
          addressLine2: z.string().optional(),
          city: z.string().min(1, { message: 'Rquired.' }),
          country: z.string().min(1, { message: 'Required.' }),
        })
      )
      .min(1, { message: 'At least one address is required.' })
      .optional(),

    agreeToRequirements: z
      .boolean()
      .refine((val) => val === true, {
        message: 'Select this option.',
      })
      .optional(),

    typeOfDocument: z
      .string()
      .min(1, { message: 'Select a choice.' })
      .optional(),

    documentIssueLocation: z
      .string()
      .min(1, { message: 'Select a choice.' })
      .optional(),

    attestedByEmbassy: z
      .string()
      .min(1, { message: 'Select a choice.' })
      .optional(),

    isMoaAoa: z.string().min(1, { message: 'Select a choice.' }).optional(),

    numDocuments: z.string().min(1, { message: 'Select a choice.' }).optional(),
    uploadCorporateDocuments1: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),
    uploadCorporateDocuments2: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),

    uploadCorporateDocuments3: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),

    uploadCorporateDocuments4: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),

    uploadCorporateDocuments5: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),

    uploadCorporateDocuments6: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),

    uploadCorporateDocuments7: z
      .any()
      .refine((val) => val instanceof File && val.size > 0, {
        message: 'Upload a file here.',
      })
      .optional(),
    typeOfCourierService: z
      .string()
      .min(1, { message: 'Select a choice.' })
      .optional(),
  })

  .refine(
    (data) => {
      if (!data.shareholdersCount || data.shareholdersCount === 'choose') {
        return true
      }
      return data.shareholderAddresses?.every(
        (address) => address.addressLine1?.trim() !== ''
      )
    },
    {
      message: 'Required.',
      path: ['shareholderAddresses', 0, 'addressLine1'],
    }
  )
  .refine(
    (data) => {
      if (!data.shareholdersCount || data.shareholdersCount === 'choose') {
        return true
      }
      return data.shareholderAddresses?.every(
        (address) => address.city?.trim() !== ''
      )
    },
    {
      message: 'Required.',
      path: ['shareholderAddresses', 0, 'city'],
    }
  )
  .refine(
    (data) => {
      if (!data.shareholdersCount || data.shareholdersCount === 'choose') {
        return true
      }
      return data.shareholderAddresses?.every(
        (address) => address.country?.trim() !== ''
      )
    },
    {
      message: 'Required.',
      path: ['shareholderAddresses', 0, 'country'],
    }
  )

  .refine(
    (data) => {
      // Check if "Letter Addressed To" is required based on the selected document and condition
      const requiredDocuments = [
        'Certificate of Name change',
        'Certified True Copy - Certificate of Continuation',
        'Certified True Copy - Certificate of Formation',
        'Copy of COF/COC',
        'Copy of Original CONC (Certificate of Name Change)',
        'Copy of Trade License',
        'Copy of ULA',
        'IFZA Letters & NOCs',
        'Registry Extract',
        'Set of Incorporation Documents',
        'NOC - For Company member to be a Shareholder in another freezone/Mainland',
        'NOC - For the IFZA company to be a Shareholder in another freezone/Mainland',
      ]

      const lettersDocumentsRequired = data.lettersDocumentsRequired

      if (requiredDocuments.includes(lettersDocumentsRequired)) {
        return (
          data.letterAddressedTo && data.letterAddressedTo.trim().length > 0
        )
      }

      return true // No need to make "Letter Addressed To" mandatory if the condition isn't met
    },
    {
      message:
        'Letter Addressed To is required when specific documents are selected.',
    }
  )

  .refine(
    (data) => {
      const condition =
        (data.lettersDocumentsRequired === 'Certificate of Incumbency' &&
          data.registryExtractIssued === 'no') ||
        (data.lettersDocumentsRequired ===
          'Certified True Copy - Registry Extract' &&
          data.registryExtractIssued === 'no') ||
        (data.lettersDocumentsRequired === 'Letter of Good Standing' &&
          data.registryExtractIssued === 'no')

      if (condition) {
        return (
          data.registaryAddressedTo &&
          data.registaryAddressedTo.trim().length > 0
        )
      }
      return true // No need to make it mandatory if the condition isn't met
    },
    { message: 'Registry Addressed To is required when the condition is met.' }
  )

  .refine(
    (data) => {
      const condition =
        (data.lettersDocumentsRequired === 'Certificate of Incumbency' &&
          data.registryExtractIssued === 'no') ||
        (data.lettersDocumentsRequired ===
          'Certified True Copy - Registry Extract' &&
          data.registryExtractIssued === 'no') ||
        data.lettersDocumentsRequired === 'Registry Extract' ||
        (data.lettersDocumentsRequired === 'Letter of Good Standing' &&
          data.registryExtractIssued === 'no') ||
        data.lettersDocumentsRequired === 'IFZA Letters & NOCs' ||
        data.lettersDocumentsRequired === 'Certified True Copy - MOA'

      if (condition) {
        return data.servicetype && data.servicetype.trim().length > 0
      }
      return true // No need to make it mandatory if the condition isn't met
    },
    { message: 'Service Type is required when the condition is met.' }
  )

  .refine(
    (data) => {
      if (data.lettersDocumentsRequired === 'Certified True Copy - MOA') {
        return (
          data.moaIssuedPreviously && data.moaIssuedPreviously.trim().length > 0
        )
      }
      return true // No need to make it mandatory if the condition isn't met
    },
    {
      message:
        'MOA Issued Previously is required when Certified True Copy - MOA is selected.',
    }
  )

  .refine(
    (data) => {
      const condition =
        data.lettersDocumentsRequired === 'Letter of Good Standing' ||
        data.lettersDocumentsRequired === 'Certificate of Incumbency' ||
        data.lettersDocumentsRequired ===
          'Certified True Copy - Registry Extract' ||
        data.lettersDocumentsRequired === 'Registry Extract'

      if (condition) {
        return data.languageOfLetter && data.languageOfLetter.trim().length > 0
      }
      return true // No need to make it mandatory if the condition isn't met
    },
    { message: 'Language of Letter is required when the condition is met.' }
  )

  .refine(
    (data) => {
      if (data.lettersDocumentsRequired === 'Company Stamp') {
        return data.serviceType && data.serviceType.trim().length > 0
      }
      return true // No need to make it mandatory if the condition isn't met
    },
    { message: 'Service Type is required when "Company Stamp" is selected.' }
  )

  .refine(
    (data) => {
      if (
        data.lettersDocumentsRequired === 'Company Stamp' &&
        data.serviceType === 'personalized'
      ) {
        return data.companyStampFile && data.companyStampFile !== ''
      }
      return true // No need to make it mandatory if the condition isn't met
    },
    {
      message:
        'Company Stamp File is required when "Company Stamp" and "personalized" are selected.',
    }
  )

  .refine(
    (data) => {
      if (data.lettersDocumentsRequired === 'General Document Attestation') {
        return data.attestedDocument && data.attestedDocument !== ''
      }
      return true
    },
    {
      message:
        'Attested Document is required when "General Document Attestation" is selected.',
    }
  )

  .refine(
    (data) => {
      if (data.lettersDocumentsRequired === 'IFZA Letters & NOCs') {
        return data.purposeOfNOC && data.purposeOfNOC.trim().length > 0
      }
      return true // No need to make it mandatory if the condition isn't met
    },
    {
      message:
        'Purpose of NOC is required when "IFZA Letters & NOCs" is selected.',
    }
  )

  .refine(
    (data) => {
      if (
        data.lettersDocumentsRequired === 'IFZA Letters & NOCs' &&
        data.purposeOfNOC === 'To buy/own Property'
      ) {
        return (
          data.developerName &&
          data.developerName.trim().length > 0 &&
          data.propertyUnitNumber &&
          data.propertyUnitNumber.trim().length > 0
        )
      }
      return true // No need to make it mandatory if the condition isn't met
    },
    {
      message:
        'Developer Name and Property Unit Number are required when "To buy/own Property" is selected.',
    }
  )

  .refine(
    (data) => {
      const condition =
        (data.lettersDocumentsRequired === 'IFZA Letters & NOCs' &&
          data.purposeOfNOC ===
            'RTA - To Buy/register car under the company') ||
        (data.lettersDocumentsRequired === 'IFZA Letters & NOCs' &&
          data.purposeOfNOC ===
            'RTA - To sell car registered under the company')

      if (condition) {
        return (
          data.carMake &&
          data.carMake.trim().length > 0 &&
          data.carModel &&
          data.carModel.trim().length > 0 &&
          data.yearOfManufacture &&
          data.yearOfManufacture.trim().length > 0 &&
          data.vinNumber &&
          data.vinNumber.trim().length > 0 &&
          data.carColor &&
          data.carColor.trim().length > 0 &&
          data.carEngineNumber &&
          data.carEngineNumber.trim().length > 0 &&
          data.countryOfOrigin &&
          data.countryOfOrigin.trim().length > 0
        )
      }
      return true // No need to make it mandatory if the condition isn't met
    },
    {
      message:
        'Car details are required when "RTA - To Buy/Register car" or "RTA - To sell car" is selected.',
    }
  )

  .refine(
    (data) => {
      if (
        data.lettersDocumentsRequired === 'IFZA Letters & NOCs' &&
        data.purposeOfNOC === 'Others'
      ) {
        return data.details && data.details.trim().length > 0
      }
      return true // No need to make it mandatory if the condition isn't met
    },
    {
      message:
        'Details are required when "Others" is selected for Purpose of NOC.',
    }
  )

  .refine(
    (data) => {
      if (data.lettersDocumentsRequired === 'Share Certificate') {
        return data.shareCapitalLetter && data.shareCapitalLetter !== ''
      }
      return true // No need to make it mandatory if the condition isn't met
    },
    {
      message:
        'Share Capital Letter is required when "Share Certificate" is selected.',
    }
  )

  .refine(
    (data) => {
      if (data.lettersDocumentsRequired === 'Share Certificate') {
        return (
          data.shareholdersCount && data.shareholdersCount.trim().length > 0
        )
      }
      return true // No need to make it mandatory if the condition isn't met
    },
    {
      message:
        'Shareholders Count is required when "Share Certificate" is selected.',
    }
  )

  .refine(
    (data) => {
      if (data.lettersDocumentsRequired === 'Share Certificate') {
        return data.numberOfShareholders && data.numberOfShareholders > 0
      }
      return true // No need to make it mandatory if the condition isn't met
    },
    {
      message:
        'Number of Shareholders is required when "Share Certificate" is selected.',
    }
  )

  .refine(
    (data) => {
      if (data.lettersDocumentsRequired === 'MOFA Attestation') {
        return data.agreeToRequirements === true
      }
      return true
    },
    {
      message: 'Select this option.',
      path: ['agreeToRequirements'],
    }
  )

  .refine(
    (data) => {
      if (data.lettersDocumentsRequired === 'MOFA Attestation') {
        return data.typeOfDocument && data.typeOfDocument.trim().length > 0
      }
      return true
    },
    {
      message:
        'Type of Document is required when "MOFA Attestation" is selected.',
    }
  )

  .refine(
    (data) => {
      if (data.lettersDocumentsRequired === 'MOFA Attestation') {
        return (
          data.documentIssueLocation &&
          data.documentIssueLocation.trim().length > 0
        )
      }
      return true
    },
    {
      message:
        'Document Issue Location is required when "MOFA Attestation" is selected.',
    }
  )

  .refine(
    (data) => {
      if (data.documentIssueLocation === 'Issued Outside UAE') {
        return (
          data.attestedByEmbassy && data.attestedByEmbassy.trim().length > 0
        )
      }
      return true
    },
    { message: 'Attested by the UAE Embassy is required.' }
  )

  .refine(
    (data) => {
      if (data.documentIssueLocation === 'Issued Inside UAE (IFZA Documents)') {
        return data.isMoaAoa && data.isMoaAoa.trim().length > 0
      }
      return true
    },
    { message: 'Is one of the to be attested documents a MOA/AOA is required.' }
  )

  .refine(
    (data) => {
      if (data.lettersDocumentsRequired === 'MOFA Attestation') {
        return data.numDocuments && data.numDocuments.trim().length > 0
      }
      return true
    },
    {
      message:
        'Number of documents to be attested is required when "MOFA Attestation" is selected.',
    }
  )
  .refine(
    (data) => {
      const requiredNums = ['1', '2', '3', '4', '5', '6', '7']
      if (data.numDocuments && requiredNums.includes(data.numDocuments)) {
        return (
          data.uploadCorporateDocuments1 instanceof File &&
          data.uploadCorporateDocuments1.size > 0
        )
      }
      return true
    },
    {
      message: 'Upload a file here.',
      path: ['uploadCorporateDocuments1'],
    }
  )
  .refine(
    (data) => {
      const requiredNums = ['2', '3', '4', '5', '6', '7']
      if (data.numDocuments && requiredNums.includes(data.numDocuments)) {
        return (
          data.uploadCorporateDocuments2 instanceof File &&
          data.uploadCorporateDocuments2.size > 0
        )
      }
      return true
    },
    {
      message: 'Upload a file here.',
      path: ['uploadCorporateDocuments2'],
    }
  )

  .refine(
    (data) => {
      const requiredNums = ['3', '4', '5', '6', '7']
      if (data.numDocuments && requiredNums.includes(data.numDocuments)) {
        return (
          data.uploadCorporateDocuments3 instanceof File &&
          data.uploadCorporateDocuments3.size > 0
        )
      }
      return true
    },
    {
      message: 'Upload a file here.',
      path: ['uploadCorporateDocuments3'],
    }
  )

  .refine(
    (data) => {
      const requiredNums = ['4', '5', '6', '7']
      if (data.numDocuments && requiredNums.includes(data.numDocuments)) {
        return (
          data.uploadCorporateDocuments4 instanceof File &&
          data.uploadCorporateDocuments4.size > 0
        )
      }
      return true
    },
    {
      message: 'Upload a file here.',
      path: ['uploadCorporateDocuments4'],
    }
  )

  .refine(
    (data) => {
      const requiredNums = ['5', '6', '7']
      if (data.numDocuments && requiredNums.includes(data.numDocuments)) {
        return (
          data.uploadCorporateDocuments5 instanceof File &&
          data.uploadCorporateDocuments5.size > 0
        )
      }
      return true
    },
    {
      message: 'Upload a file here.',
      path: ['uploadCorporateDocuments5'],
    }
  )

  .refine(
    (data) => {
      const requiredNums = ['6', '7']
      if (data.numDocuments && requiredNums.includes(data.numDocuments)) {
        return (
          data.uploadCorporateDocuments6 instanceof File &&
          data.uploadCorporateDocuments6.size > 0
        )
      }
      return true
    },
    {
      message: 'Upload a file here.',
      path: ['uploadCorporateDocuments6'],
    }
  )

  .refine(
    (data) => {
      const requiredNums = ['7']
      if (data.numDocuments && requiredNums.includes(data.numDocuments)) {
        return (
          data.uploadCorporateDocuments7 instanceof File &&
          data.uploadCorporateDocuments7.size > 0
        )
      }
      return true
    },
    {
      message: 'Upload a file here.',
      path: ['uploadCorporateDocuments7'],
    }
  )

type ApplicationFormValues = z.infer<typeof applicationFormSchema>
export default function ComponentBlockScreen() {
  const [isLoading, setIsLoading] = useState(false)
  useState<boolean>(false)
  const form = useForm<ApplicationFormValues>({
    resolver: zodResolver(applicationFormSchema),
    defaultValues: {
      lettersDocumentsRequired: '',
      letterAddressedTo: '',
      registryExtractIssued: '',
      registaryAddressedTo: '',
      servicetype: '',
      moaIssuedPreviously: '',
      languageOfLetter: '',
      serviceType: '',
      companyStampFile: '',
      attestedDocument: '',
      purposeOfNOC: '',
      developerName: '',
      propertyUnitNumber: '',
      carMake: '',
      carModel: '',
      yearOfManufacture: '',
      vinNumber: '',
      carColor: '',
      carEngineNumber: '',
      countryOfOrigin: '',
      details: '',
      shareCapitalLetter: '',
      shareholdersCount: '',
      registeredCompanyName: '',
      tradeLicenseNumber: '',
      numberOfShareholders: 1,
      preferredPaymentOption: '',
      title: '',
      firstName: '',
      lastName: '',
      phone: '',
      email: '',
      mobileNumber: '',
      shareholderAddresses: [],
      agreeToRequirements: false,

      typeOfDocument: '',
      documentIssueLocation: '',
      attestedByEmbassy: '',

      isMoaAoa: '',

      numDocuments: '',
      uploadCorporateDocuments1: undefined,
      uploadCorporateDocuments2: undefined,
      uploadCorporateDocuments3: undefined,
      uploadCorporateDocuments4: undefined,
      uploadCorporateDocuments5: undefined,
      uploadCorporateDocuments6: undefined,
      uploadCorporateDocuments7: undefined,

      typeOfCourierService: '',
    },
    shouldUnregister: true,
  })

  const handleReset = () => {
    form.reset()
  }

  const onSubmit = (data: ApplicationFormValues) => {
    // console.log('data', data)
    data.shareholderAddresses = rows
    const { feilds, formData } = mainpulateLicenseLetterRequestData(data)
    console.log('feilds', feilds)

    // setIsFormSubmitted(true)
    setIsLoading(true)
    createLicenseLetterRequest(
      feilds,
      (datas) => {
        createLicenseLetterRequestFiles(
          datas.data,
          formData,
          () => {
            setIsLoading(false)
            // Show success toast notification
            toast({
              title: 'Success!',
              description:
                'Your license letter request has been submitted successfully.',
              variant: 'success',
            })
          },
          (err) => {
            console.log('createLicenseLetterRequest')
            console.log('error', err)
            toast({
              title: 'Fail!',
              description:
                err?.response?.data?.message || 'Something went wrong.',
              variant: 'error',
            })
            setIsLoading(false)
          }
        )
        // setIsFormSubmitted(true)
      },
      (error) => {
        console.log('createLicenseLetterRequestFiles')

        console.log('error', error)
        toast({
          title: 'Fail!',
          description:
            error?.response?.data?.message || 'Something went wrong.',
          variant: 'error',
        })
        // setIsFormSubmitted(false)
        setIsLoading(false)
      }
    )
    // console.log("mainpulateData",mainpulateData)
    // Set loading state to true when submission starts

    // Make sure to include the current rows data in the submission

    // console.log('data', data)

    // Simulate API call with timeout
    // setTimeout(() => {
    //   setIsFormSubmitted(true)
    //   setIsLoading(false)
    //   // Show success toast notification
    //   toast({
    //     title: 'Success!',
    //     description:
    //       'Your license letter request has been submitted successfully.',
    //     variant: 'success',
    //   })
    // }, 1500)
  }

  // const onError = (errors: any) => {
  //   console.log('errors', errors)
  // }

  const [rows, setRows] = useState<RowData[]>([
    {
      id: 1,
      addressLine1: '',
      addressLine2: '',
      city: '',
      country: '',
    },
  ])

  function handleDelete(id: number) {
    const updatedRows = rows.filter((row) => row.id !== id)
    setRows(updatedRows)

    // Update form value when rows change
    form.setValue('shareholderAddresses', updatedRows)
  }

  function handleFieldChange(
    id: number,
    field: keyof Omit<RowData, 'id'>,
    value: string
  ) {
    setRows(
      rows.map((row) => (row.id === id ? { ...row, [field]: value } : row))
    )

    // Update form value when rows change
    form.setValue(
      'shareholderAddresses',
      rows.map((row) => ({
        id: row.id,
        addressLine1:
          field === 'addressLine1' && id === row.id ? value : row.addressLine1,
        addressLine2:
          field === 'addressLine2' && id === row.id ? value : row.addressLine2,
        city: field === 'city' && id === row.id ? value : row.city,
        country: field === 'country' && id === row.id ? value : row.country,
      })),
      { shouldValidate: true }
    )

    form.trigger(
      `shareholderAddresses.${rows.findIndex((r) => r.id === id)}.${field}`
    )
  }

  function handleAddRow() {
    const newId = rows.length ? Math.max(...rows.map((r) => r.id)) + 1 : 1
    const newRow: RowData = {
      id: newId,
      addressLine1: '',
      addressLine2: '',
      city: '',
      country: '',
    }
    const updatedRows = [...rows, newRow]
    setRows(updatedRows)

    // Update form value when rows change
    form.setValue('shareholderAddresses', updatedRows)
  }

  // Initialize shareholderAddresses in form when component mounts
  // useEffect(() => {
  //   form.setValue('shareholderAddresses', rows);
  // }, []);
  useEffect(() => {
    if (
      !form.watch('shareholdersCount') ||
      form.watch('shareholdersCount') === 'choose'
    ) {
      form.clearErrors('shareholderAddresses')
    }
  }, [form.watch('shareholdersCount')])

  const [openEmbassyDialog, setOpenEmbassyDialog] = useState(false)

  // This effect opens the Dialog when the user selects "No" for the attestedByEmbassy field
  useEffect(() => {
    if (form.watch('attestedByEmbassy') === 'No') {
      setOpenEmbassyDialog(true)
    }
  }, [form.watch('attestedByEmbassy')])

  // Function to handle when the Dialog is closed
  const handleDialogClose = (open: boolean) => {
    setOpenEmbassyDialog(open)
    if (!open) {
      form.setValue('attestedByEmbassy', '')
    }
  }

  const [openIndividualDocument, setOpenIndividualDocument] = useState(false)

  // Open individual document dialog if user selects that type
  useEffect(() => {
    if (form.watch('typeOfDocument') === 'Individual Document') {
      setOpenIndividualDocument(true)
    }
  }, [form.watch('typeOfDocument')])

  // Handle closing of individual document dialog
  const handleIndividualDocumentDialogClose = (open: boolean) => {
    setOpenIndividualDocument(open)
    if (!open) {
      form.setValue('typeOfDocument', '')
    }
  }

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        {/* <TopNav links={topNav} /> */}
        <div className='ml-auto flex items-center space-x-4'>
          {/* <Search /> */}
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-2 flex items-center justify-between space-y-2'>
          <h1 className='text-2xl font-bold tracking-tight lg:text-[28px]'>
            License Letter Request
          </h1>
          <div className='flex items-center space-x-2 lg:relative sm:fixed lg:w-auto sm:space-x-2 fixed bottom-0 right-0 p-3 bg-background w-full text-right flex justify-end z-10 sm:z-auto sm:bg-transparent sm:p-0'></div>
        </div>

        <Card className='mt-6'>
          <CardContent>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className='space-y-4 mb-4'
              >
                <CardHeader className='px-0 pt-0 pb-0'>
                  <CardTitle>Letter / Request Details</CardTitle>
                </CardHeader>
                {/* Letters/Documents Required Dropdown */}
                <FormField
                  control={form.control}
                  name='lettersDocumentsRequired'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Letters/Documents Required{' '}
                        <span className='text-red-500'>*</span>
                      </FormLabel>
                      <FormControl>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder='Choose Document' />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='Certificate of Incumbency'>
                              Certificate of Incumbency
                            </SelectItem>
                            <SelectItem value='Certificate of Name change'>
                              Certificate of Name change
                            </SelectItem>
                            <SelectItem value='Certified True Copy - MOA'>
                              Certified True Copy - MOA
                            </SelectItem>
                            <SelectItem value='Certified True Copy - Registry Extract'>
                              Certified True Copy - Registry Extract
                            </SelectItem>
                            <SelectItem value='Certified True Copy - Trade License'>
                              Certified True Copy - Trade License
                            </SelectItem>
                            <SelectItem value='Certified True Copy of Initial/Amendment Resolution'>
                              Certified True Copy of Initial/Amendment
                              Resolution
                            </SelectItem>
                            <SelectItem value='Certified True Copy - Certificate of Continuation'>
                              Certified True Copy - Certificate of Continuation
                            </SelectItem>
                            <SelectItem value='Certified True Copy - Certificate of Formation'>
                              Certified True Copy - Certificate of Formation
                            </SelectItem>
                            <SelectItem value='Company Stamp'>
                              Company Stamp
                            </SelectItem>
                            <SelectItem value='Copy of COF/COC'>
                              Copy of COF/COC
                            </SelectItem>
                            <SelectItem value='Copy of Original CONC (Certificate of Name Change)'>
                              Copy of Original CONC (Certificate of Name Change)
                            </SelectItem>
                            <SelectItem value='Copy of Trade License'>
                              Copy of Trade License
                            </SelectItem>
                            <SelectItem value='Copy of ULA'>
                              Copy of ULA
                            </SelectItem>
                            <SelectItem value='Establishment Card'>
                              Establishment Card
                            </SelectItem>
                            <SelectItem value='General Document Attestation'>
                              General Document Attestation
                            </SelectItem>
                            <SelectItem value='IFZA Letters & NOCs'>
                              IFZA Letters & NOCs
                            </SelectItem>
                            <SelectItem value='Letter of Good Standing'>
                              Letter of Good Standing
                            </SelectItem>
                            <SelectItem value='Memorandum & Articles of Association - Hard Copy'>
                              Memorandum & Articles of Association - Hard Copy
                            </SelectItem>
                            <SelectItem value='NOC - For Company member to be a Shareholder in another freezone/Mainland'>
                              NOC - For Company member to be a Shareholder in
                              another freezone/Mainland
                            </SelectItem>
                            <SelectItem value='NOC - For the IFZA company to be a Shareholder in another freezone/Mainland'>
                              NOC - For the IFZA company to be a Shareholder in
                              another freezone/Mainland
                            </SelectItem>
                            <SelectItem value='Registry Extract'>
                              Registry Extract
                            </SelectItem>

                            <SelectItem value='Set of Incorporation Documents'>
                              Set of Incorporation Documents
                            </SelectItem>
                            <SelectItem value='Share Certificate'>
                              Share Certificate
                            </SelectItem>
                            <SelectItem value='MOFA Attestation'>
                              MOFA Attestation
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>

                      <FormDescription>
                        Please select the letter/document that you are looking
                        for.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Additional message */}

                {(form.watch('lettersDocumentsRequired') ===
                  'Certified True Copy - Registry Extract' ||
                  form.watch('lettersDocumentsRequired') ===
                    'Certified True Copy - Trade License' ||
                  form.watch('lettersDocumentsRequired') ===
                    'Certified True Copy of Initial/Amendment Resolution') && (
                  <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
                    <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                      <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
                    </span>
                    <div className='flex space-y-2 flex-col ml-3'>
                      <AlertDescription>
                        <p className='text-sm text-slate-800 dark:text-slate-400'>
                          <span className='underline'>Disclaimer </span> :
                          Certified True copies issued are a digital copy with
                          QR code and Stamp, no hard copies will be provided.
                        </p>
                      </AlertDescription>
                    </div>
                  </Alert>
                )}

                {/* "The letter should be addressed to" field */}
                {(form.watch('lettersDocumentsRequired') ===
                  'Certificate of Name change' ||
                  form.watch('lettersDocumentsRequired') ===
                    'Certified True Copy - Certificate of Continuation' ||
                  form.watch('lettersDocumentsRequired') ===
                    'Certified True Copy - Certificate of Formation' ||
                  form.watch('lettersDocumentsRequired') ===
                    'Copy of COF/COC' ||
                  form.watch('lettersDocumentsRequired') ===
                    'Copy of Original CONC (Certificate of Name Change)' ||
                  form.watch('lettersDocumentsRequired') ===
                    'Copy of Trade License' ||
                  form.watch('lettersDocumentsRequired') === 'Copy of ULA' ||
                  form.watch('lettersDocumentsRequired') ===
                    'IFZA Letters & NOCs' ||
                  form.watch('lettersDocumentsRequired') ===
                    'Registry Extract' ||
                  form.watch('lettersDocumentsRequired') ===
                    'Set of Incorporation Documents' ||
                  form.watch('lettersDocumentsRequired') ===
                    'NOC - For Company member to be a Shareholder in another freezone/Mainland' ||
                  form.watch('lettersDocumentsRequired') ===
                    'NOC - For the IFZA company to be a Shareholder in another freezone/Mainland') && (
                  <>
                    <FormField
                      control={form.control}
                      name='letterAddressedTo'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            The letter should be addressed to ?{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Input {...field} placeholder='Please specify' />
                          </FormControl>

                          <FormDescription>
                            Please specify who the letter should be addressed
                            to. For example: Dubai Immigration, Bank Name, RTA.
                            Letters CANNOT be addressed to "To Whom It May
                            Concern".{' '}
                          </FormDescription>
                          <FormMessage>
                            {form.formState.errors.letterAddressedTo?.message}
                          </FormMessage>
                        </FormItem>
                      )}
                    />
                  </>
                )}

                {/* Is the Registry Extract issued for the company? */}
                {(form.watch('lettersDocumentsRequired') ===
                  'Certificate of Incumbency' ||
                  form.watch('lettersDocumentsRequired') ===
                    'Certified True Copy - Registry Extract' ||
                  form.watch('lettersDocumentsRequired') ===
                    'Letter of Good Standing') && (
                  <>
                    <FormField
                      control={form.control}
                      name='registryExtractIssued'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Is the Registry Extract document issued for this
                            company in the last 30 days ?{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <RadioGroup
                              className='flex align-center'
                              value={field.value}
                              onValueChange={field.onChange}
                            >
                              <RadioGroupItem
                                className='mt-1'
                                value='yes'
                                id='option1'
                              />
                              <label htmlFor='option1'>Yes</label>
                              <RadioGroupItem
                                className='mt-1'
                                value='no'
                                id='option2'
                              />
                              <label htmlFor='option2'>No</label>
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormDescription>
                      Please mention if the extract document was issued in the
                      last 30 days, if yes, we can proceed with the letter of
                      incumbency request, otherwise, you will have to apply for
                      the registery extract document as well.
                    </FormDescription>
                  </>
                )}

                {/* "Registry Extract should be addressed to?" field */}
                {((form.watch('lettersDocumentsRequired') ===
                  'Certificate of Incumbency' &&
                  form.watch('registryExtractIssued') === 'no') ||
                  (form.watch('lettersDocumentsRequired') ===
                    'Certified True Copy - Registry Extract' &&
                    form.watch('registryExtractIssued') === 'no') ||
                  (form.watch('lettersDocumentsRequired') ===
                    'Letter of Good Standing' &&
                    form.watch('registryExtractIssued') === 'no')) && (
                  <>
                    <FormField
                      control={form.control}
                      name='registaryAddressedTo'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Registry Extract should be addressed to ?{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Input {...field} placeholder='Please specify ' />
                          </FormControl>

                          <FormDescription>
                            Please specify who the letter should be addressed
                            to. For example: Dubai Immigration, Bank Name, RTA.
                            Letters CANNOT be addressed to "To Whom It May
                            Concern".
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}

                {/* "Service Type" dropdown  */}
                {((form.watch('lettersDocumentsRequired') ===
                  'Certificate of Incumbency' &&
                  form.watch('registryExtractIssued') === 'no') ||
                  (form.watch('lettersDocumentsRequired') ===
                    'Certified True Copy - Registry Extract' &&
                    form.watch('registryExtractIssued') === 'no') ||
                  form.watch('lettersDocumentsRequired') ===
                    'Registry Extract' ||
                  (form.watch('lettersDocumentsRequired') ===
                    'Letter of Good Standing' &&
                    form.watch('registryExtractIssued') === 'no') ||
                  form.watch('lettersDocumentsRequired') ===
                    'IFZA Letters & NOCs' ||
                  form.watch('lettersDocumentsRequired') ===
                    'Certified True Copy - MOA') && (
                  <>
                    <FormField
                      control={form.control}
                      name='servicetype'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Service Type <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder='Choose Service Type ' />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value='VIP'>VIP</SelectItem>
                                <SelectItem value='Standard'>
                                  Standard
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormDescription>
                            1. VIP: 1 - 2 Working Days 2. Standard: 3 - 5
                            Working Days
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}

                {/* Please confirm if MOA was issued previously  */}
                {form.watch('lettersDocumentsRequired') ===
                  'Certified True Copy - MOA' && (
                  <>
                    <FormField
                      control={form.control}
                      name='moaIssuedPreviously'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Please confirm if MOA was issued previously{' '}
                            <span style={{ color: 'red' }}>*</span>
                          </FormLabel>
                          <FormControl>
                            <RadioGroup
                              className='flex align-center'
                              value={field.value}
                              onValueChange={field.onChange}
                            >
                              <RadioGroupItem
                                className='mt-1'
                                value='yes'
                                id='option1'
                              />
                              <label htmlFor='option1'>Yes</label>
                              <RadioGroupItem
                                className='mt-1'
                                value='no'
                                id='option2'
                              />
                              <label htmlFor='option2'>No</label>
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}

                {(form.watch('lettersDocumentsRequired') ===
                  'Certificate of Incumbency' ||
                  form.watch('lettersDocumentsRequired') ===
                    'Letter of Good Standing') && (
                  <>
                    {/* Additional message */}
                    <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
                      <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                        <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
                      </span>
                      <div className='flex space-y-2 flex-col ml-3'>
                        <AlertDescription>
                          <p className='text-sm text-slate-800 dark:text-slate-400'>
                            In order to issue the Certificate of Incumbency, the
                            registry extract needs to be applied and paid for.
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>
                  </>
                )}

                {/*"Language of Lette" dropdown*/}
                {(form.watch('lettersDocumentsRequired') ===
                  'Letter of Good Standing' ||
                  form.watch('lettersDocumentsRequired') ===
                    'Certificate of Incumbency' ||
                  form.watch('lettersDocumentsRequired') ===
                    'Certified True Copy - Registry Extract' ||
                  form.watch('lettersDocumentsRequired') ===
                    'Registry Extract') && (
                  <>
                    <FormField
                      control={form.control}
                      name='languageOfLetter'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Language of Letter{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder='Select Preferred Language' />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value='Arabic'>Arabic</SelectItem>
                                <SelectItem value='English'>English</SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}

                {/* Show Service Type field when the user selects "Company Stamp" */}
                {form.watch('lettersDocumentsRequired') === 'Company Stamp' && (
                  <>
                    <FormField
                      control={form.control}
                      name='serviceType'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Service Type <span style={{ color: 'red' }}>*</span>
                          </FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder='Select ' />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value='regular'>Regular</SelectItem>
                                <SelectItem value='personalized'>
                                  Personalized
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormDescription>
                            Regular stamp AED120 with company name /
                            Personalized stamp AED240 with company name and logo
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}

                {/* Show Upload Company Stamp field when the user selects "Personalized 240 AED" */}
                {form.watch('lettersDocumentsRequired') === 'Company Stamp' &&
                  form.watch('serviceType') === 'personalized' && (
                    <>
                      <div className='mb-4'>
                        <FormField
                          control={form.control}
                          name='companyStampFile'
                          render={() => (
                            <FormItem>
                              <FormLabel>
                                Upload your company stamp{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <FileUploadField
                                  accept='.pdf,.jpg,.jpeg,.png'
                                  onchoose={(file) => {
                                    form.setValue(
                                      'companyStampFile',
                                      file || ''
                                    )
                                    form.clearErrors('companyStampFile')
                                  }}

                                  // Update value on file select
                                />
                              </FormControl>
                              <FormDescription>
                                Please upload your company logo in black and
                                white, PDF format
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </>
                  )}

                {/* Show file upload when "General Document Attestation" is selected */}
                {form.watch('lettersDocumentsRequired') ===
                  'General Document Attestation' && (
                  <>
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle>Note :</AlertTitle>
                        <AlertDescription>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Selecting this option allows attestation of
                            UBO/ULA/Bank Board Resolution/Other.
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>

                    <div className='mb-4'>
                      <FormField
                        control={form.control}
                        name='attestedDocument'
                        render={() => (
                          <FormItem>
                            <FormLabel>
                              Please upload the copy of the document to be
                              attested
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <FileUploadField
                                accept='.pdf,.jpg,.jpeg,.png'
                                onchoose={(file) => {
                                  form.setValue('attestedDocument', file || '')
                                  form.clearErrors('attestedDocument')
                                }}

                                // Update value on file select
                              />
                            </FormControl>
                            <FormDescription>
                              Please upload a copy of the document so we can
                              verify if it can be attested.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </>
                )}

                {/* Show "Purpose of the NOC" dropdown when "IFZA Letters & NOCs" is selected */}
                {form.watch('lettersDocumentsRequired') ===
                  'IFZA Letters & NOCs' && (
                  <>
                    <FormField
                      control={form.control}
                      name='purposeOfNOC'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Purpose of the NOC{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder='Choose Service Type ' />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value='To buy/own Property'>
                                  To buy/own Property
                                </SelectItem>
                                <SelectItem value='To sell Property'>
                                  To sell Property
                                </SelectItem>
                                <SelectItem value='RTA - To Buy/register car under the company'>
                                  RTA - To Buy/register car under the company
                                </SelectItem>
                                <SelectItem value='RTA - To sell car registered under the company'>
                                  RTA - To sell car registered under the company
                                </SelectItem>
                                <SelectItem value='Others'>Others</SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormDescription>
                            Please let us know what is the NOC used for so we
                            can issue the right letter.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}

                {/* Show "Name of Developer" and "Property/Unit Number" when "To buy/own Property" OR "To sell Property" is selected */}
                {form.watch('lettersDocumentsRequired') ===
                  'IFZA Letters & NOCs' &&
                  form.watch('purposeOfNOC') === 'To buy/own Property' && (
                    <>
                      <FormField
                        control={form.control}
                        name='developerName'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Name of Developer{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder='Please mention the complete Name of Developer'
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      {/* Property / Unit Number */}

                      <FormField
                        control={form.control}
                        name='propertyUnitNumber'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Property / Unit Number{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder='Please mention the Property/Unit Number'
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </>
                  )}

                {/* Show car details fields when "RTA - To Buy/ register car under the company" or "RTA - To sell car registered under the company" is selected */}
                {((form.watch('lettersDocumentsRequired') ===
                  'IFZA Letters & NOCs' &&
                  form.watch('purposeOfNOC') ===
                    'RTA - To Buy/register car under the company') ||
                  (form.watch('lettersDocumentsRequired') ===
                    'IFZA Letters & NOCs' &&
                    form.watch('purposeOfNOC') ===
                      'RTA - To sell car registered under the company')) && (
                  <>
                    <div className='mb-4 mt-4'>
                      <FormField
                        control={form.control}
                        name='carMake'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Car Make <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className='mb-4 mt-4'>
                      <FormField
                        control={form.control}
                        name='carModel'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Car Model <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className='mb-4 mt-4'>
                      <FormField
                        control={form.control}
                        name='yearOfManufacture'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Year of Manufacture{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className='mb-4 mt-4'>
                      <FormField
                        control={form.control}
                        name='vinNumber'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              VIN Number <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className='mb-4 mt-4'>
                      <FormField
                        control={form.control}
                        name='carColor'
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Car Color <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className='mb-4 mt-4'>
                      <FormLabel>
                        Car Engine Number{' '}
                        <span className='text-red-500'>*</span>
                      </FormLabel>
                      <FormField
                        control={form.control}
                        name='carEngineNumber'
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name='countryOfOrigin'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Country of Origin of Car Manufacturer{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <CountryDropdown
                              placeholder='Country'
                              defaultValue={field.value as string} 
                              onChange={(c) => field.onChange(c.alpha3)}
                            />
                          </FormControl>
                          <FormDescription>
                            E.g., Japan, America, and Korea etc.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}

                {/* Show "Details" text field when "Others" is selected */}
                {form.watch('lettersDocumentsRequired') ===
                  'IFZA Letters & NOCs' &&
                  form.watch('purposeOfNOC') === 'Others' && (
                    <>
                      <div className='mb-4 mt-4'>
                        <FormField
                          control={form.control}
                          name='details'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Details <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  placeholder='Please specify'
                                />
                              </FormControl>
                              <FormDescription>
                                Please provide us with details on what is
                                required for the NOC
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </>
                  )}

                {form.watch('lettersDocumentsRequired') ===
                  'Share Certificate' && (
                  <>
                    {/* Instructions Text */}
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertDescription>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            In order to issue a share certificate, you are
                            requested to submit.
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            A share capital from a Licensed Auditor in Dubai OR
                            a letter from the bank clearly stating that the
                            company's capital amount "xxx" is deposited in the
                            company's bank account.
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            The letter needs to be addressed to Dubai Silicon
                            Oasis and the bank name needs to be mentioned.
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>

                    {/* Upload Share Capital Letter */}
                    <div className='mb-4'>
                      <FormField
                        control={form.control}
                        name='shareCapitalLetter'
                        render={() => (
                          <FormItem>
                            <FormLabel>
                              Please Upload Share Capital Letter{' '}
                              <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl>
                              <FileUploadField
                                accept='.pdf,.jpg,.jpeg,.png'
                                onchoose={(file) => {
                                  form.setValue(
                                    'shareCapitalLetter',
                                    file || ''
                                  )
                                  form.clearErrors('shareCapitalLetter')
                                }}

                                // Update value on file select
                              />
                            </FormControl>
                            <FormDescription>
                              The letter is required in order to proceed with
                              the share capital request.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* How many shareholder(s) the company has ? */}
                    <FormField
                      control={form.control}
                      name='shareholdersCount'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            How many shareholder(s) the company has ?{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Select
                              {...field}
                              onValueChange={(value) => field.onChange(value)}
                              value={field.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder='Choose' />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value='1'>1</SelectItem>
                                <SelectItem value='2'>2</SelectItem>
                                <SelectItem value='3'>3</SelectItem>
                                <SelectItem value='4'>4</SelectItem>
                                <SelectItem value='5'>5</SelectItem>
                                <SelectItem value='6'>6</SelectItem>
                                <SelectItem value='7'>7</SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}
                {/* Address of each Shareholder(s)  */}
                {form.watch('shareholdersCount') &&
                  form.watch('shareholdersCount') !== 'choose' && (
                    <>
                      <CardTitle className='border-none'>
                        Address of each Shareholder(s) :
                      </CardTitle>

                      <div className='fz-input-table space-y-4'>
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead> </TableHead>
                              <TableHead>
                                Address Line 1{' '}
                                <span className='text-red-500'>*</span>
                              </TableHead>
                              <TableHead>Address Line 2</TableHead>
                              <TableHead>
                                City <span className='text-red-500'>*</span>
                              </TableHead>
                              <TableHead>
                                Country <span className='text-red-500'>*</span>
                              </TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {rows.map((row, index) => (
                              <TableRow key={row.id}>
                                <TableCell className='align-top'>
                                  <Button
                                    variant='ghost'
                                    onClick={() => handleDelete(row.id)}
                                    disabled={rows.length <= 1}
                                  >
                                    <TrashIcon className='w-4 h-4' />
                                  </Button>
                                </TableCell>

                                <TableCell>
                                  <Input
                                    value={row.addressLine1}
                                    onChange={(e) =>
                                      handleFieldChange(
                                        row.id,
                                        'addressLine1',
                                        e.target.value
                                      )
                                    }
                                    placeholder='Enter'
                                  />
                                  <div className='min-h-[20px]'>
                                    {form.watch('shareholdersCount') &&
                                      form.watch('shareholdersCount') !==
                                        'choose' &&
                                      form.formState.errors
                                        .shareholderAddresses?.[index]
                                        ?.addressLine1 && (
                                        <FormMessage>
                                          {
                                            form.formState.errors
                                              .shareholderAddresses?.[index]
                                              ?.addressLine1?.message
                                          }
                                        </FormMessage>
                                      )}
                                  </div>
                                </TableCell>

                                <TableCell>
                                  <Input
                                    value={row.addressLine2}
                                    onChange={(e) =>
                                      handleFieldChange(
                                        row.id,
                                        'addressLine2',
                                        e.target.value
                                      )
                                    }
                                    placeholder='Enter'
                                  />
                                  <div className='min-h-[20px]'></div>
                                </TableCell>
                                <TableCell>
                                  <Input
                                    value={row.city}
                                    onChange={(e) =>
                                      handleFieldChange(
                                        row.id,
                                        'city',
                                        e.target.value
                                      )
                                    }
                                    placeholder='Enter'
                                  />
                                  <div className='min-h-[20px]'>
                                    {form.watch('shareholdersCount') &&
                                      form.watch('shareholdersCount') !==
                                        'choose' &&
                                      form.formState.errors
                                        .shareholderAddresses?.[index]
                                        ?.city && (
                                        <FormMessage>
                                          {
                                            form.formState.errors
                                              .shareholderAddresses?.[index]
                                              ?.city?.message
                                          }
                                        </FormMessage>
                                      )}
                                  </div>
                                </TableCell>

                                <TableCell>
                                  <CountryDropdown
                                    placeholder='Country'
                                    defaultValue={row.country}
                                    onChange={(country) =>
                                      handleFieldChange(
                                        row.id,
                                        'country',
                                        country.alpha3
                                      )
                                    }
                                  />
                                  <div className='min-h-[20px]'>
                                    {form.watch('shareholdersCount') &&
                                      form.watch('shareholdersCount') !==
                                        'choose' &&
                                      form.formState.errors
                                        .shareholderAddresses?.[index]
                                        ?.country && (
                                        <FormMessage>
                                          {
                                            form.formState.errors
                                              .shareholderAddresses?.[index]
                                              ?.country?.message
                                          }
                                        </FormMessage>
                                      )}
                                  </div>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>

                        <Button
                          type='button'
                          variant='link'
                          className='mt-4 underline'
                          onClick={handleAddRow}
                        >
                          <PlusIcon className='w-4 h-4 mr-1' /> Add New
                        </Button>

                        <FormDescription className='mt-2'>
                          All shareholders should provide their complete address
                          information. Address Line 1, City and Country are
                          required fields.
                        </FormDescription>
                      </div>
                    </>
                  )}

                {/* Show "MOFA Attestation" when "MOFA Attestation" is selected */}
                {form.watch('lettersDocumentsRequired') ===
                  'MOFA Attestation' && (
                  <>
                    <div className='pb-4'>
                      <a
                        href='https://files.ifza.com/external/9aa71262e65739f6e5379abd00f3f7b6ab56c8e08ed7a11077699f4689b51fc3'
                        target='_blank'
                        className='text-blue-500 underline'
                      >
                        MOFA - Attestation Requirements and Price List
                      </a>
                    </div>
                    {/* We have read and agree to the requirements */}
                    <FormField
                      control={form.control}
                      name='agreeToRequirements'
                      render={({ field }) => (
                        <FormItem>
                          <div className='flex items-center'>
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={(checked: boolean) =>
                                  field.onChange(checked)
                                }
                              />
                            </FormControl>
                            <FormLabel className='ml-2'>
                              We have read and agree to the requirements
                            </FormLabel>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Type of Document */}
                    <FormField
                      control={form.control}
                      name='typeOfDocument'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Type of Document{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Select
                              {...field}
                              onValueChange={(value) => field.onChange(value)}
                              value={field.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder='Choose' />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value='Individual Document'>
                                  Individual Document
                                </SelectItem>
                                <SelectItem value='Commercial / Corporate Document'>
                                  Commercial / Corporate Document
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Dialog
                      open={openIndividualDocument}
                      onOpenChange={handleIndividualDocumentDialogClose}
                    >
                      <DialogContent className='text-center'>
                        <DialogHeader>
                          <DialogTitle className='text-red-500'>
                            Important Notice :
                          </DialogTitle>
                          <DialogDescription className='mt-4'>
                            Please get in touch with{' '}
                            <a
                              href='<EMAIL>'
                              className='text-blue-500'
                              rel='noopener noreferrer'
                              target='_blank'
                            >
                              <EMAIL>
                            </a>{' '}
                            to submit your request for individual document
                            attestation. This form is applicable for commercial
                            / corporate documents ONLY.
                          </DialogDescription>
                        </DialogHeader>
                      </DialogContent>
                    </Dialog>

                    {/* Document Issue Location */}
                    <FormField
                      control={form.control}
                      name='documentIssueLocation'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Document Issue Location{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Select
                              {...field}
                              onValueChange={(value) => field.onChange(value)}
                              value={field.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder='Choose' />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value='Issued Outside UAE'>
                                  Issued Outside UAE
                                </SelectItem>
                                <SelectItem value='Issued Inside UAE (IFZA Documents)'>
                                  Issued Inside UAE (IFZA Documents)
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Attested by the UAE Embassy ? */}
                    {form.watch('documentIssueLocation') ===
                      'Issued Outside UAE' && (
                      <>
                        <FormField
                          control={form.control}
                          name='attestedByEmbassy'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                {' '}
                                Attested by the UAE Embassy ?{' '}
                                <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Select
                                  {...field}
                                  onValueChange={(value) =>
                                    field.onChange(value)
                                  }
                                  value={field.value}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder='Choose' />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value='Yes'>Yes</SelectItem>
                                    <SelectItem value='No'>No</SelectItem>
                                  </SelectContent>
                                </Select>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </>
                    )}

                    <Dialog
                      open={openEmbassyDialog}
                      onOpenChange={handleDialogClose}
                    >
                      <DialogContent className='text-center'>
                        <DialogHeader>
                          <DialogTitle className='text-red-500'>
                            Important Notice :
                          </DialogTitle>
                          <DialogDescription className='mt-4'>
                            MOFA Attestation can’t be completed at this stage.
                            Please proceed with the attestation of the UAE
                            Embassy first.
                          </DialogDescription>
                        </DialogHeader>
                      </DialogContent>
                    </Dialog>

                    {/* Is one of the to be attested documents a MOA/AOA ? */}

                    {form.watch('documentIssueLocation') ===
                      'Issued Inside UAE (IFZA Documents)' && (
                      <>
                        <FormField
                          control={form.control}
                          name='isMoaAoa'
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                Is one of the to be attested documents a MOA/AOA
                                ? <span className='text-red-500'>*</span>
                              </FormLabel>
                              <FormControl>
                                <Select
                                  {...field}
                                  onValueChange={(value) =>
                                    field.onChange(value)
                                  }
                                  value={field.value}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder='Choose' />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value='Yes'>Yes</SelectItem>
                                    <SelectItem value='No'>No</SelectItem>
                                  </SelectContent>
                                </Select>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </>
                    )}

                    {/* Number of documents to be attested */}
                    <FormField
                      control={form.control}
                      name='numDocuments'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Number of documents to be attested{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Select
                              {...field}
                              onValueChange={(value) => field.onChange(value)}
                              value={field.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder='Choose' />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value='1'>1</SelectItem>
                                <SelectItem value='2'>2</SelectItem>
                                <SelectItem value='3'>3</SelectItem>
                                <SelectItem value='4'>4</SelectItem>
                                <SelectItem value='5'>5</SelectItem>
                                <SelectItem value='6'>6</SelectItem>
                                <SelectItem value='7'>7</SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* If "1" is selected*/}
                    {(form.watch('numDocuments') === '1' ||
                      form.watch('numDocuments') === '2' ||
                      form.watch('numDocuments') === '3' ||
                      form.watch('numDocuments') === '4' ||
                      form.watch('numDocuments') === '5' ||
                      form.watch('numDocuments') === '6' ||
                      form.watch('numDocuments') === '7') && (
                      <>
                        <div className='mb-4'>
                          <FormField
                            control={form.control}
                            name='uploadCorporateDocuments1'
                            render={() => (
                              <FormItem>
                                <FormLabel>
                                  1. Upload Corporate Documents{' '}
                                  <span className='text-red-500'>*</span>
                                </FormLabel>
                                <FormControl>
                                  <FileUploadField
                                    accept='.pdf,.jpg,.png'
                                    onchoose={(file) => {
                                      form.setValue(
                                        'uploadCorporateDocuments1',
                                        file || ''
                                      )
                                      form.clearErrors(
                                        'uploadCorporateDocuments1'
                                      )
                                    }}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </>
                    )}

                    {/* If "2" is selected*/}
                    {(form.watch('numDocuments') === '2' ||
                      form.watch('numDocuments') === '3' ||
                      form.watch('numDocuments') === '4' ||
                      form.watch('numDocuments') === '5' ||
                      form.watch('numDocuments') === '6' ||
                      form.watch('numDocuments') === '7') && (
                      <>
                        <div className='mb-4'>
                          <FormField
                            control={form.control}
                            name='uploadCorporateDocuments2'
                            render={() => (
                              <FormItem>
                                <FormLabel>
                                  2. Upload Corporate Documents{' '}
                                  <span className='text-red-500'>*</span>
                                </FormLabel>
                                <FormControl>
                                  <FileUploadField
                                    accept='.pdf,.jpg,.png'
                                    onchoose={(file) => {
                                      form.setValue(
                                        'uploadCorporateDocuments2',
                                        file || ''
                                      )
                                      form.clearErrors(
                                        'uploadCorporateDocuments2'
                                      )
                                    }}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </>
                    )}

                    {/* If "3" is selected*/}
                    {(form.watch('numDocuments') === '3' ||
                      form.watch('numDocuments') === '4' ||
                      form.watch('numDocuments') === '5' ||
                      form.watch('numDocuments') === '6' ||
                      form.watch('numDocuments') === '7') && (
                      <>
                        <div className='mb-4'>
                          <FormField
                            control={form.control}
                            name='uploadCorporateDocuments3'
                            render={() => (
                              <FormItem>
                                <FormLabel>
                                  3. Upload Corporate Documents{' '}
                                  <span className='text-red-500'>*</span>
                                </FormLabel>
                                <FormControl>
                                  <FileUploadField
                                    accept='.pdf,.jpg,.png'
                                    onchoose={(file) => {
                                      form.setValue(
                                        'uploadCorporateDocuments3',
                                        file || ''
                                      )
                                      form.clearErrors(
                                        'uploadCorporateDocuments3'
                                      )
                                    }}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </>
                    )}

                    {/* If "4" is selected*/}
                    {(form.watch('numDocuments') === '4' ||
                      form.watch('numDocuments') === '5' ||
                      form.watch('numDocuments') === '6' ||
                      form.watch('numDocuments') === '7') && (
                      <>
                        <div className='mb-4'>
                          <FormField
                            control={form.control}
                            name='uploadCorporateDocuments4'
                            render={() => (
                              <FormItem>
                                <FormLabel>
                                  4. Upload Corporate Documents{' '}
                                  <span className='text-red-500'>*</span>
                                </FormLabel>
                                <FormControl>
                                  <FileUploadField
                                    accept='.pdf,.jpg,.png'
                                    onchoose={(file) => {
                                      form.setValue(
                                        'uploadCorporateDocuments4',
                                        file || ''
                                      )
                                      form.clearErrors(
                                        'uploadCorporateDocuments4'
                                      )
                                    }}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </>
                    )}

                    {/* If "5" is selected*/}
                    {(form.watch('numDocuments') === '5' ||
                      form.watch('numDocuments') === '6' ||
                      form.watch('numDocuments') === '7') && (
                      <>
                        <div className='mb-4'>
                          <FormField
                            control={form.control}
                            name='uploadCorporateDocuments5'
                            render={() => (
                              <FormItem>
                                <FormLabel>
                                  5. Upload Corporate Documents{' '}
                                  <span className='text-red-500'>*</span>
                                </FormLabel>
                                <FormControl>
                                  <FileUploadField
                                    accept='.pdf,.jpg,.png'
                                    onchoose={(file) => {
                                      form.setValue(
                                        'uploadCorporateDocuments5',
                                        file || ''
                                      )
                                      form.clearErrors(
                                        'uploadCorporateDocuments5'
                                      )
                                    }}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </>
                    )}
                    {/* If "6" is selected*/}
                    {(form.watch('numDocuments') === '6' ||
                      form.watch('numDocuments') === '7') && (
                      <>
                        <div className='mb-4'>
                          <FormField
                            control={form.control}
                            name='uploadCorporateDocuments6'
                            render={() => (
                              <FormItem>
                                <FormLabel>
                                  6. Upload Corporate Documents{' '}
                                  <span className='text-red-500'>*</span>
                                </FormLabel>
                                <FormControl>
                                  <FileUploadField
                                    accept='.pdf,.jpg,.png'
                                    onchoose={(file) => {
                                      form.setValue(
                                        'uploadCorporateDocuments6',
                                        file || ''
                                      )
                                      form.clearErrors(
                                        'uploadCorporateDocuments6'
                                      )
                                    }}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </>
                    )}

                    {/* If "7" is selected*/}
                    {form.watch('numDocuments') === '7' && (
                      <>
                        <div className='mb-4'>
                          <FormField
                            control={form.control}
                            name='uploadCorporateDocuments7'
                            render={() => (
                              <FormItem>
                                <FormLabel>
                                  7. Upload Corporate Documents{' '}
                                  <span className='text-red-500'>*</span>
                                </FormLabel>
                                <FormControl>
                                  <FileUploadField
                                    accept='.pdf,.jpg,.png'
                                    onchoose={(file) => {
                                      form.setValue(
                                        'uploadCorporateDocuments7',
                                        file || ''
                                      )
                                      form.clearErrors(
                                        'uploadCorporateDocuments7'
                                      )
                                    }}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </>
                    )}

                    {/* Type of courier service required for MOFA attestation */}

                    <FormField
                      control={form.control}
                      name='typeOfCourierService'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Type of courier service required for MOFA
                            attestation
                          </FormLabel>
                          <FormControl>
                            <Select
                              {...field}
                              onValueChange={(value) => field.onChange(value)}
                              value={field.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder='Choose' />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value='Reqular'>Reqular</SelectItem>
                                <SelectItem value='Express'>Express</SelectItem>
                                <SelectItem value='Next Day Delivery'>
                                  Next Day Delivery
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Instructions Text */}
                    <Alert className='flex items-start border-dashed border-primary mt-2 alert-bg-warning dark:bg-primary/20'>
                      <span className='bg-primary/10 p-2 rounded inline-block dark:text-primary-dark'>
                        <FilePenLine className='w-4 h-4 stroke-primary dark:text-primary-dark' />
                      </span>
                      <div className='flex flex-col ml-2'>
                        <AlertTitle className=' text-red-500'>
                          Please make sure the original documents for
                          attestation are dropped off or sent via courier to us
                          :
                        </AlertTitle>

                        <AlertDescription>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            Drop off at Dubai Digital Park, Building A2, IFZA
                            Reception (Ground Floor) - Between 8:00 AM - 8:00 PM
                            (Monday- Friday) or 10:00 AM - 4:00 PM (Sunday),
                            alternatively the documents can be sent to the same
                            address.
                          </p>
                          <p className="text-sm text-slate-700 dark:text-slate-400 pl-4 relative before:content-['•'] before:absolute before:left-0 before:top-0">
                            In case documents are sent via courier, please
                            mention +971 50 473 4088 as well. This ensures we
                            can be contacted once the delivery is out.
                          </p>
                        </AlertDescription>
                      </div>
                    </Alert>
                  </>
                )}

                <CardHeader className='px-0 pt-4 pb-0'>
                  <CardTitle>Registered Company Details</CardTitle>
                </CardHeader>
                {/* Registered Company Name */}

                <FormField
                  control={form.control}
                  name='registeredCompanyName'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Registered Company Name{' '}
                        <span className='text-red-500'>*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder='Enter Registered Company Name'
                          {...field}
                        />
                      </FormControl>

                      <FormDescription>
                        Please mention the name exactly as per your Trade
                        License. If the name is not accurate it might cause
                        delays in issuance of the document.
                      </FormDescription>

                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Trade License Number */}

                <FormField
                  control={form.control}
                  name='tradeLicenseNumber'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Trade License Number{' '}
                        <span className='text-red-500'>*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder='Trade License Number Exactly as per your License'
                          {...field}
                        />
                      </FormControl>

                      <FormDescription>
                        Please mention Trade License Number exactly as per your
                        Trade License. For example, 1234 or 12345 if the Trade
                        License number is not accurate it might cause delays in
                        issuance of the document.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/*  No. of Shareholders */}

                {form.watch('lettersDocumentsRequired') ===
                  'Share Certificate' && (
                  <>
                    <FormField
                      control={form.control}
                      name='numberOfShareholders'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            No. of Shareholders{' '}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder='Enter '
                              onChange={(va) => {
                                field.onChange(Number(va.target.value))
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}

                {/* How would you like to make payment? */}
                <FormField
                  control={form.control}
                  name='preferredPaymentOption'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        How would you like to make payment ?{' '}
                        <span className='text-red-500'>*</span>
                      </FormLabel>
                      <FormControl>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder='Select Preferred Payment Option' />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='online'>
                              Online Payment
                            </SelectItem>
                            <SelectItem value='Bank Transfer'>
                              Bank Transfer
                            </SelectItem>
                            <SelectItem value='cheque'>Cheque</SelectItem>
                            <SelectItem value='cash'>Cash</SelectItem>
                            <SelectItem value='atm'>ATM</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <CardHeader className='px-0 pt-0 pb-0'>
                  <CardTitle>Contact Information</CardTitle>
                </CardHeader>
                <Alert className='flex items-start border-dashed border-blue-500 mt-2 bg-slate-50 dark:bg-primary/20'>
                  <span className='bg-blue-50 p-2 rounded inline-block dark:text-primary-dark'>
                    <Lightbulb className='w-4 h-4 stroke-blue-500 dark:text-primary-dark' />
                  </span>
                  <div className='flex space-y-2 flex-col ml-3'>
                    <AlertDescription>
                      <p className='text-sm text-slate-800 dark:text-slate-400'>
                        These are your contact details; we need this information
                        to get in touch with you and send you the communication
                        regarding this request.
                      </p>
                    </AlertDescription>
                  </div>
                </Alert>

                {/* Your Name  */}
                <div className='flex-1'>
                  <div className='flex items-center space-x-2'>
                    <div className='flex flex-col w-full'>
                      <FormLabel>
                        Your Name <span className='text-red-500'>*</span>
                      </FormLabel>
                      <div className='flex space-x-2 mt-4'>
                        <FormField
                          control={form.control}
                          name='title'
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                >
                                  <SelectTrigger className='w-[100px]'>
                                    <SelectValue placeholder='Title' />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value='Mr'>Mr.</SelectItem>
                                    <SelectItem value='Mrs'>Mrs.</SelectItem>
                                    <SelectItem value='Ms'>Ms.</SelectItem>
                                  </SelectContent>
                                </Select>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <div className='w-full'>
                          <FormField
                            control={form.control}
                            name='firstName'
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input {...field} placeholder='First Name' />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        <div className='w-full'>
                          <FormField
                            control={form.control}
                            name='lastName'
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input {...field} placeholder='Last Name' />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Phone Field */}

                <FormField
                  control={form.control}
                  name='phone'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone</FormLabel>
                      <PhoneInput
                        country={'ae'}
                        // value={field.value.toString()}
                        onChange={(phone) => field.onChange(phone)}
                        containerClass='fz-country-code flex h-9 w-full rounded-tl-[6px] rounded-bl-[6px] border border-input bg-transparent text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm'
                        inputClass=' flex  !h-9 !w-full rounded-tl-[6px] rounded-bl-[6px] border !border-transparent !bg-transparent px-3 text-base !shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm transform translate-y-[-2px]'
                        buttonClass='!h-100 !border-l-transparent !border-t-transparent !bg-slate-200 dark:!bg-slate-800 dark:!border-r-transparent !border-b-transparent !border-gray-200 !bg-transparent !rounded-tl-[8px] !rounded-bl-[6px]'
                        enableSearch={true}
                        searchPlaceholder='Search country...'
                      />
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/*Email */}

                <FormField
                  control={form.control}
                  name='email'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {' '}
                        Email <span className='text-red-500'>*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          // placeholder='Email Address'
                        />
                      </FormControl>
                      <FormDescription>
                        Please let us know your registered email address with
                        us. Please note all letters will be sent to registered
                        email addresses only.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Request Updates WhatsApp Number */}
                <FormField
                  control={form.control}
                  name='mobileNumber'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Request Updates Whatsapp Number</FormLabel>
                      <PhoneInput
                        country={'ae'}
                        value={field.value || ''}
                        onChange={(phone) => field.onChange(phone)}
                        containerClass='fz-country-code flex h-9 w-full rounded-tl-[6px] rounded-bl-[6px] border border-input bg-transparent text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm'
                        inputClass=' flex  !h-9 !w-full rounded-tl-[6px] rounded-bl-[6px] border !border-transparent !bg-transparent px-3 text-base !shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm transform translate-y-[-2px]'
                        buttonClass='!h-100 !border-l-transparent !border-t-transparent !bg-slate-200 dark:!bg-slate-800 dark:!border-r-transparent !border-b-transparent !border-gray-200 !bg-transparent !rounded-tl-[8px] !rounded-bl-[6px]'
                        enableSearch={true}
                        searchPlaceholder='Search country...'
                      />
                      <FormDescription>
                        Please provide your WhatsApp Mobile Number. Please note
                        all letters will be sent to this WhatsApp.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </form>

              <div className='fixed bottom-0 right-0 p-3 bg-background flex justify-end space-x-2 z-10 fz-form-btns'>
                <Button
                  variant={'btn_outline'}
                  onClick={handleReset}
                  disabled={isLoading}
                >
                  Reset
                </Button>
                <Button
                  variant={'default'}
                  onClick={form.handleSubmit(
                    (data) => {
                      onSubmit(data)
                    },
                    (errors) => {
                      console.log('Form Errors:', errors)
                    }
                  )}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      Loading
                      <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                    </>
                  ) : (
                    'Submit'
                  )}
                </Button>
              </div>
            </Form>
          </CardContent>
        </Card>
      </Main>
    </>
  )
}
